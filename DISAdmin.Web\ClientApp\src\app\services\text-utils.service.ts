import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class TextUtilsService {

  /**
   * Inteligentní zkrácení názvu metody pro zobrazení v grafu
   * @param text Text k zkrácení
   * @param maxLength Maximální délka včetně "..." (výchozí 30 znaků)
   * @returns Zkrácený text s "..." pokud je potřeba, celková délka nepřekročí maxLength
   */
  truncateMethodName(text: string, maxLength: number = 30): string {
    if (!text || text.length <= maxLength) {
      return text;
    }

    // Zkrátíme na maxLength - 3 znaků, aby se vešly "..."
    const targetLength = maxLength - 3;
    if (targetLength <= 0) {
      return '...';
    }

    // Pokusíme se zkrátit na posledním "." před limitem pro zachování čitelnosti
    const truncated = text.substring(0, targetLength);
    const lastDotIndex = truncated.lastIndexOf('.');

    // Pokud najdeme tečku a není příliš na začátku (alespoň 40% z cílové délky), zkrátíme na ní
    if (lastDotIndex > 0 && lastDotIndex >= targetLength * 0.4) {
      const result = text.substring(0, lastDotIndex) + '...';
      console.log(`TextUtils.truncateMethodName: "${text}" (${text.length}) -> "${result}" (${result.length})`);
      return result;
    }

    // Jinak zkrátíme na pevné délce
    const result = truncated + '...';
    console.log(`TextUtils.truncateMethodName: "${text}" (${text.length}) -> "${result}" (${result.length})`);
    return result;
  }

  /**
   * Vytvoří konfiguraci pro zkrácení popisků na ose X pro Chart.js
   * @param originalLabels Originální popisky
   * @param options Možnosti konfigurace
   * @returns Objekt s konfigurací pro Chart.js
   */
  createTruncatedLabelsConfig(originalLabels: string[], options: {
    enabled?: boolean;
    maxLength?: number;
    showFullInTooltip?: boolean;
  } = {}) {
    const config = {
      enabled: options.enabled ?? true,
      maxLength: options.maxLength ?? 30,
      showFullInTooltip: options.showFullInTooltip ?? true
    };

    if (!config.enabled) {
      return {};
    }

    const result: any = {
      scales: {
        x: {
          ticks: {
            callback: (_value: any, index: number) => {
              const label = originalLabels[index];
              const result = this.truncateMethodName(label, config.maxLength);
              console.log(`Chart.js callback: index=${index}, original="${label}", truncated="${result}"`);
              return result;
            },
            // Zakázat automatické přeskakování a zkracování Chart.js
            autoSkip: false,        // Nezobrazovat automaticky jen některé popisky
            maxRotation: 45,        // Fixní rotace 45°
            minRotation: 45,        // Fixní rotace 45°
            autoSkipPadding: 0      // Žádné automatické odsazení
          }
        }
      }
    };

    if (config.showFullInTooltip) {
      result.plugins = {
        tooltip: {
          callbacks: {
            title: (context: any) => {
              if (context && context.length > 0) {
                const dataIndex = context[0].dataIndex;
                const fullLabel = originalLabels[dataIndex];
                return fullLabel || context[0].label;
              }
              return context[0]?.label || '';
            }
          }
        }
      };
    }

    return result;
  }

  /**
   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu
   * @param baseConfig Základní konfigurace grafu
   * @param truncatedConfig Konfigurace pro zkrácené popisky
   * @returns Sloučená konfigurace
   */
  mergeChartConfig(baseConfig: any, truncatedConfig: any): any {
    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {
      return baseConfig;
    }

    const merged = { ...baseConfig };

    // Sloučení scales
    if (truncatedConfig.scales) {
      merged.scales = merged.scales || {};
      if (truncatedConfig.scales.x) {
        merged.scales.x = merged.scales.x || {};
        merged.scales.x.ticks = {
          ...merged.scales.x.ticks,
          ...truncatedConfig.scales.x.ticks
        };
      }
    }

    // Sloučení plugins
    if (truncatedConfig.plugins) {
      merged.plugins = merged.plugins || {};
      if (truncatedConfig.plugins.tooltip) {
        merged.plugins.tooltip = merged.plugins.tooltip || {};
        merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};
        
        // Sloučení tooltip callbacks - zachováme existující a přidáme nové
        Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {
          merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];
        });
      }
    }

    return merged;
  }
}
