import { Injectable } from '@angular/core';
import { Chart } from 'chart.js';
import { Subject } from 'rxjs';
import { TextUtilsService } from './text-utils.service';

declare global {
  interface Window {
    bootstrap: any;
  }
}

/**
 * Služba pro zobrazení grafů v modálním okně
 */
@Injectable({
  providedIn: 'root'
})
export class ChartModalService {
  // ID modálního okna
  private readonly modalId = 'chartFullscreenModal';

  // Instance modálního okna
  private modalInstance: any = null;

  // Subjekt pro předání dat grafu
  private chartDataSubject = new Subject<{
    chartInstance: Chart | null;
    title: string;
    config: any;
  }>();

  // Observable pro předání dat grafu
  public chartData$ = this.chartDataSubject.asObservable();

  constructor(private textUtils: TextUtilsService) { }



  /**
   * Detekuje, zda graf pou<PERSON><PERSON>v<PERSON> zkrácené popisky na ose X
   * @param chartInstance Instance grafu
   * @returns True pokud graf používá zkrácené popisky
   */
  private hasXAxisTruncation(chartInstance: Chart): boolean {
    const xAxis = chartInstance.options?.scales?.x;
    return !!(xAxis?.ticks?.callback);
  }

  /**
   * Získá originální labels z dat grafu pro tooltip
   * @param chartInstance Instance grafu
   * @returns Originální labels nebo null
   */
  private getOriginalLabels(chartInstance: Chart): string[] | null {
    // Zkusíme najít originální labels v data objektu
    const data = chartInstance.data as any;
    if (data.originalLabels && Array.isArray(data.originalLabels)) {
      return data.originalLabels;
    }

    // Fallback na běžné labels
    if (data.labels && Array.isArray(data.labels)) {
      return data.labels;
    }

    return null;
  }

  /**
   * Otevře modální okno s grafem
   * @param chartInstance Instance grafu, který chceme zobrazit
   * @param title Titulek grafu
   */
  openChartModal(chartInstance: Chart | null, title: string): void {
    if (!chartInstance) {
      console.error('Nelze zobrazit graf v modálním okně - instance grafu není k dispozici');
      return;
    }

    try {
      // Získáme typ grafu
      let chartType: any = 'line'; // Výchozí typ grafu

      // Pokusíme se získat typ grafu z instance
      if (chartInstance.config && typeof chartInstance.config === 'object') {
        // Pokud je config objekt, zkusíme získat typ
        if ('type' in chartInstance.config) {
          chartType = (chartInstance.config as any).type;
        }
      }

      // Získáme konfiguraci grafu
      const config = {
        type: chartType,
        data: JSON.parse(JSON.stringify(chartInstance.data)),
        options: JSON.parse(JSON.stringify(chartInstance.options))
      };

      // Detekujeme, zda původní graf používá zkrácené popisky na ose X
      const hasXTruncation = this.hasXAxisTruncation(chartInstance);
      const originalLabels = this.getOriginalLabels(chartInstance);

    // Upravíme konfiguraci pro modální okno
    if (config.options) {
      // Zajistíme responzivní zobrazení
      config.options.responsive = true;
      config.options.maintainAspectRatio = false;

      // Zvětšíme fonty pro lepší čitelnost
      if (config.options.plugins && config.options.plugins.title) {
        config.options.plugins.title.font = {
          ...config.options.plugins.title.font,
          size: 18
        };
      }

      if (config.options.scales) {
        // Zvětšíme fonty pro osy
        Object.keys(config.options.scales).forEach(axisKey => {
          const axis = config.options.scales[axisKey];
          if (axis && axis.ticks) {
            axis.ticks.font = {
              ...axis.ticks.font,
              size: 14
            };
          }
        });

        // Pokud původní graf používal zkrácené popisky na ose X, aplikujeme je i zde
        if (hasXTruncation && originalLabels && config.options.scales.x) {
          config.options.scales.x.ticks = config.options.scales.x.ticks || {};
          config.options.scales.x.ticks.callback = (_value: any, index: number) => {
            const label = originalLabels[index];
            return this.textUtils.truncateMethodName(label);
          };
        }
      }

      // Pokud původní graf používal zkrácené popisky, přidáme tooltip s plnými názvy
      if (hasXTruncation && originalLabels) {
        config.options.plugins = config.options.plugins || {};
        config.options.plugins.tooltip = config.options.plugins.tooltip || {};
        config.options.plugins.tooltip.callbacks = config.options.plugins.tooltip.callbacks || {};
        config.options.plugins.tooltip.callbacks.title = (context: any) => {
          if (context && context.length > 0) {
            const dataIndex = context[0].dataIndex;
            const fullLabel = originalLabels[dataIndex];
            return fullLabel || context[0].label;
          }
          return context[0]?.label || '';
        };
      }
    }

    // Předáme data grafu do subjektu
    this.chartDataSubject.next({
      chartInstance,
      title,
      config
    });

    // Otevřeme modální okno
    this.openModal();
    } catch (error) {
      console.error('Chyba při otevírání grafu v modálním okně:', error);
    }
  }

  /**
   * Otevře modální okno
   */
  private openModal(): void {
    const modalElement = document.getElementById(this.modalId);
    if (!modalElement) {
      console.error(`Modal element with ID ${this.modalId} not found`);
      return;
    }

    // Zkontrolujeme, zda již existuje instance modálního okna
    let existingModal = window.bootstrap.Modal.getInstance(modalElement);
    if (existingModal) {
      existingModal.dispose();
    }

    // Vytvoříme novou instanci modálního okna
    const modalOptions = {
      backdrop: true,
      keyboard: true
    };

    this.modalInstance = new window.bootstrap.Modal(modalElement, modalOptions);
    this.modalInstance.show();
  }

  /**
   * Zavře modální okno
   */
  closeModal(): void {
    if (this.modalInstance) {
      this.modalInstance.hide();
      this.modalInstance = null;
    }
  }
}
