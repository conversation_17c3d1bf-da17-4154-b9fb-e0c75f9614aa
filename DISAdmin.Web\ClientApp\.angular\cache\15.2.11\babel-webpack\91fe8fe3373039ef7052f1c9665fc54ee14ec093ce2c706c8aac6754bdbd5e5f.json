{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\n/**\r\n * Služba pro zobrazení grafů v modálním okně\r\n */\nexport class ChartModalService {\n  constructor() {\n    // ID modálního okna\n    this.modalId = 'chartFullscreenModal';\n    // Instance modálního okna\n    this.modalInstance = null;\n    // Subjekt pro předání dat grafu\n    this.chartDataSubject = new Subject();\n    // Observable pro předání dat grafu\n    this.chartData$ = this.chartDataSubject.asObservable();\n  }\n  /**\r\n   * Inteligentní zkrácení názvu metody pro zobrazení v grafu\r\n   * @param text Text k zkrácení\r\n   * @param maxLength Maximální délka (výchozí 30 znaků)\r\n   * @returns Zkrácený text s \"...\" pokud je potřeba\r\n   */\n  truncateMethodName(text, maxLength = 30) {\n    if (!text || text.length <= maxLength) {\n      return text;\n    }\n    // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n    const truncated = text.substring(0, maxLength - 3);\n    const lastDotIndex = truncated.lastIndexOf('.');\n    // Pokud najdeme tečku a není příliš na začátku, zkrátíme na ní\n    if (lastDotIndex > 0 && lastDotIndex > maxLength * 0.4) {\n      return text.substring(0, lastDotIndex) + '...';\n    }\n    // Jinak zkrátíme na pevné délce\n    return truncated + '...';\n  }\n  /**\r\n   * Detekuje, zda graf používá zkrácené popisky na ose X\r\n   * @param chartInstance Instance grafu\r\n   * @returns True pokud graf používá zkrácené popisky\r\n   */\n  hasXAxisTruncation(chartInstance) {\n    const xAxis = chartInstance.options?.scales?.x;\n    return !!xAxis?.ticks?.callback;\n  }\n  /**\r\n   * Získá originální labels z dat grafu pro tooltip\r\n   * @param chartInstance Instance grafu\r\n   * @returns Originální labels nebo null\r\n   */\n  getOriginalLabels(chartInstance) {\n    // Zkusíme najít originální labels v data objektu\n    const data = chartInstance.data;\n    if (data.originalLabels && Array.isArray(data.originalLabels)) {\n      return data.originalLabels;\n    }\n    // Fallback na běžné labels\n    if (data.labels && Array.isArray(data.labels)) {\n      return data.labels;\n    }\n    return null;\n  }\n  /**\r\n   * Otevře modální okno s grafem\r\n   * @param chartInstance Instance grafu, který chceme zobrazit\r\n   * @param title Titulek grafu\r\n   */\n  openChartModal(chartInstance, title) {\n    if (!chartInstance) {\n      console.error('Nelze zobrazit graf v modálním okně - instance grafu není k dispozici');\n      return;\n    }\n    try {\n      // Získáme typ grafu\n      let chartType = 'line'; // Výchozí typ grafu\n      // Pokusíme se získat typ grafu z instance\n      if (chartInstance.config && typeof chartInstance.config === 'object') {\n        // Pokud je config objekt, zkusíme získat typ\n        if ('type' in chartInstance.config) {\n          chartType = chartInstance.config.type;\n        }\n      }\n      // Získáme konfiguraci grafu\n      const config = {\n        type: chartType,\n        data: JSON.parse(JSON.stringify(chartInstance.data)),\n        options: JSON.parse(JSON.stringify(chartInstance.options))\n      };\n      // Upravíme konfiguraci pro modální okno\n      if (config.options) {\n        // Zajistíme responzivní zobrazení\n        config.options.responsive = true;\n        config.options.maintainAspectRatio = false;\n        // Zvětšíme fonty pro lepší čitelnost\n        if (config.options.plugins && config.options.plugins.title) {\n          config.options.plugins.title.font = {\n            ...config.options.plugins.title.font,\n            size: 18\n          };\n        }\n        if (config.options.scales) {\n          // Zvětšíme fonty pro osy\n          Object.keys(config.options.scales).forEach(axisKey => {\n            const axis = config.options.scales[axisKey];\n            if (axis && axis.ticks) {\n              axis.ticks.font = {\n                ...axis.ticks.font,\n                size: 14\n              };\n            }\n          });\n        }\n      }\n      // Předáme data grafu do subjektu\n      this.chartDataSubject.next({\n        chartInstance,\n        title,\n        config\n      });\n      // Otevřeme modální okno\n      this.openModal();\n    } catch (error) {\n      console.error('Chyba při otevírání grafu v modálním okně:', error);\n    }\n  }\n  /**\r\n   * Otevře modální okno\r\n   */\n  openModal() {\n    const modalElement = document.getElementById(this.modalId);\n    if (!modalElement) {\n      console.error(`Modal element with ID ${this.modalId} not found`);\n      return;\n    }\n    // Zkontrolujeme, zda již existuje instance modálního okna\n    let existingModal = window.bootstrap.Modal.getInstance(modalElement);\n    if (existingModal) {\n      existingModal.dispose();\n    }\n    // Vytvoříme novou instanci modálního okna\n    const modalOptions = {\n      backdrop: true,\n      keyboard: true\n    };\n    this.modalInstance = new window.bootstrap.Modal(modalElement, modalOptions);\n    this.modalInstance.show();\n  }\n  /**\r\n   * Zavře modální okno\r\n   */\n  closeModal() {\n    if (this.modalInstance) {\n      this.modalInstance.hide();\n      this.modalInstance = null;\n    }\n  }\n  static {\n    this.ɵfac = function ChartModalService_Factory(t) {\n      return new (t || ChartModalService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ChartModalService,\n      factory: ChartModalService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,OAAO,QAAQ,MAAM;;AAQ9B;;;AAMA,OAAM,MAAOC,iBAAiB;EAiB5BC;IAhBA;IACiB,YAAO,GAAG,sBAAsB;IAEjD;IACQ,kBAAa,GAAQ,IAAI;IAEjC;IACQ,qBAAgB,GAAG,IAAIF,OAAO,EAIlC;IAEJ;IACO,eAAU,GAAG,IAAI,CAACG,gBAAgB,CAACC,YAAY,EAAE;EAExC;EAEhB;;;;;;EAMQC,kBAAkB,CAACC,IAAY,EAAEC,YAAoB,EAAE;IAC7D,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE;MACrC,OAAOD,IAAI;;IAGb;IACA,MAAMG,SAAS,GAAGH,IAAI,CAACI,SAAS,CAAC,CAAC,EAAEH,SAAS,GAAG,CAAC,CAAC;IAClD,MAAMI,YAAY,GAAGF,SAAS,CAACG,WAAW,CAAC,GAAG,CAAC;IAE/C;IACA,IAAID,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAGJ,SAAS,GAAG,GAAG,EAAE;MACtD,OAAOD,IAAI,CAACI,SAAS,CAAC,CAAC,EAAEC,YAAY,CAAC,GAAG,KAAK;;IAGhD;IACA,OAAOF,SAAS,GAAG,KAAK;EAC1B;EAEA;;;;;EAKQI,kBAAkB,CAACC,aAAoB;IAC7C,MAAMC,KAAK,GAAGD,aAAa,CAACE,OAAO,EAAEC,MAAM,EAAEC,CAAC;IAC9C,OAAO,CAAC,CAAEH,KAAK,EAAEI,KAAK,EAAEC,QAAS;EACnC;EAEA;;;;;EAKQC,iBAAiB,CAACP,aAAoB;IAC5C;IACA,MAAMQ,IAAI,GAAGR,aAAa,CAACQ,IAAW;IACtC,IAAIA,IAAI,CAACC,cAAc,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAACC,cAAc,CAAC,EAAE;MAC7D,OAAOD,IAAI,CAACC,cAAc;;IAG5B;IACA,IAAID,IAAI,CAACI,MAAM,IAAIF,KAAK,CAACC,OAAO,CAACH,IAAI,CAACI,MAAM,CAAC,EAAE;MAC7C,OAAOJ,IAAI,CAACI,MAAM;;IAGpB,OAAO,IAAI;EACb;EAEA;;;;;EAKAC,cAAc,CAACb,aAA2B,EAAEc,KAAa;IACvD,IAAI,CAACd,aAAa,EAAE;MAClBe,OAAO,CAACC,KAAK,CAAC,uEAAuE,CAAC;MACtF;;IAGF,IAAI;MACF;MACA,IAAIC,SAAS,GAAQ,MAAM,CAAC,CAAC;MAE7B;MACA,IAAIjB,aAAa,CAACkB,MAAM,IAAI,OAAOlB,aAAa,CAACkB,MAAM,KAAK,QAAQ,EAAE;QACpE;QACA,IAAI,MAAM,IAAIlB,aAAa,CAACkB,MAAM,EAAE;UAClCD,SAAS,GAAIjB,aAAa,CAACkB,MAAc,CAACC,IAAI;;;MAIlD;MACA,MAAMD,MAAM,GAAG;QACbC,IAAI,EAAEF,SAAS;QACfT,IAAI,EAAEY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACtB,aAAa,CAACQ,IAAI,CAAC,CAAC;QACpDN,OAAO,EAAEkB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACtB,aAAa,CAACE,OAAO,CAAC;OAC1D;MAEH;MACA,IAAIgB,MAAM,CAAChB,OAAO,EAAE;QAClB;QACAgB,MAAM,CAAChB,OAAO,CAACqB,UAAU,GAAG,IAAI;QAChCL,MAAM,CAAChB,OAAO,CAACsB,mBAAmB,GAAG,KAAK;QAE1C;QACA,IAAIN,MAAM,CAAChB,OAAO,CAACuB,OAAO,IAAIP,MAAM,CAAChB,OAAO,CAACuB,OAAO,CAACX,KAAK,EAAE;UAC1DI,MAAM,CAAChB,OAAO,CAACuB,OAAO,CAACX,KAAK,CAACY,IAAI,GAAG;YAClC,GAAGR,MAAM,CAAChB,OAAO,CAACuB,OAAO,CAACX,KAAK,CAACY,IAAI;YACpCC,IAAI,EAAE;WACP;;QAGH,IAAIT,MAAM,CAAChB,OAAO,CAACC,MAAM,EAAE;UACzB;UACAyB,MAAM,CAACC,IAAI,CAACX,MAAM,CAAChB,OAAO,CAACC,MAAM,CAAC,CAAC2B,OAAO,CAACC,OAAO,IAAG;YACnD,MAAMC,IAAI,GAAGd,MAAM,CAAChB,OAAO,CAACC,MAAM,CAAC4B,OAAO,CAAC;YAC3C,IAAIC,IAAI,IAAIA,IAAI,CAAC3B,KAAK,EAAE;cACtB2B,IAAI,CAAC3B,KAAK,CAACqB,IAAI,GAAG;gBAChB,GAAGM,IAAI,CAAC3B,KAAK,CAACqB,IAAI;gBAClBC,IAAI,EAAE;eACP;;UAEL,CAAC,CAAC;;;MAIN;MACA,IAAI,CAACtC,gBAAgB,CAAC4C,IAAI,CAAC;QACzBjC,aAAa;QACbc,KAAK;QACLI;OACD,CAAC;MAEF;MACA,IAAI,CAACgB,SAAS,EAAE;KACf,CAAC,OAAOlB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;;EAEtE;EAEA;;;EAGQkB,SAAS;IACf,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACC,OAAO,CAAC;IAC1D,IAAI,CAACH,YAAY,EAAE;MACjBpB,OAAO,CAACC,KAAK,CAAC,yBAAyB,IAAI,CAACsB,OAAO,YAAY,CAAC;MAChE;;IAGF;IACA,IAAIC,aAAa,GAAGC,MAAM,CAACC,SAAS,CAACC,KAAK,CAACC,WAAW,CAACR,YAAY,CAAC;IACpE,IAAII,aAAa,EAAE;MACjBA,aAAa,CAACK,OAAO,EAAE;;IAGzB;IACA,MAAMC,YAAY,GAAG;MACnBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX;IAED,IAAI,CAACC,aAAa,GAAG,IAAIR,MAAM,CAACC,SAAS,CAACC,KAAK,CAACP,YAAY,EAAEU,YAAY,CAAC;IAC3E,IAAI,CAACG,aAAa,CAACC,IAAI,EAAE;EAC3B;EAEA;;;EAGAC,UAAU;IACR,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACG,IAAI,EAAE;MACzB,IAAI,CAACH,aAAa,GAAG,IAAI;;EAE7B;;;uBAnLW7D,iBAAiB;IAAA;EAAA;;;aAAjBA,iBAAiB;MAAAiE,SAAjBjE,iBAAiB;MAAAkE,YAFhB;IAAM;EAAA", "names": ["Subject", "ChartModalService", "constructor", "chartDataSubject", "asObservable", "truncateMethodName", "text", "max<PERSON><PERSON><PERSON>", "length", "truncated", "substring", "lastDotIndex", "lastIndexOf", "hasXAxisTruncation", "chartInstance", "xAxis", "options", "scales", "x", "ticks", "callback", "getOriginalLabels", "data", "originalLabels", "Array", "isArray", "labels", "openChartModal", "title", "console", "error", "chartType", "config", "type", "JSON", "parse", "stringify", "responsive", "maintainAspectRatio", "plugins", "font", "size", "Object", "keys", "for<PERSON>ach", "axisKey", "axis", "next", "openModal", "modalElement", "document", "getElementById", "modalId", "existingModal", "window", "bootstrap", "Modal", "getInstance", "dispose", "modalOptions", "backdrop", "keyboard", "modalInstance", "show", "closeModal", "hide", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\services\\chart-modal.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Chart } from 'chart.js';\nimport { Subject } from 'rxjs';\n\ndeclare global {\n  interface Window {\n    bootstrap: any;\n  }\n}\n\n/**\n * Služba pro zobrazení grafů v modálním okně\n */\n@Injectable({\n  providedIn: 'root'\n})\nexport class ChartModalService {\n  // ID modálního okna\n  private readonly modalId = 'chartFullscreenModal';\n\n  // Instance modálního okna\n  private modalInstance: any = null;\n\n  // Subjekt pro předání dat grafu\n  private chartDataSubject = new Subject<{\n    chartInstance: Chart | null;\n    title: string;\n    config: any;\n  }>();\n\n  // Observable pro předání dat grafu\n  public chartData$ = this.chartDataSubject.asObservable();\n\n  constructor() { }\n\n  /**\n   * Inteligentní zkrácení názvu metody pro zobrazení v grafu\n   * @param text Text k zkrácení\n   * @param maxLength Maximální délka (výchozí 30 znaků)\n   * @returns Zkrácený text s \"...\" pokud je potřeba\n   */\n  private truncateMethodName(text: string, maxLength: number = 30): string {\n    if (!text || text.length <= maxLength) {\n      return text;\n    }\n\n    // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n    const truncated = text.substring(0, maxLength - 3);\n    const lastDotIndex = truncated.lastIndexOf('.');\n\n    // Pokud najdeme tečku a není příliš na začátku, zkrátíme na ní\n    if (lastDotIndex > 0 && lastDotIndex > maxLength * 0.4) {\n      return text.substring(0, lastDotIndex) + '...';\n    }\n\n    // Jinak zkrátíme na pevné délce\n    return truncated + '...';\n  }\n\n  /**\n   * Detekuje, zda graf používá zkrácené popisky na ose X\n   * @param chartInstance Instance grafu\n   * @returns True pokud graf používá zkrácené popisky\n   */\n  private hasXAxisTruncation(chartInstance: Chart): boolean {\n    const xAxis = chartInstance.options?.scales?.x;\n    return !!(xAxis?.ticks?.callback);\n  }\n\n  /**\n   * Získá originální labels z dat grafu pro tooltip\n   * @param chartInstance Instance grafu\n   * @returns Originální labels nebo null\n   */\n  private getOriginalLabels(chartInstance: Chart): string[] | null {\n    // Zkusíme najít originální labels v data objektu\n    const data = chartInstance.data as any;\n    if (data.originalLabels && Array.isArray(data.originalLabels)) {\n      return data.originalLabels;\n    }\n\n    // Fallback na běžné labels\n    if (data.labels && Array.isArray(data.labels)) {\n      return data.labels;\n    }\n\n    return null;\n  }\n\n  /**\n   * Otevře modální okno s grafem\n   * @param chartInstance Instance grafu, který chceme zobrazit\n   * @param title Titulek grafu\n   */\n  openChartModal(chartInstance: Chart | null, title: string): void {\n    if (!chartInstance) {\n      console.error('Nelze zobrazit graf v modálním okně - instance grafu není k dispozici');\n      return;\n    }\n\n    try {\n      // Získáme typ grafu\n      let chartType: any = 'line'; // Výchozí typ grafu\n\n      // Pokusíme se získat typ grafu z instance\n      if (chartInstance.config && typeof chartInstance.config === 'object') {\n        // Pokud je config objekt, zkusíme získat typ\n        if ('type' in chartInstance.config) {\n          chartType = (chartInstance.config as any).type;\n        }\n      }\n\n      // Získáme konfiguraci grafu\n      const config = {\n        type: chartType,\n        data: JSON.parse(JSON.stringify(chartInstance.data)),\n        options: JSON.parse(JSON.stringify(chartInstance.options))\n      };\n\n    // Upravíme konfiguraci pro modální okno\n    if (config.options) {\n      // Zajistíme responzivní zobrazení\n      config.options.responsive = true;\n      config.options.maintainAspectRatio = false;\n\n      // Zvětšíme fonty pro lepší čitelnost\n      if (config.options.plugins && config.options.plugins.title) {\n        config.options.plugins.title.font = {\n          ...config.options.plugins.title.font,\n          size: 18\n        };\n      }\n\n      if (config.options.scales) {\n        // Zvětšíme fonty pro osy\n        Object.keys(config.options.scales).forEach(axisKey => {\n          const axis = config.options.scales[axisKey];\n          if (axis && axis.ticks) {\n            axis.ticks.font = {\n              ...axis.ticks.font,\n              size: 14\n            };\n          }\n        });\n      }\n    }\n\n    // Předáme data grafu do subjektu\n    this.chartDataSubject.next({\n      chartInstance,\n      title,\n      config\n    });\n\n    // Otevřeme modální okno\n    this.openModal();\n    } catch (error) {\n      console.error('Chyba při otevírání grafu v modálním okně:', error);\n    }\n  }\n\n  /**\n   * Otevře modální okno\n   */\n  private openModal(): void {\n    const modalElement = document.getElementById(this.modalId);\n    if (!modalElement) {\n      console.error(`Modal element with ID ${this.modalId} not found`);\n      return;\n    }\n\n    // Zkontrolujeme, zda již existuje instance modálního okna\n    let existingModal = window.bootstrap.Modal.getInstance(modalElement);\n    if (existingModal) {\n      existingModal.dispose();\n    }\n\n    // Vytvoříme novou instanci modálního okna\n    const modalOptions = {\n      backdrop: true,\n      keyboard: true\n    };\n\n    this.modalInstance = new window.bootstrap.Modal(modalElement, modalOptions);\n    this.modalInstance.show();\n  }\n\n  /**\n   * Zavře modální okno\n   */\n  closeModal(): void {\n    if (this.modalInstance) {\n      this.modalInstance.hide();\n      this.modalInstance = null;\n    }\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}