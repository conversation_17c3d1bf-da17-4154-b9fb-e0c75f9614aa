{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let TextUtilsService = /*#__PURE__*/(() => {\n  class TextUtilsService {\n    /**\r\n     * Inteligentní zkrácení názvu metody pro zobrazení v grafu\r\n     * @param text Text k zkrácení\r\n     * @param maxLength Maximální délka včetně \"...\" (výchozí 30 znaků)\r\n     * @returns Zkrácený text s \"...\" pokud je potřeba, celková délka nepřekročí maxLength\r\n     */\n    truncateMethodName(text, maxLength = 30) {\n      if (!text || text.length <= maxLength) {\n        return text;\n      }\n      // Zkrátíme na maxLength - 3 znaků, aby se vešly \"...\"\n      const targetLength = maxLength - 3;\n      if (targetLength <= 0) {\n        return '...';\n      }\n      // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n      const truncated = text.substring(0, targetLength);\n      const lastDotIndex = truncated.lastIndexOf('.');\n      // Pokud najdeme tečku a není příliš na začátku (alespoň 40% z cílové délky), zkrátíme na ní\n      if (lastDotIndex > 0 && lastDotIndex >= targetLength * 0.4) {\n        const result = text.substring(0, lastDotIndex) + '...';\n        console.log(`TextUtils.truncateMethodName: \"${text}\" (${text.length}) -> \"${result}\" (${result.length})`);\n        return result;\n      }\n      // Jinak zkrátíme na pevné délce\n      const result = truncated + '...';\n      console.log(`TextUtils.truncateMethodName: \"${text}\" (${text.length}) -> \"${result}\" (${result.length})`);\n      return result;\n    }\n    /**\r\n     * Vytvoří konfiguraci pro zkrácení popisků na ose X pro Chart.js\r\n     * @param originalLabels Originální popisky\r\n     * @param options Možnosti konfigurace\r\n     * @returns Objekt s konfigurací pro Chart.js\r\n     */\n    createTruncatedLabelsConfig(originalLabels, options = {}) {\n      const config = {\n        enabled: options.enabled ?? true,\n        maxLength: options.maxLength ?? 30,\n        showFullInTooltip: options.showFullInTooltip ?? true\n      };\n      if (!config.enabled) {\n        return {};\n      }\n      const result = {\n        scales: {\n          x: {\n            ticks: {\n              callback: (_value, index) => {\n                const label = originalLabels[index];\n                const result = this.truncateMethodName(label, config.maxLength);\n                console.log(`Chart.js callback: index=${index}, original=\"${label}\", truncated=\"${result}\"`);\n                return result;\n              },\n              // Zakázat automatické přeskakování a zkracování Chart.js\n              autoSkip: false,\n              maxRotation: 45,\n              minRotation: 45,\n              autoSkipPadding: 0 // Žádné automatické odsazení\n            }\n          }\n        }\n      };\n\n      if (config.showFullInTooltip) {\n        result.plugins = {\n          tooltip: {\n            callbacks: {\n              title: context => {\n                if (context && context.length > 0) {\n                  const dataIndex = context[0].dataIndex;\n                  const fullLabel = originalLabels[dataIndex];\n                  return fullLabel || context[0].label;\n                }\n                return context[0]?.label || '';\n              }\n            }\n          }\n        };\n      }\n      return result;\n    }\n    /**\r\n     * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu\r\n     * @param baseConfig Základní konfigurace grafu\r\n     * @param truncatedConfig Konfigurace pro zkrácené popisky\r\n     * @returns Sloučená konfigurace\r\n     */\n    mergeChartConfig(baseConfig, truncatedConfig) {\n      if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {\n        return baseConfig;\n      }\n      const merged = {\n        ...baseConfig\n      };\n      // Sloučení scales\n      if (truncatedConfig.scales) {\n        merged.scales = merged.scales || {};\n        if (truncatedConfig.scales.x) {\n          merged.scales.x = merged.scales.x || {};\n          merged.scales.x.ticks = {\n            ...merged.scales.x.ticks,\n            ...truncatedConfig.scales.x.ticks\n          };\n        }\n      }\n      // Sloučení plugins\n      if (truncatedConfig.plugins) {\n        merged.plugins = merged.plugins || {};\n        if (truncatedConfig.plugins.tooltip) {\n          merged.plugins.tooltip = merged.plugins.tooltip || {};\n          merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};\n          // Sloučení tooltip callbacks - zachováme existující a přidáme nové\n          Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {\n            merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];\n          });\n        }\n      }\n      return merged;\n    }\n    static {\n      this.ɵfac = function TextUtilsService_Factory(t) {\n        return new (t || TextUtilsService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TextUtilsService,\n        factory: TextUtilsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TextUtilsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}