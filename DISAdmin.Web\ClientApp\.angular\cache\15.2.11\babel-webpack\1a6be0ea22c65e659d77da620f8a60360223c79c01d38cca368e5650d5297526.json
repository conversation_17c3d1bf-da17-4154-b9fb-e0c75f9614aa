{"ast": null, "code": "import { Chart, registerables } from 'chart.js';\nimport { moveItemInArray } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/chart.service\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/alert.service\";\nimport * as i4 from \"../services/dashboard-config.service\";\nimport * as i5 from \"../services/chart-modal.service\";\nimport * as i6 from \"../services/monitoring.service\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/cdk/drag-drop\";\nconst _c0 = [\"apiCallsChart\"];\nconst _c1 = [\"apiPerformanceChart\"];\nconst _c2 = [\"instancesUsageChart\"];\nconst _c3 = [\"productionPerformanceChart\"];\nconst _c4 = [\"securityEventsChart\"];\nfunction DashboardComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.resetDashboardConfig());\n    });\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵelementStart(2, \"span\", 7);\n    i0.ɵɵtext(3, \"Resetovat\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"i\", 20);\n    i0.ɵɵtext(2, \" Nyn\\u00ED m\\u016F\\u017Eete p\\u0159et\\u00E1hnout widgety a zm\\u011Bnit jejich po\\u0159ad\\u00ED. Kliknut\\u00EDm na tla\\u010D\\u00EDtko \\\"Dokon\\u010Dit \\u00FApravy\\\" ulo\\u017E\\u00EDte zm\\u011Bny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"span\", 23);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DashboardComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction DashboardComponent_div_28_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 28);\n    i0.ɵɵtext(4, \"DISApi vol\\u00E1n\\u00ED v \\u010Dase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_28_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const _r12 = i0.ɵɵreference(10);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.openFullscreenChart(_r12, \"DISApi vol\\u00E1n\\u00ED v \\u010Dase\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_28_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r4.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r4.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.editMode);\n  }\n}\nfunction DashboardComponent_div_29_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 37);\n    i0.ɵɵtext(4, \"Odezva DIS metod\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_29_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const _r16 = i0.ɵɵreference(10);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.openFullscreenChart(_r16, \"Odezva DIS metod\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_29_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r5.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r5.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.editMode);\n  }\n}\nfunction DashboardComponent_div_30_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 39);\n    i0.ɵɵtext(4, \"Vol\\u00E1n\\u00ED DIS metod podle instanc\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_30_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const _r20 = i0.ɵɵreference(10);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.openFullscreenChart(_r20, \"Vol\\u00E1n\\u00ED DIS metod podle instanc\\u00ED\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_30_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r6.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r6.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.editMode);\n  }\n}\nfunction DashboardComponent_div_31_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 41);\n    i0.ɵɵtext(4, \"Pr\\u016Fm\\u011Brn\\u00E1 odezva a 95. percentil produk\\u010Dn\\u00EDch instanc\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const _r24 = i0.ɵɵreference(10);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.openFullscreenChart(_r24, \"Pr\\u016Fm\\u011Brn\\u00E1 odezva a 95. percentil produk\\u010Dn\\u00EDch instanc\\u00ED\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_31_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r7.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r7.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.editMode);\n  }\n}\nfunction DashboardComponent_div_32_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 43);\n    i0.ɵɵtext(4, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_32_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const _r28 = i0.ɵɵreference(10);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.openFullscreenChart(_r28, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_32_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r8.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r8.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.editMode);\n  }\n}\nfunction DashboardComponent_div_33_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.toggleWidgetVisibility(\"system-info\"));\n    });\n    i0.ɵɵelement(2, \"i\", 62);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_33_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_div_16_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.navigateToSecurityEvents());\n    });\n    i0.ɵɵelementStart(2, \"div\", 53)(3, \"h3\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 55);\n    i0.ɵɵtext(6, \"Nevy\\u0159e\\u0161en\\u00FDch bezp. ud\\u00E1lost\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r33.dashboardSummary.securityEventsCount || 0);\n  }\n}\nfunction DashboardComponent_div_33_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"div\", 47)(3, \"div\", 27)(4, \"h5\", 48);\n    i0.ɵɵtext(5, \"Syst\\u00E9mov\\u00E9 informace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, DashboardComponent_div_33_div_6_Template, 3, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 50)(9, \"div\", 51)(10, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.navigateToAlerts());\n    });\n    i0.ɵɵelementStart(11, \"div\", 53)(12, \"h3\", 54);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 55);\n    i0.ɵɵtext(15, \"Aktivn\\u00ED upozorn\\u011Bn\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(16, DashboardComponent_div_33_div_16_Template, 7, 1, \"div\", 56);\n    i0.ɵɵelementStart(17, \"div\", 51)(18, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.navigateToMonitoring());\n    });\n    i0.ɵɵelementStart(19, \"div\", 53)(20, \"h3\", 54);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 55);\n    i0.ɵɵtext(23, \"Dostupnost DIS API\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 51)(25, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_Template_div_click_25_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.navigateToPerformance());\n    });\n    i0.ɵɵelementStart(26, \"div\", 53)(27, \"h3\", 54);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\", 55);\n    i0.ɵɵtext(30, \"Pr\\u016Fm\\u011Brn\\u00E1 odezva DIS metod\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 51)(32, \"div\", 59);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_Template_div_click_32_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.navigateToCertificates());\n    });\n    i0.ɵɵelementStart(33, \"div\", 53)(34, \"h3\", 54);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 55);\n    i0.ɵɵtext(37, \"Expiruj\\u00EDc\\u00ED certifik\\u00E1ty\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(38, DashboardComponent_div_33_div_38_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r9.editMode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r9.editMode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.editMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r9.dashboardSummary.alertsCount || 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isAdmin);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.systemStatistics.ApiAvailability || 100, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.systemStatistics.AvgApiResponseTime || 0, \"ms\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r9.dashboardSummary.expiringCertificatesCount || 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.editMode);\n  }\n}\nconst _c5 = function (a0, a1) {\n  return {\n    \"bi-pencil-square\": a0,\n    \"bi-check-lg\": a1\n  };\n};\n// Registrace všech komponent Chart.js\nChart.register(...registerables);\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    constructor(chartService, authService, alertService, dashboardConfigService, chartModalService, monitoringService, router) {\n      this.chartService = chartService;\n      this.authService = authService;\n      this.alertService = alertService;\n      this.dashboardConfigService = dashboardConfigService;\n      this.chartModalService = chartModalService;\n      this.monitoringService = monitoringService;\n      this.router = router;\n      this.loading = true;\n      this.error = null;\n      this.isAdmin = false;\n      this.alerts = [];\n      this.systemStatistics = {};\n      this.dashboardSummary = {};\n      // Grafy\n      this.apiCallsChart = null;\n      this.apiPerformanceChart = null;\n      this.instancesUsageChart = null;\n      this.productionPerformanceChart = null;\n      this.securityEventsChart = null;\n      // Filtry\n      this.selectedDays = 30;\n      this.selectedInstanceId = null;\n      // Dashboard konfigurace\n      this.dashboardConfig = null;\n      this.widgets = [];\n      this.editMode = false;\n      // Aktualizace dat\n      this.updateSubscription = null;\n      // Event listener pro zavírání popoverů\n      this.documentClickListener = null;\n      this.authService.currentUser.subscribe(user => {\n        this.currentUser = user;\n        this.isAdmin = user?.isAdmin || false;\n      });\n      // Načtení uložené hodnoty počtu dní z local storage\n      const savedDateRange = localStorage.getItem('monitoring_dateRange');\n      if (savedDateRange) {\n        this.selectedDays = parseInt(savedDateRange, 10);\n      }\n    }\n    ngOnInit() {\n      // Načtení konfigurace dashboardu\n      this.loadDashboardConfig();\n      this.loadAlerts();\n      this.loadSystemStatistics();\n      this.loadDashboardSummary();\n    }\n    /**\r\n     * Načte konfiguraci dashboardu pro aktuálního uživatele\r\n     */\n    loadDashboardConfig() {\n      this.dashboardConfigService.getUserDashboardConfig().subscribe({\n        next: config => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n        },\n        error: error => {\n          console.error('Chyba při načítání konfigurace dashboardu', error);\n          // Použijeme výchozí konfiguraci\n          this.dashboardConfigService.resetDashboardConfig().subscribe(config => {\n            this.dashboardConfig = config;\n            this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n          });\n        }\n      });\n    }\n    ngAfterViewInit() {\n      // Inicializace grafů po načtení view\n      setTimeout(() => {\n        this.initCharts();\n        // Inicializace popoverů s delším zpožděním, aby se zajistilo, že DOM je plně načten\n        setTimeout(() => {\n          this.initPopovers();\n        }, 300);\n      }, 500);\n    }\n    ngOnDestroy() {\n      // Zrušení subscription při zničení komponenty\n      if (this.updateSubscription) {\n        this.updateSubscription.unsubscribe();\n      }\n      // Odstranění globálního event listeneru\n      if (this.documentClickListener) {\n        document.removeEventListener('click', this.documentClickListener);\n        this.documentClickListener = null;\n      }\n      // Zničení všech popoverů\n      this.destroyPopovers();\n      // Zničení grafů\n      this.destroyCharts();\n    }\n    /**\r\n     * Načtení upozornění\r\n     */\n    loadAlerts() {\n      this.alertService.getDashboardAlerts().subscribe({\n        next: data => {\n          this.alerts = data.filter(alert => !alert.isResolved);\n        },\n        error: err => {\n          console.error('Chyba při načítání upozornění', err);\n          this.error = 'Nepodařilo se načíst upozornění';\n        }\n      });\n    }\n    /**\r\n     * Načtení systémových statistik\r\n     */\n    loadSystemStatistics() {\n      this.monitoringService.getSystemStatistics().subscribe({\n        next: data => {\n          this.systemStatistics = data;\n        },\n        error: err => {\n          console.error('Chyba při načítání systémových statistik', err);\n          this.error = 'Nepodařilo se načíst systémové statistiky';\n        }\n      });\n    }\n    /**\r\n     * Načtení dashboard summary dat\r\n     */\n    loadDashboardSummary() {\n      this.monitoringService.getDashboardSummary().subscribe({\n        next: data => {\n          this.dashboardSummary = data;\n        },\n        error: err => {\n          console.error('Chyba při načítání dashboard summary', err);\n          this.error = 'Nepodařilo se načíst dashboard summary';\n        }\n      });\n    }\n    /**\r\n     * Inicializace všech grafů\r\n     */\n    initCharts() {\n      this.loading = true;\n      // Načtení dat pro grafy\n      this.loadApiCallsChart();\n      this.loadApiPerformanceChart();\n      this.loadInstancesUsageChart();\n      this.loadProductionPerformanceChart();\n      if (this.isAdmin) {\n        this.loadSecurityEventsChart();\n      }\n      this.loading = false;\n    }\n    /**\r\n     * Aktualizace všech grafů\r\n     */\n    refreshCharts() {\n      this.loadApiCallsChart();\n      this.loadApiPerformanceChart();\n      this.loadInstancesUsageChart();\n      this.loadProductionPerformanceChart();\n      if (this.isAdmin) {\n        this.loadSecurityEventsChart();\n      }\n      this.loadAlerts();\n      this.loadSystemStatistics();\n      this.loadDashboardSummary();\n      // Reinicializace popoverů po aktualizaci dat\n      setTimeout(() => {\n        this.initPopovers();\n      }, 100);\n    }\n    /**\r\n     * Zničení všech grafů\r\n     */\n    destroyCharts() {\n      if (this.apiCallsChart) {\n        this.apiCallsChart.destroy();\n        this.apiCallsChart = null;\n      }\n      if (this.apiPerformanceChart) {\n        this.apiPerformanceChart.destroy();\n        this.apiPerformanceChart = null;\n      }\n      if (this.instancesUsageChart) {\n        this.instancesUsageChart.destroy();\n        this.instancesUsageChart = null;\n      }\n      if (this.securityEventsChart) {\n        this.securityEventsChart.destroy();\n        this.securityEventsChart = null;\n      }\n    }\n    /**\r\n     * Načtení grafu API volání\r\n     */\n    loadApiCallsChart() {\n      this.chartService.getApiCallsChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n        next: data => {\n          if (this.apiCallsChart) {\n            this.apiCallsChart.data.labels = data.labels;\n            this.apiCallsChart.data.datasets[0].data = data.data;\n            this.apiCallsChart.update();\n          } else if (this.apiCallsChartRef) {\n            this.apiCallsChart = new Chart(this.apiCallsChartRef.nativeElement, {\n              type: 'line',\n              data: {\n                labels: data.labels,\n                datasets: [{\n                  label: 'Počet DISApi volání',\n                  data: data.data,\n                  backgroundColor: 'rgba(54, 162, 235, 0.2)',\n                  borderColor: 'rgba(54, 162, 235, 1)',\n                  borderWidth: 2,\n                  tension: 0.3,\n                  fill: true\n                }]\n              },\n              options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: {\n                  title: {\n                    display: true,\n                    text: data.title,\n                    font: {\n                      size: 16\n                    }\n                  },\n                  legend: {\n                    display: true,\n                    position: 'top'\n                  },\n                  tooltip: {\n                    mode: 'index',\n                    intersect: false\n                  }\n                },\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    title: {\n                      display: true,\n                      text: 'Počet volání'\n                    }\n                  },\n                  x: {\n                    title: {\n                      display: true,\n                      text: 'Datum'\n                    }\n                  }\n                }\n              }\n            });\n          }\n        },\n        error: err => {\n          console.error('Chyba při načítání dat pro graf API volání', err);\n          this.error = 'Nepodařilo se načíst data pro graf API volání';\n        }\n      });\n    }\n    /**\r\n     * Načtení grafu výkonu API\r\n     */\n    loadApiPerformanceChart() {\n      this.chartService.getApiPerformanceChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n        next: data => {\n          if (this.apiPerformanceChart) {\n            this.apiPerformanceChart.data.labels = data.labels;\n            this.apiPerformanceChart.data.datasets[0].data = data.avgData;\n            this.apiPerformanceChart.data.datasets[1].data = data.maxData;\n            this.apiPerformanceChart.update();\n          } else if (this.apiPerformanceChartRef) {\n            // Uložení originálních labels pro tooltip\n            const originalLabels = [...data.labels];\n            // Konfigurace pro zkrácené popisky na ose X\n            const truncatedLabelsConfig = this.createTruncatedLabelsConfig(originalLabels, {\n              enabled: true,\n              maxLength: 30,\n              showFullInTooltip: true // Zobrazit plný název v tooltip\n            });\n            // Základní konfigurace grafu\n            const baseOptions = {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Doba odezvy (ms)'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Endpoint'\n                  }\n                }\n              }\n            };\n            // Sloučení s konfigurací pro zkrácené popisky\n            const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n            this.apiPerformanceChart = new Chart(this.apiPerformanceChartRef.nativeElement, {\n              type: 'bar',\n              data: {\n                labels: data.labels,\n                datasets: [{\n                  label: 'Průměrná doba odezvy (ms)',\n                  data: data.avgData,\n                  backgroundColor: 'rgba(54, 162, 235, 0.5)',\n                  borderColor: 'rgba(54, 162, 235, 1)',\n                  borderWidth: 1\n                }, {\n                  label: 'Maximální doba odezvy (ms)',\n                  data: data.maxData,\n                  backgroundColor: 'rgba(255, 99, 132, 0.5)',\n                  borderColor: 'rgba(255, 99, 132, 1)',\n                  borderWidth: 1\n                }]\n              },\n              options: finalOptions\n            });\n          }\n        },\n        error: err => {\n          console.error('Chyba při načítání dat pro graf výkonu API', err);\n          this.error = 'Nepodařilo se načíst data pro graf výkonu API';\n        }\n      });\n    }\n    /**\r\n     * Načtení grafu využití instancí\r\n     */\n    loadInstancesUsageChart() {\n      this.chartService.getInstancesUsageChartData(this.selectedDays).subscribe({\n        next: data => {\n          if (this.instancesUsageChart) {\n            this.instancesUsageChart.data.labels = data.labels;\n            this.instancesUsageChart.data.datasets[0].data = data.data;\n            this.instancesUsageChart.update();\n          } else if (this.instancesUsageChartRef) {\n            this.instancesUsageChart = new Chart(this.instancesUsageChartRef.nativeElement, {\n              type: 'bar',\n              data: {\n                labels: data.labels,\n                datasets: [{\n                  label: 'Počet volání DIS metod',\n                  data: data.data,\n                  backgroundColor: ['rgba(255, 99, 132, 0.5)', 'rgba(54, 162, 235, 0.5)', 'rgba(255, 206, 86, 0.5)', 'rgba(75, 192, 192, 0.5)', 'rgba(153, 102, 255, 0.5)', 'rgba(255, 159, 64, 0.5)', 'rgba(199, 199, 199, 0.5)', 'rgba(83, 102, 255, 0.5)', 'rgba(255, 99, 255, 0.5)', 'rgba(99, 255, 132, 0.5)'],\n                  borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)', 'rgba(199, 199, 199, 1)', 'rgba(83, 102, 255, 1)', 'rgba(255, 99, 255, 1)', 'rgba(99, 255, 132, 1)'],\n                  borderWidth: 1\n                }]\n              },\n              options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: {\n                  title: {\n                    display: true,\n                    text: data.title,\n                    font: {\n                      size: 16\n                    }\n                  },\n                  legend: {\n                    display: false\n                  }\n                },\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    title: {\n                      display: true,\n                      text: 'Počet volání'\n                    }\n                  },\n                  x: {\n                    title: {\n                      display: true,\n                      text: 'Instance'\n                    }\n                  }\n                }\n              }\n            });\n          }\n        },\n        error: err => {\n          console.error('Chyba při načítání dat pro graf využití instancí', err);\n          this.error = 'Nepodařilo se načíst data pro graf využití instancí';\n        }\n      });\n    }\n    /**\r\n     * Načtení grafu výkonu produkčních instancí\r\n     */\n    loadProductionPerformanceChart() {\n      this.chartService.getProductionPerformanceChartData(this.selectedDays).subscribe({\n        next: data => {\n          if (this.productionPerformanceChart) {\n            this.productionPerformanceChart.data.labels = data.labels;\n            this.productionPerformanceChart.data.datasets = data.datasets;\n            this.productionPerformanceChart.update();\n          } else if (this.productionPerformanceChartRef) {\n            this.productionPerformanceChart = new Chart(this.productionPerformanceChartRef.nativeElement, {\n              type: 'line',\n              data: {\n                labels: data.labels,\n                datasets: data.datasets\n              },\n              options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: {\n                  title: {\n                    display: true,\n                    text: data.title,\n                    font: {\n                      size: 16\n                    }\n                  },\n                  legend: {\n                    display: true,\n                    position: 'top'\n                  },\n                  tooltip: {\n                    mode: 'index',\n                    intersect: false\n                  }\n                },\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    title: {\n                      display: true,\n                      text: 'Doba odezvy (ms)'\n                    }\n                  },\n                  x: {\n                    title: {\n                      display: true,\n                      text: 'Datum'\n                    }\n                  }\n                }\n              }\n            });\n          }\n        },\n        error: err => {\n          console.error('Chyba při načítání dat pro graf výkonu produkčních instancí', err);\n          this.error = 'Nepodařilo se načíst data pro graf výkonu produkčních instancí';\n        }\n      });\n    }\n    /**\r\n     * Načtení grafu bezpečnostních událostí\r\n     */\n    loadSecurityEventsChart() {\n      this.chartService.getSecurityEventsChartData(this.selectedDays).subscribe({\n        next: data => {\n          if (this.securityEventsChart) {\n            this.securityEventsChart.data.labels = data.labels;\n            this.securityEventsChart.data.datasets = data.datasets;\n            this.securityEventsChart.update();\n          } else if (this.securityEventsChartRef) {\n            this.securityEventsChart = new Chart(this.securityEventsChartRef.nativeElement, {\n              type: 'line',\n              data: {\n                labels: data.labels,\n                datasets: data.datasets\n              },\n              options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: {\n                  title: {\n                    display: true,\n                    text: data.title,\n                    font: {\n                      size: 16\n                    }\n                  },\n                  legend: {\n                    display: true,\n                    position: 'top'\n                  },\n                  tooltip: {\n                    mode: 'index',\n                    intersect: false\n                  }\n                },\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    title: {\n                      display: true,\n                      text: 'Počet událostí'\n                    }\n                  },\n                  x: {\n                    title: {\n                      display: true,\n                      text: 'Datum'\n                    }\n                  }\n                }\n              }\n            });\n          }\n        },\n        error: err => {\n          console.error('Chyba při načítání dat pro graf bezpečnostních událostí', err);\n          this.error = 'Nepodařilo se načíst data pro graf bezpečnostních událostí';\n        }\n      });\n    }\n    /**\r\n     * Změna filtru dnů\r\n     */\n    onDaysChange(days) {\n      this.selectedDays = days;\n      // Uložení vybraného počtu dní do local storage\n      localStorage.setItem('monitoring_dateRange', days.toString());\n      this.refreshCharts();\n    }\n    /**\r\n     * Změna filtru instance\r\n     */\n    onInstanceChange(instanceId) {\n      this.selectedInstanceId = instanceId;\n      this.refreshCharts();\n    }\n    /**\r\n     * Získání textu pro typ alertu\r\n     */\n    getAlertTypeText(alertType) {\n      switch (alertType) {\n        case 'SecurityBreach':\n          return 'Bezpečnostní incident';\n        case 'CertificateExpiration':\n          return 'Expirace certifikátu';\n        case 'FailedConnectionAttempts':\n          return 'Selhané připojení';\n        case 'SuspiciousActivity':\n          return 'Podezřelá aktivita';\n        default:\n          return alertType;\n      }\n    }\n    /**\r\n     * Přepnutí režimu úprav dashboardu\r\n     */\n    toggleEditMode() {\n      this.editMode = !this.editMode;\n    }\n    /**\r\n     * Zpracování přetažení widgetu\r\n     */\n    onDrop(event) {\n      if (event.previousIndex === event.currentIndex) {\n        return;\n      }\n      // Získáme všechny viditelné widgety v pořadí, jak jsou zobrazeny v DOM\n      const visibleWidgets = Array.from(document.querySelectorAll('.col-lg-6[cdkDrag]')).map(element => {\n        // Získáme ID widgetu z atributu *ngIf\n        const widget = this.widgets.find(w => {\n          const widgetId = w.id;\n          return element.innerHTML.includes(`isWidgetVisible('${widgetId}')`);\n        });\n        return widget;\n      }).filter(widget => widget !== undefined);\n      // Provedeme přesun v poli viditelných widgetů\n      moveItemInArray(visibleWidgets, event.previousIndex, event.currentIndex);\n      // Aktualizujeme pozice všech widgetů\n      let position = 0;\n      // Nejprve aktualizujeme pozice viditelných widgetů\n      visibleWidgets.forEach(visibleWidget => {\n        const widget = this.widgets.find(w => w.id === visibleWidget.id);\n        if (widget) {\n          widget.position = position++;\n        }\n      });\n      // Poté aktualizujeme pozice skrytých widgetů\n      this.widgets.filter(widget => !visibleWidgets.some(vw => vw.id === widget.id)).forEach(widget => {\n        widget.position = position++;\n      });\n      // Seřadíme widgety podle pozice\n      this.widgets.sort((a, b) => a.position - b.position);\n      // Uložení konfigurace\n      this.saveDashboardConfig();\n    }\n    /**\r\n     * Změna viditelnosti widgetu\r\n     */\n    toggleWidgetVisibility(widgetId) {\n      const widget = this.widgets.find(w => w.id === widgetId);\n      if (widget) {\n        widget.visible = !widget.visible;\n        this.saveDashboardConfig();\n      }\n    }\n    /**\r\n     * Kontrola viditelnosti widgetu\r\n     */\n    isWidgetVisible(widgetId) {\n      const widget = this.widgets.find(w => w.id === widgetId);\n      return widget ? widget.visible : true;\n    }\n    /**\r\n     * Uložení konfigurace dashboardu\r\n     */\n    saveDashboardConfig() {\n      if (!this.dashboardConfig) {\n        return;\n      }\n      this.dashboardConfig.widgets = [...this.widgets];\n      this.dashboardConfig.lastModified = new Date();\n      this.dashboardConfigService.saveDashboardConfig(this.dashboardConfig).subscribe({\n        next: config => {\n          console.log('Konfigurace dashboardu byla úspěšně uložena');\n        },\n        error: error => {\n          console.error('Chyba při ukládání konfigurace dashboardu', error);\n        }\n      });\n    }\n    /**\r\n     * Reset konfigurace dashboardu na výchozí hodnoty\r\n     */\n    resetDashboardConfig() {\n      if (confirm('Opravdu chcete resetovat dashboard na výchozí nastavení?')) {\n        this.dashboardConfigService.resetDashboardConfig().subscribe({\n          next: config => {\n            this.dashboardConfig = config;\n            this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n            this.editMode = false;\n            this.refreshCharts();\n          },\n          error: error => {\n            console.error('Chyba při resetování konfigurace dashboardu', error);\n          }\n        });\n      }\n    }\n    /**\r\n     * Otevře modální okno s grafem v režimu \"full screen\"\r\n     * @param chart Instance grafu nebo reference na canvas element\r\n     * @param title Titulek grafu\r\n     */\n    openFullscreenChart(chart, title) {\n      // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart\n      if (chart instanceof HTMLCanvasElement) {\n        // Najdeme instanci Chart pro daný canvas element\n        const chartInstance = Chart.getChart(chart);\n        this.chartModalService.openChartModal(chartInstance || null, title);\n      } else {\n        this.chartModalService.openChartModal(chart, title);\n      }\n    }\n    /**\r\n     * Inicializace popoverů pro nápovědu\r\n     */\n    initPopovers() {\n      // Nejprve zničíme existující popovers\n      this.destroyPopovers();\n      // Definice obsahu nápověd\n      const helpContent = {\n        'api-performance': 'Graf zobrazuje top 10 nejpomalejších metod seřazených podle 95. percentilu doby odezvy. ' + 'Pro každou metodu jsou zobrazeny dva sloupce: průměrná doba odezvy (modrý sloupec) a maximální doba odezvy (červený sloupec). ' + 'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',\n        'api-calls': 'Graf zobrazuje počet DISApi volání v čase pro vybrané období. ' + 'Zobrazuje volání z DIS aplikací na DISAdmin.DISApi službu. ' + 'Data jsou získávána z tabulky ActivityLogs za zvolené období.',\n        'instances-usage': 'Graf zobrazuje celkový počet volání DIS metod podle instancí. ' + 'Ukazuje, které instance jsou nejvíce zatížené podle skutečného počtu volání metod. ' + 'Data jsou získávána z tabulky PerformanceMetrics jako suma TotalCount za zvolené období.',\n        'production-performance': 'Graf zobrazuje průměrnou dobu odezvy a 95. percentil pouze pro produkční instance. ' + 'Produkční instance jsou kritické pro běžný provoz a jejich výkon je klíčový pro uživatelskou spokojenost. ' + '95. percentil ukazuje, že 95% všech volání bylo rychlejších než zobrazená hodnota, což je lepší indikátor než prostý průměr. ' + 'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',\n        'security-events': 'Graf zobrazuje počet bezpečnostních událostí v čase. ' + 'Zahrnuje události jako neúspěšné pokusy o přihlášení, podezřelé aktivity a porušení bezpečnosti. ' + 'Data jsou získávána z tabulky SecurityLogs za zvolené období.',\n        'system-info': 'Tento widget zobrazuje souhrnné systémové informace včetně počtu aktivních upozornění, ' + 'dostupnosti API, průměrné odezvy DIS metod a počtu expirujících certifikátů. ' + 'Poskytuje rychlý přehled o celkovém stavu systému.',\n        // Nápovědy pro karty systémových informací\n        'alerts-count': 'Počet aktivních upozornění v systému. Upozornění jsou automaticky generována při detekci problémů s výkonem, ' + 'bezpečností nebo dostupností služeb. Kliknutím přejdete na modul Alerty a upozornění.',\n        'security-events-count': 'Počet nevyřešených bezpečnostních událostí včetně neúspěšných pokusů o přihlášení, ' + 'podezřelých aktivit a narušení bezpečnosti. Kliknutím přejdete na modul Bezpečnostní události.',\n        'api-availability': 'Procentuální dostupnost DIS API za posledních 24 hodin. Počítá se jako poměr úspěšných volání ' + 'k celkovému počtu volání API. Data jsou získávána z tabulky ActivityLogs. Kliknutím přejdete na modul Monitoring.',\n        'api-response-time': 'Průměrná doba odezvy DIS API metod v milisekundách za posledních 24 hodin. ' + 'Nižší hodnota znamená lepší výkon systému. Data jsou získávána z tabulky PerformanceMetrics. ' + 'Kliknutím přejdete na modul Výkon DIS.',\n        'expiring-certificates': 'Počet certifikátů, které vyprší během následujících 30 dnů. ' + 'Včasná obnova certifikátů je kritická pro bezpečnost a nepřerušený provoz systému. ' + 'Kliknutím přejdete na modul Správa certifikátů.'\n      };\n      // Nejprve zrušíme všechny existující popovery\n      document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n        const popover = bootstrap.Popover.getInstance(el);\n        if (popover) {\n          popover.dispose();\n        }\n      });\n      // Inicializace popoverů pomocí Bootstrap API\n      document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n        const helpType = el.getAttribute('data-help-type');\n        //console.log('Initializing popover for element with help-type:', helpType);\n        if (helpType && helpType in helpContent) {\n          try {\n            const popover = new bootstrap.Popover(el, {\n              content: helpContent[helpType],\n              html: true,\n              trigger: 'hover focus',\n              placement: 'top',\n              container: 'body',\n              sanitize: false\n            });\n            // Přidáme event listener pro kliknutí na klikatelné karty\n            if (el.classList.contains('clickable-card') || el.closest('.clickable-card')) {\n              el.addEventListener('click', () => {\n                popover.hide();\n              });\n            }\n          } catch (error) {\n            console.error('Error initializing popover:', error);\n          }\n        } else if (helpType) {\n          console.warn('Help content not found for type:', helpType);\n        }\n      });\n      // Přidáme globální event listener pro zavření popoverů při kliknutí kamkoliv\n      this.documentClickListener = event => {\n        const target = event.target;\n        // Pokud se nekliklo na element s popoverem nebo na samotný popover, zavřeme všechny popovers\n        if (!target.closest('[data-bs-toggle=\"popover\"]') && !target.closest('.popover')) {\n          this.hideAllPopovers();\n        }\n      };\n      document.addEventListener('click', this.documentClickListener);\n    }\n    /**\r\n     * Zavře všechny aktivní popovers\r\n     */\n    hideAllPopovers() {\n      document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n        const popoverInstance = bootstrap.Popover.getInstance(el);\n        if (popoverInstance) {\n          popoverInstance.hide();\n        }\n      });\n    }\n    /**\r\n     * Zničí všechny popovers\r\n     */\n    destroyPopovers() {\n      document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n        const popoverInstance = bootstrap.Popover.getInstance(el);\n        if (popoverInstance) {\n          popoverInstance.dispose();\n        }\n      });\n    }\n    /**\r\n     * Navigace na modul Alerty a upozornění\r\n     */\n    navigateToAlerts() {\n      this.hideAllPopovers();\n      this.router.navigate(['/alerts']);\n    }\n    /**\r\n     * Navigace na modul Bezpečnostní události\r\n     */\n    navigateToSecurityEvents() {\n      this.hideAllPopovers();\n      this.router.navigate(['/security']);\n    }\n    /**\r\n     * Navigace na modul Certifikáty\r\n     */\n    navigateToCertificates() {\n      this.hideAllPopovers();\n      this.router.navigate(['/certificates']);\n    }\n    /**\r\n     * Navigace na modul Monitoring\r\n     */\n    navigateToMonitoring() {\n      this.hideAllPopovers();\n      this.router.navigate(['/monitoring']);\n    }\n    /**\r\n     * Navigace na modul Výkon DIS\r\n     */\n    navigateToPerformance() {\n      this.hideAllPopovers();\n      this.router.navigate(['/performance']);\n    }\n    /**\r\n     * Inteligentní zkrácení názvu metody pro zobrazení v grafu\r\n     * @param text Text k zkrácení\r\n     * @param maxLength Maximální délka (výchozí 30 znaků)\r\n     * @returns Zkrácený text s \"...\" pokud je potřeba\r\n     */\n    truncateMethodName(text, maxLength = 30) {\n      if (!text || text.length <= maxLength) {\n        return text;\n      }\n      // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n      const truncated = text.substring(0, maxLength - 3);\n      const lastDotIndex = truncated.lastIndexOf('.');\n      // Pokud najdeme tečku a není příliš na začátku, zkrátíme na ní\n      if (lastDotIndex > 0 && lastDotIndex > maxLength * 0.4) {\n        return text.substring(0, lastDotIndex) + '...';\n      }\n      // Jinak zkrátíme na pevné délce\n      return truncated + '...';\n    }\n    /**\r\n     * Vytvoří konfiguraci pro zkrácení popisků na ose X\r\n     * @param originalLabels Originální popisky\r\n     * @param options Možnosti konfigurace\r\n     * @returns Objekt s konfigurací pro Chart.js\r\n     */\n    createTruncatedLabelsConfig(originalLabels, options = {}) {\n      const config = {\n        enabled: options.enabled ?? true,\n        maxLength: options.maxLength ?? 30,\n        showFullInTooltip: options.showFullInTooltip ?? true\n      };\n      if (!config.enabled) {\n        return {};\n      }\n      const result = {\n        scales: {\n          x: {\n            ticks: {\n              callback: (value, index) => {\n                console.log('X-axis ticks callback - value:', value, 'index:', index);\n                const label = originalLabels[index];\n                console.log('Original label:', label);\n                const truncated = this.truncateMethodName(label, config.maxLength);\n                console.log('Truncated label:', truncated);\n                return truncated;\n              }\n            }\n          }\n        }\n      };\n      if (config.showFullInTooltip) {\n        result.plugins = {\n          tooltip: {\n            callbacks: {\n              title: context => {\n                console.log('Tooltip title callback - context:', context);\n                if (context && context.length > 0) {\n                  const dataIndex = context[0].dataIndex;\n                  console.log('dataIndex:', dataIndex, 'originalLabels:', originalLabels);\n                  const fullLabel = originalLabels[dataIndex];\n                  console.log('Returning full label:', fullLabel);\n                  return fullLabel || context[0].label;\n                }\n                return context[0]?.label || '';\n              }\n            }\n          }\n        };\n      }\n      return result;\n    }\n    /**\r\n     * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu\r\n     * @param baseConfig Základní konfigurace grafu\r\n     * @param truncatedConfig Konfigurace pro zkrácené popisky\r\n     * @returns Sloučená konfigurace\r\n     */\n    mergeChartConfig(baseConfig, truncatedConfig) {\n      if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {\n        return baseConfig;\n      }\n      const merged = {\n        ...baseConfig\n      };\n      // Sloučení scales\n      if (truncatedConfig.scales) {\n        merged.scales = merged.scales || {};\n        if (truncatedConfig.scales.x) {\n          merged.scales.x = merged.scales.x || {};\n          merged.scales.x.ticks = {\n            ...merged.scales.x.ticks,\n            ...truncatedConfig.scales.x.ticks\n          };\n        }\n      }\n      // Sloučení plugins\n      if (truncatedConfig.plugins) {\n        merged.plugins = merged.plugins || {};\n        if (truncatedConfig.plugins.tooltip) {\n          merged.plugins.tooltip = merged.plugins.tooltip || {};\n          merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};\n          // Sloučení tooltip callbacks - zachováme existující a přidáme nové\n          Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {\n            merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];\n          });\n        }\n      }\n      return merged;\n    }\n    static {\n      this.ɵfac = function DashboardComponent_Factory(t) {\n        return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ChartService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.AlertService), i0.ɵɵdirectiveInject(i4.DashboardConfigService), i0.ɵɵdirectiveInject(i5.ChartModalService), i0.ɵɵdirectiveInject(i6.MonitoringService), i0.ɵɵdirectiveInject(i7.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        viewQuery: function DashboardComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(_c2, 5);\n            i0.ɵɵviewQuery(_c3, 5);\n            i0.ɵɵviewQuery(_c4, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.apiCallsChartRef = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.apiPerformanceChartRef = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.instancesUsageChartRef = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.productionPerformanceChartRef = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.securityEventsChartRef = _t.first);\n          }\n        },\n        decls: 34,\n        vars: 36,\n        consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"d-flex\", \"gap-2\", \"flex-wrap\"], [\"role\", \"group\", 1, \"btn-group\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-1\"], [1, \"d-none\", \"d-sm-inline\"], [1, \"btn\", 3, \"click\"], [1, \"bi\", 3, \"ngClass\"], [\"class\", \"btn btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"alert alert-info mb-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"cdkDropList\", \"\", 1, \"row\", 3, \"cdkDropListDisabled\", \"cdkDropListDropped\"], [\"class\", \"col-lg-6 mb-4\", \"cdkDrag\", \"\", 3, \"cdkDragDisabled\", 4, \"ngIf\"], [\"class\", \"row\", \"cdkDrag\", \"\", 3, \"cdkDragDisabled\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-arrow-counterclockwise\", \"me-1\"], [1, \"alert\", \"alert-info\", \"mb-4\"], [1, \"bi\", \"bi-info-circle-fill\", \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\"], [\"cdkDrag\", \"\", 1, \"col-lg-6\", \"mb-4\", 3, \"cdkDragDisabled\"], [1, \"card\", \"h-100\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-calls\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"title\", \"Zobrazit graf na celou obrazovku\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-arrows-fullscreen\"], [1, \"card-body\"], [1, \"chart-container\", 2, \"position\", \"relative\", \"height\", \"300px\"], [\"apiCallsChart\", \"\"], [\"class\", \"drag-handle\", \"cdkDragHandle\", \"\", 4, \"ngIf\"], [\"cdkDragHandle\", \"\", 1, \"drag-handle\"], [1, \"bi\", \"bi-grip-horizontal\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-performance\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"apiPerformanceChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"instances-usage\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"instancesUsageChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"production-performance\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"productionPerformanceChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"security-events\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"securityEventsChart\", \"\"], [\"cdkDrag\", \"\", 1, \"row\", 3, \"cdkDragDisabled\"], [1, \"col-12\", \"mb-4\"], [1, \"card\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"system-info\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"class\", \"widget-controls\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-6\", \"col-lg-2-4\", \"mb-3\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"alerts-count\", 1, \"card\", \"bg-primary\", \"text-white\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"card-body\", \"text-center\", \"compact-card\"], [1, \"h2\", \"mb-1\"], [1, \"mb-0\", \"card-label\"], [\"class\", \"col-6 col-lg-2-4 mb-3\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-availability\", 1, \"card\", \"bg-success\", \"text-white\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-response-time\", 1, \"card\", \"bg-info\", \"text-white\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"expiring-certificates\", 1, \"card\", \"bg-warning\", \"text-dark\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"widget-controls\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"bi\", \"bi-eye-slash\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"security-events-count\", 1, \"card\", \"bg-danger\", \"text-white\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_6_listener() {\n              return ctx.onDaysChange(1);\n            });\n            i0.ɵɵtext(7, \"1 den\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_8_listener() {\n              return ctx.onDaysChange(7);\n            });\n            i0.ɵɵtext(9, \"7 dn\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_10_listener() {\n              return ctx.onDaysChange(30);\n            });\n            i0.ɵɵtext(11, \"30 dn\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_12_listener() {\n              return ctx.onDaysChange(90);\n            });\n            i0.ɵɵtext(13, \"90 dn\\u00ED\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 2)(15, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_15_listener() {\n              return ctx.refreshCharts();\n            });\n            i0.ɵɵelement(16, \"i\", 6);\n            i0.ɵɵelementStart(17, \"span\", 7);\n            i0.ɵɵtext(18, \"Aktualizovat\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_19_listener() {\n              return ctx.toggleEditMode();\n            });\n            i0.ɵɵelement(20, \"i\", 9);\n            i0.ɵɵelementStart(21, \"span\", 7);\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(23, DashboardComponent_button_23_Template, 4, 0, \"button\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(24, DashboardComponent_div_24_Template, 3, 0, \"div\", 11);\n            i0.ɵɵtemplate(25, DashboardComponent_div_25_Template, 4, 0, \"div\", 12);\n            i0.ɵɵtemplate(26, DashboardComponent_div_26_Template, 2, 1, \"div\", 13);\n            i0.ɵɵelementStart(27, \"div\", 14);\n            i0.ɵɵlistener(\"cdkDropListDropped\", function DashboardComponent_Template_div_cdkDropListDropped_27_listener($event) {\n              return ctx.onDrop($event);\n            });\n            i0.ɵɵtemplate(28, DashboardComponent_div_28_Template, 12, 4, \"div\", 15);\n            i0.ɵɵtemplate(29, DashboardComponent_div_29_Template, 12, 4, \"div\", 15);\n            i0.ɵɵtemplate(30, DashboardComponent_div_30_Template, 12, 4, \"div\", 15);\n            i0.ɵɵtemplate(31, DashboardComponent_div_31_Template, 12, 4, \"div\", 15);\n            i0.ɵɵtemplate(32, DashboardComponent_div_32_Template, 12, 4, \"div\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(33, DashboardComponent_div_33_Template, 39, 10, \"div\", 16);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 1)(\"btn-outline-primary\", ctx.selectedDays !== 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 7)(\"btn-outline-primary\", ctx.selectedDays !== 7);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 30)(\"btn-outline-primary\", ctx.selectedDays !== 30);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 90)(\"btn-outline-primary\", ctx.selectedDays !== 90);\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassProp(\"btn-outline-warning\", !ctx.editMode)(\"btn-warning\", ctx.editMode);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(33, _c5, !ctx.editMode, ctx.editMode));\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.editMode ? \"Dokon\\u010Dit \\u00FApravy\" : \"Upravit dashboard\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"cdkDropListDisabled\", !ctx.editMode);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"api-calls\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"api-performance\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"instances-usage\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"production-performance\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAdmin && ctx.isWidgetVisible(\"security-events\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAdmin && ctx.isWidgetVisible(\"system-info\"));\n          }\n        },\n        dependencies: [i8.NgClass, i8.NgIf, i9.CdkDropList, i9.CdkDrag, i9.CdkDragHandle],\n        styles: [\".chart-container[_ngcontent-%COMP%]{position:relative;margin:auto;height:300px;width:100%}.draggable-card[_ngcontent-%COMP%]{cursor:move;transition:box-shadow .2s ease;position:relative}.draggable-card[_ngcontent-%COMP%]:hover{box-shadow:0 .5rem 1rem #00000026}.drag-handle[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;height:24px;background-color:#0000000d;display:flex;align-items:center;justify-content:center;cursor:move;border-bottom-left-radius:.25rem;border-bottom-right-radius:.25rem}.drag-handle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem;color:#6c757d}.widget-controls[_ngcontent-%COMP%]{display:flex;gap:.5rem}.cdk-drag-preview[_ngcontent-%COMP%]{box-shadow:0 .5rem 1rem #0000004d;opacity:.8;z-index:1000}.cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:.3;background-color:#007bff1a;border:2px dashed #0d6efd;border-radius:8px}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.row.cdk-drop-list-dragging[_ngcontent-%COMP%]   .col-lg-6[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}body.dark-theme[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{background-color:#0d6efd33;border:2px dashed #0d6efd}.card[_ngcontent-%COMP%]{box-shadow:0 4px 6px #0000001a;transition:transform .2s ease,box-shadow .2s ease;border-radius:8px;overflow:hidden}.card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 8px 15px #0000001a}.card-header[_ngcontent-%COMP%]{font-weight:500}.badge[_ngcontent-%COMP%]{font-weight:500;padding:.5em .75em}.alert-type-badge[_ngcontent-%COMP%]{padding:.5rem;border-radius:.25rem;font-weight:500;font-size:.85rem}.alert-type-security-breach[_ngcontent-%COMP%]{background-color:#dc3545;color:#fff}.alert-type-certificate-expiration[_ngcontent-%COMP%]{background-color:#ffc107;color:#212529}.alert-type-failed-connection[_ngcontent-%COMP%]{background-color:#fd7e14;color:#fff}.alert-type-suspicious-activity[_ngcontent-%COMP%]{background-color:#6f42c1;color:#fff}.severity-badge[_ngcontent-%COMP%]{padding:.5rem;border-radius:.25rem;font-weight:500;font-size:.85rem}.severity-high[_ngcontent-%COMP%]{background-color:#dc3545;color:#fff}.severity-medium[_ngcontent-%COMP%]{background-color:#fd7e14;color:#fff}.severity-low[_ngcontent-%COMP%]{background-color:#ffc107;color:#212529}@media (max-width: 768px){.chart-container[_ngcontent-%COMP%]{height:250px}.display-4[_ngcontent-%COMP%]{font-size:1.5rem}.card-body[_ngcontent-%COMP%], .card-header[_ngcontent-%COMP%]{padding:.75rem}.row[_ngcontent-%COMP%] > [class*=col-][_ngcontent-%COMP%]{padding-left:8px;padding-right:8px}.row[_ngcontent-%COMP%]{margin-left:-8px;margin-right:-8px}.btn-group.role-group[_ngcontent-%COMP%]{flex-wrap:wrap}.btn-group.role-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{flex:1;padding:.375rem .5rem;margin-bottom:5px}.card[_ngcontent-%COMP%]   h5.mb-0[_ngcontent-%COMP%]{font-size:1rem}.card[_ngcontent-%COMP%]   p.mb-0[_ngcontent-%COMP%]{font-size:.8rem}.widget-controls[_ngcontent-%COMP%]{position:absolute;top:.5rem;right:.5rem}}body.dark-theme[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{background-color:#2b3035;border-color:#373b3e}body.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]{color:#e9ecef}body.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-bottom-color:#373b3e}body.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], body.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top-color:#373b3e}body.dark-theme[_ngcontent-%COMP%]   .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#ffffff13}body.dark-theme[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]{color:#0d6efd;border-color:#0d6efd}body.dark-theme[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#0d6efd;color:#fff}body.dark-theme[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]{color:#198754;border-color:#198754}body.dark-theme[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]:hover{background-color:#198754;color:#fff}body.dark-theme[_ngcontent-%COMP%]   .draggable-card[_ngcontent-%COMP%]{background-color:#1a2530}body.dark-theme[_ngcontent-%COMP%]   .drag-handle[_ngcontent-%COMP%]{background-color:#ffffff0d}body.dark-theme[_ngcontent-%COMP%]   .drag-handle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#adb5bd}.clickable-card[_ngcontent-%COMP%]{transition:transform .2s ease,box-shadow .2s ease,opacity .2s ease}.clickable-card[_ngcontent-%COMP%]:hover{transform:translateY(-3px) scale(1.02);box-shadow:0 8px 20px #00000026;opacity:.9}.clickable-card[_ngcontent-%COMP%]:active{transform:translateY(-1px) scale(1.01);box-shadow:0 4px 10px #0000001a}body.dark-theme[_ngcontent-%COMP%]   .clickable-card[_ngcontent-%COMP%]:hover{box-shadow:0 8px 20px #0000004d}@media (min-width: 992px){.col-lg-2-4[_ngcontent-%COMP%]{flex:0 0 20%;max-width:20%}}@media (min-width: 992px){.col-lg-2-4[_ngcontent-%COMP%]   .compact-card[_ngcontent-%COMP%]{padding:.75rem .25rem}.col-lg-2-4[_ngcontent-%COMP%]   .h2[_ngcontent-%COMP%]{font-size:1.5rem}.col-lg-2-4[_ngcontent-%COMP%]   .card-label[_ngcontent-%COMP%]{font-size:.85rem;line-height:1.2}}.card-label[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.3;font-weight:500}.compact-card[_ngcontent-%COMP%]{padding:.75rem .3rem}@media (max-width: 991px){.compact-card[_ngcontent-%COMP%]{padding:.75rem .5rem}.card-label[_ngcontent-%COMP%]{font-size:.85rem}}@media (max-width: 576px){.col-lg-2-4[_ngcontent-%COMP%]   .card-label[_ngcontent-%COMP%]{font-size:.8rem;line-height:1.2}.col-lg-2-4[_ngcontent-%COMP%]   .h2[_ngcontent-%COMP%]{font-size:1.3rem}}\"]\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}