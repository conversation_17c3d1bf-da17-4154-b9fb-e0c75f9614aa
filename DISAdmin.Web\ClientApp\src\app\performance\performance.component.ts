import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { PerformanceService } from '../services/performance.service';
import { InstanceService } from '../services/instance.service';
import { ChartModalService } from '../services/chart-modal.service';
import { Chart, registerables } from 'chart.js';
import { DatePipe } from '@angular/common';
import { Subscription } from 'rxjs';
import { PerformanceFilterData } from './performance-filter/performance-filter.component';
import { FilterField, minArrayLength } from '../shared/advanced-filter/advanced-filter.component';
import { TabItem } from '../shared/tab-navigation/tab-navigation.component';
import { TableSortingService, SortingState } from '../services/table-sorting.service';

// Deklarace globálního objektu bootstrap pro TypeScript
declare const bootstrap: any;

// Registrace všech komponent Chart.js
Chart.register(...registerables);

@Component({
  selector: 'app-performance',
  templateUrl: './performance.component.html',
  styleUrls: ['./performance.component.css']
})
export class PerformanceComponent implements OnInit, AfterViewInit, OnDestroy {
  // Reference na canvas elementy pro grafy
  @ViewChild('aggregatedPerformanceChart') aggregatedPerformanceChartRef!: ElementRef;
  @ViewChild('instancesComparisonChart') instancesComparisonChartRef!: ElementRef;
  @ViewChild('versionsComparisonChart') versionsComparisonChartRef!: ElementRef;
  @ViewChild('methodDetailChart') methodDetailChartRef!: ElementRef;

  // Grafy
  aggregatedPerformanceChart: Chart | null = null;
  instancesComparisonChart: Chart | null = null;
  versionsComparisonChart: Chart | null = null;
  methodDetailChart: Chart | null = null;

  // Stav vykreslování grafů
  renderingAggregatedChart: boolean = false;
  renderingInstancesComparisonChart: boolean = false;
  renderingVersionsComparisonChart: boolean = false;
  renderingMethodDetailChart: boolean = false;

  // Indikátory, zda jsou k dispozici data pro grafy
  hasAggregatedChartData: boolean = true;
  hasInstancesComparisonChartData: boolean = true;
  hasVersionsComparisonChartData: boolean = true;
  hasMethodDetailChartData: boolean = true;

  // Data
  slowestMethods: any[] = [];
  mostCalledMethods: any[] = [];
  allMethods: any[] = [];
  selectedMethod: any = null;
  methodDetailData: any = null;
  instances: any[] = [];
  aggregatedPerformanceData: any = null;
  instancesComparisonData: any = null;
  versionsComparisonData: any = null;
  methodDetailChartData: any = null;

  // Typ metod pro zobrazení v záložce "Metody"
  methodsDisplayType: string = 'slowest';

  // Řazení
  sortColumn: string = 'percentil95';
  sortDirection: 'asc' | 'desc' = 'desc';

  // Pole pro filtry
  aggregatedFilterFields: FilterField[] = [];
  instancesComparisonFilterFields: FilterField[] = [];
  versionsComparisonFilterFields: FilterField[] = [];
  methodsFilterFields: FilterField[] = [];
  methodDetailFilterFields: FilterField[] = [];

  // Stav komponenty
  loading = true;
  error: string | null = null;
  activeTab = 'aggregated';
  subscriptions: Subscription[] = [];

  // Definice záložek
  tabs: TabItem[] = [
    { id: 'aggregated', label: 'Agregovaný výkon' },
    { id: 'instances-comparison', label: 'Srovnání instancí' },
    { id: 'versions-comparison', label: 'Srovnání verzí' },
    { id: 'methods', label: 'Metody' },
    { id: 'method-detail', label: 'Detail metody' }
  ];

  // Filtry
  aggregatedFilterData: PerformanceFilterData = {
    period: 7
  };

  instancesComparisonFilterData: PerformanceFilterData = {
    instanceIds: [],
    period: 7,
    className: '',
    methodName: ''
  };

  versionsComparisonFilterData: PerformanceFilterData = {
    period: 90,
    className: '',
    methodName: ''
  };

  methodDetailFilterData: PerformanceFilterData = {
    period: 30,
    className: '',
    methodName: ''
  };

  constructor(
    private performanceService: PerformanceService,
    private instanceService: InstanceService,
    private datePipe: DatePipe,
    private chartModalService: ChartModalService,
    private changeDetectorRef: ChangeDetectorRef,
    private tableSortingService: TableSortingService
  ) { }

  ngOnInit(): void {
    console.log('PerformanceComponent - ngOnInit');
    // Nejprve inicializujeme prázdná pole filtrů, aby se komponenta advanced-filter správně inicializovala
    this.initEmptyFilterFields();

    // Načtení uloženého stavu třídění
    this.loadSortingState();

    // Načtení poslední aktivní záložky z localStorage
    const lastActiveTab = this.loadActiveTab();
    if (lastActiveTab) {
      console.log(`ngOnInit - Načtena poslední aktivní záložka: ${lastActiveTab}`);
      this.activeTab = lastActiveTab;
    }

    // Inicializace výchozích hodnot filtru
    this.aggregatedFilterData = {
      instanceId: undefined,
      period: '7',
      fromDate: undefined,
      toDate: undefined,
      methodsDisplayType: 'slowest'
    };

    // Načtení instancí - toto musí proběhnout jako první
    this.instanceService.getAll().subscribe({
      next: (data: any) => {
        console.log('ngOnInit - Instance úspěšně načteny:', data);
        this.instances = data;

        // Po načtení instancí inicializujeme pole filtrů
        this.initFilterFields();

        // Načtení posledního filtru pro aktuální záložku
        this.loadLastFilter('performance_aggregated');

        // Přímé volání načtení dat až po načtení instancí
        console.log('ngOnInit - Volám API pro načtení dat po načtení instancí');

        // Přímé volání API s použitím uloženého filtru
        setTimeout(() => {
          console.log('ngOnInit - Volám API přímo s použitím uloženého filtru:', this.aggregatedFilterData);
          this.loading = true;

          // Příprava parametrů pro volání API
          let instanceId = this.aggregatedFilterData.instanceId;
          if (instanceId === null || instanceId === undefined || String(instanceId) === "null") {
            instanceId = undefined;
          }

          let fromDate: Date;
          let toDate: Date = new Date();

          // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní
          if (!this.aggregatedFilterData.period) {
            fromDate = new Date();
            fromDate.setDate(toDate.getDate() - 7); // Výchozí hodnota 7 dní
          }
          // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
          else if (this.aggregatedFilterData.period !== 'custom') {
            fromDate = new Date();
            fromDate.setDate(toDate.getDate() - Number(this.aggregatedFilterData.period));
          }
          // Pokud je vybráno vlastní období, použijeme zadané hodnoty
          else {
            // Kontrola, zda fromDate a toDate jsou validní
            if (this.aggregatedFilterData.fromDate) {
              fromDate = new Date(this.aggregatedFilterData.fromDate);
            } else {
              // Výchozí hodnota, pokud fromDate není zadáno
              fromDate = new Date();
              fromDate.setDate(toDate.getDate() - 7);
            }

            if (this.aggregatedFilterData.toDate) {
              toDate = new Date(this.aggregatedFilterData.toDate);
            }
          }

          console.log('ngOnInit - Parametry pro volání API:', { instanceId, fromDate, toDate });

          const subscription = this.performanceService.getAggregatedPerformance(instanceId, fromDate, toDate).subscribe({
            next: (data: any) => {
              console.log('ngOnInit - Data úspěšně načtena přímo:', data);

              // Uložení dat pro pozdější vykreslení
              this.aggregatedPerformanceData = data;

              // Vykreslení grafu
              if (this.aggregatedPerformanceChartRef) {
                this.renderAggregatedPerformanceChart(data);
              }

              this.loading = false;

              // Načtení dat pro záložku "Metody"
              setTimeout(() => {
                this.loadSlowestMethods();
              }, 100);
            },
            error: (error: any) => {
              console.error('ngOnInit - Chyba při přímém načítání dat:', error);
              this.loading = false;
            }
          });

          this.subscriptions.push(subscription);
        }, 500);
      },
      error: (error: any) => {
        console.error('ngOnInit - Chyba při načítání instancí:', error);
      }
    });
  }

  ngAfterViewInit(): void {
    console.log('PerformanceComponent - ngAfterViewInit');
    // Inicializace popoverů pro nápovědu
    this.initPopovers();

    // Inicializace výchozího nastavení viditelnosti metrik
    this.initDefaultMetricsVisibility();

    // Načtení dat pro aktivní záložku
    setTimeout(() => {
      // Simulujeme změnu záložky, aby se načetla data pro aktivní záložku
      this.changeTab(this.activeTab);
    }, 500);

    // Pokud již máme data, zkusíme vykreslit graf
    if (this.activeTab === 'aggregated' && this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {
      console.log('PerformanceComponent - Mám data, zkusím vykreslit graf');
      setTimeout(() => {
        this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);
      }, 500);
    } else {
      console.log('PerformanceComponent - Nemám data nebo canvas element, zkusím později');

      // Přidáme event listener pro změnu záložky, abychom mohli vykreslit graf, až bude canvas element k dispozici
      setTimeout(() => {
        if (this.activeTab === 'aggregated' && this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {
          console.log('PerformanceComponent - Mám data a canvas element, zkusím vykreslit graf po timeoutu');
          this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);
        }
      }, 2000);
    }
  }

  /**
   * Inicializace výchozího nastavení viditelnosti metrik
   */
  private initDefaultMetricsVisibility(): void {
    // Výchozí nastavení viditelnosti metrik pro agregovaný výkon
    if (!localStorage.getItem('metrics_visibility_aggregated')) {
      const aggregatedVisibility: Record<string, boolean> = {
        'Průměr (ms)': true,
        'Medián (ms)': true,
        '95. percentil (ms)': true,
        '99. percentil (ms)': false,
        'Minimum (ms)': false,
        'Maximum (ms)': false,
        'Počet volání': false
      };
      localStorage.setItem('metrics_visibility_aggregated', JSON.stringify(aggregatedVisibility));
    }

    // Výchozí nastavení viditelnosti metrik pro detail metody
    if (!localStorage.getItem('metrics_visibility_method-detail')) {
      const methodDetailVisibility: Record<string, boolean> = {
        'Průměr (ms)': true,
        'Medián (ms)': true,
        '95. percentil (ms)': true,
        'Počet volání': false
      };
      localStorage.setItem('metrics_visibility_method-detail', JSON.stringify(methodDetailVisibility));
    }

    // Pro srovnání instancí a verzí nemůžeme nastavit výchozí hodnoty,
    // protože názvy datasetů závisí na datech z API
  }

  /**
   * Inicializace prázdných polí filtrů
   */
  initEmptyFilterFields(): void {
    // Základní pole pro všechny filtry
    const periodField: FilterField = {
      name: 'period',
      label: 'Časové období',
      type: 'select',
      options: [
        { value: 1, label: '1 den' },
        { value: 7, label: '7 dní' },
        { value: 30, label: '30 dní' },
        { value: 90, label: '90 dní' },
        { value: 'custom', label: 'Vlastní období' }
      ]
    };

    // Definice polí pro datum od a do
    const fromDateField: FilterField = {
      name: 'fromDate',
      label: 'Od',
      type: 'date',
      visible: (values) => values.period === 'custom', // Zobrazit pouze při vlastním období
      required: (values) => values.period === 'custom', // Povinné pouze při vlastním období
      errorMessage: 'Zadejte počáteční datum'
    };

    const toDateField: FilterField = {
      name: 'toDate',
      label: 'Do',
      type: 'date',
      visible: (values) => values.period === 'custom', // Zobrazit pouze při vlastním období
      required: (values) => values.period === 'custom', // Povinné pouze při vlastním období
      errorMessage: 'Zadejte koncové datum'
    };

    // Inicializace prázdných polí filtrů
    this.aggregatedFilterFields = [
      {
        name: 'instanceId',
        label: 'Instance',
        type: 'select',
        options: [
          { value: null, label: 'Všechny instance' }
        ]
      },
      periodField,
      fromDateField,
      toDateField
    ];

    this.instancesComparisonFilterFields = [
      {
        name: 'instanceIds',
        label: 'Instance',
        type: 'multiselect',
        options: [],
        required: true,
        validators: [minArrayLength(2)],
        errorMessage: 'Vyberte alespoň dvě instance pro srovnání'
      },
      {
        name: 'className',
        label: 'Třída',
        type: 'text'
      },
      {
        name: 'methodName',
        label: 'Metoda',
        type: 'text'
      },
      periodField,
      fromDateField,
      toDateField
    ];

    this.versionsComparisonFilterFields = [
      {
        name: 'instanceId',
        label: 'Instance',
        type: 'select',
        options: [
          { value: null, label: 'Vyberte instanci' }
        ],
        required: true,
        errorMessage: 'Vyberte instanci pro srovnání verzí'
      },
      {
        name: 'className',
        label: 'Třída',
        type: 'text'
      },
      {
        name: 'methodName',
        label: 'Metoda',
        type: 'text'
      },
      periodField,
      fromDateField,
      toDateField
    ];

    this.methodsFilterFields = [
      {
        name: 'instanceId',
        label: 'Instance',
        type: 'select',
        options: [
          { value: null, label: 'Všechny instance' }
        ]
      },
      {
        name: 'methodsDisplayType',
        label: 'Metrika',
        type: 'select',
        options: [
          { value: 'slowest', label: 'Nejpomalejší metody' },
          { value: 'most-called', label: 'Nejčastěji volané metody' },
          { value: 'all', label: 'Všechny metody' }
        ]
      },
      periodField,
      fromDateField,
      toDateField
    ];

    this.methodDetailFilterFields = [
      {
        name: 'instanceId',
        label: 'Instance',
        type: 'select',
        options: [
          { value: 0, label: 'Všechny instance' }
        ],
        required: false
      },
      {
        name: 'className',
        label: 'Třída',
        type: 'text',
        required: true,
        errorMessage: 'Zadejte název třídy'
      },
      {
        name: 'methodName',
        label: 'Metoda',
        type: 'text',
        required: true,
        errorMessage: 'Zadejte název metody'
      },
      periodField,
      fromDateField,
      toDateField
    ];
  }

  /**
   * Inicializace polí filtrů
   */
  initFilterFields(): void {
    // Základní pole pro všechny filtry
    const periodField: FilterField = {
      name: 'period',
      label: 'Časové období',
      type: 'select',
      options: [
        { value: 1, label: '1 den' },
        { value: 7, label: '7 dní' },
        { value: 30, label: '30 dní' },
        { value: 90, label: '90 dní' },
        { value: 'custom', label: 'Vlastní období' }
      ]
    };

    // Aktualizace pole pro filtr agregovaných metrik
    const instanceOptions = [
      { value: null, label: 'Všechny instance' },
      ...this.instances
        .sort((a, b) => {
          // Nejprve seřadíme podle zkratky zákazníka
          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);
          // Pokud jsou zkratky stejné, seřadíme podle názvu instance
          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);
        })
        .map(instance => ({
          value: instance.id,
          label: `${instance.customerAbbreviation} - ${instance.name}`
        }))
    ];

    // Aktualizace pole pro filtr agregovaných metrik
    this.updateFilterField(this.aggregatedFilterFields, 0, {
      name: 'instanceId',
      label: 'Instance',
      type: 'select',
      options: instanceOptions
    });

    // Aktualizace pole pro filtr srovnání instancí
    this.updateFilterField(this.instancesComparisonFilterFields, 0, {
      name: 'instanceIds',
      label: 'Instance',
      type: 'multiselect',
      options: this.instances
        .sort((a, b) => {
          // Nejprve seřadíme podle zkratky zákazníka
          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);
          // Pokud jsou zkratky stejné, seřadíme podle názvu instance
          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);
        })
        .map(instance => ({
          value: instance.id,
          label: `${instance.customerAbbreviation} - ${instance.name}`
        })),
      required: true,
      validators: [minArrayLength(2)],
      errorMessage: 'Vyberte alespoň dvě instance pro srovnání'
    });

    // Aktualizace pole pro filtr srovnání verzí
    this.updateFilterField(this.versionsComparisonFilterFields, 0, {
      name: 'instanceId',
      label: 'Instance',
      type: 'select',
      options: [
        { value: null, label: 'Vyberte instanci' },
        ...this.instances
          .sort((a, b) => {
            // Nejprve seřadíme podle zkratky zákazníka
            const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);
            // Pokud jsou zkratky stejné, seřadíme podle názvu instance
            return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);
          })
          .map(instance => ({
            value: instance.id,
            label: `${instance.customerAbbreviation} - ${instance.name}`
          }))
      ],
      required: true,
      errorMessage: 'Vyberte instanci pro srovnání verzí'
    });

    // Aktualizace pole pro filtr metod
    this.updateFilterField(this.methodsFilterFields, 0, {
      name: 'instanceId',
      label: 'Instance',
      type: 'select',
      options: instanceOptions
    });

    // Aktualizace pole pro filtr detailu metody
    this.updateFilterField(this.methodDetailFilterFields, 0, {
      name: 'instanceId',
      label: 'Instance',
      type: 'select',
      options: [
        { value: 0, label: 'Všechny instance' },
        ...this.instances
          .sort((a, b) => {
            // Nejprve seřadíme podle zkratky zákazníka
            const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);
            // Pokud jsou zkratky stejné, seřadíme podle názvu instance
            return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);
          })
          .map(instance => ({
            value: instance.id,
            label: `${instance.customerAbbreviation} - ${instance.name}`
          }))
      ]
    });

    // Aktualizace pole period pro všechny filtry
    this.updateFilterField(this.aggregatedFilterFields, 1, periodField);
    this.updateFilterField(this.instancesComparisonFilterFields, 3, periodField);
    this.updateFilterField(this.versionsComparisonFilterFields, 3, periodField);
    this.updateFilterField(this.methodsFilterFields, 2, periodField); // Index 2, protože na indexu 1 je methodsDisplayType
    this.updateFilterField(this.methodDetailFilterFields, 3, periodField);
  }

  /**
   * Aktualizace pole filtru
   */
  private updateFilterField(filterFields: FilterField[], index: number, newField: FilterField): void {
    if (filterFields && filterFields.length > index) {
      // Zachováme validátory a další vlastnosti z původního pole
      const originalField = filterFields[index];
      filterFields[index] = {
        ...originalField,
        ...newField
      };

      console.log(`Aktualizace pole ${newField.name}:`, filterFields[index]);
    }
  }

  /**
   * Načtení seznamu instancí
   */
  loadInstances(): void {
    console.log('loadInstances - Načítám seznam instancí');

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('loadInstances - Načítání dat již probíhá, přeskakuji');
      return;
    }

    this.loading = true;

    this.subscriptions.push(
      this.instanceService.getAll().subscribe({
        next: (data: any) => {
          console.log('loadInstances - Data úspěšně načtena:', data);
          this.instances = data;
          // Po načtení instancí inicializujeme pole filtrů
          this.initFilterFields();

          // Načtení posledního filtru pro aktuální záložku
          this.loadLastFilter('performance_aggregated');

          // Již nebudeme načítat data zde, protože to děláme přímo v ngOnInit
          console.log('loadInstances - Data instancí načtena, filtry inicializovány');

          this.loading = false;
        },
        error: (error: any) => {
          console.error('Chyba při načítání instancí:', error);
          this.loading = false;
        }
      })
    );
  }

  /**
   * Změna časového období
   */
  onPeriodChange(): void {
    // Tato metoda je volána při změně období v select boxu
    // Není potřeba implementovat žádnou logiku, protože
    // zobrazení/skrytí polí od-do je řešeno pomocí *ngIf v šabloně
  }



  ngOnDestroy(): void {
    // Zrušení všech aktivních subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Otevře modální okno s grafem v režimu "full screen"
   * @param chart Instance grafu nebo reference na canvas element
   * @param title Titulek grafu
   */
  openFullscreenChart(chart: Chart | HTMLCanvasElement | null, title: string): void {
    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart
    if (chart instanceof HTMLCanvasElement) {
      // Najdeme instanci Chart pro daný canvas element
      const chartInstance = Chart.getChart(chart);
      this.chartModalService.openChartModal(chartInstance || null, title);
    } else {
      this.chartModalService.openChartModal(chart, title);
    }
  }

  /**
   * Změna aktivní záložky
   */
  changeTab(tab: any): void {
    // Resetujeme chybovou hlášku při změně záložky
    this.error = '';
    // Spustíme detekci změn, aby se změna proměnné error projevila okamžitě
    this.changeDetectorRef.detectChanges();

    // Převod parametru na string, pokud není
    const tabId = typeof tab === 'string' ? tab : String(tab);
    this.activeTab = tabId;

    // Uložení aktivní záložky do localStorage
    this.saveActiveTab(tabId);

    // Pokud je vybrána záložka "method-detail", přidáme ji do seznamu záložek
    if (tabId === 'method-detail' && this.selectedMethod) {
      // Kontrola, zda záložka již existuje
      const tabExists = this.tabs.some(t => t.id === 'method-detail');
      if (!tabExists) {
        this.tabs.push({ id: 'method-detail', label: 'Detail metody' });
      }
    }

    // Načtení dat pro vybranou záložku
    switch (tabId) {
      case 'aggregated':
        // Načtení posledního filtru z localStorage
        this.loadLastFilter('performance_aggregated');
        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen
        requestAnimationFrame(() => {
          // Ještě přidáme timeout pro jistotu
          setTimeout(() => {
            console.log('onTabChange - Načítám data pro záložku aggregated');

            // Pokud již máme data, zkusíme vykreslit graf
            if (this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {
              console.log('onTabChange - Mám data, zkusím vykreslit graf');
              this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);
            } else {
              // Jinak načteme nová data
              this.loadAggregatedPerformance();
            }
          }, 500);
        });
        break;
      case 'instances-comparison':
        // Načtení posledního filtru z localStorage
        this.loadLastFilter('performance_instances_comparison');
        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen
        requestAnimationFrame(() => {
          // Ještě přidáme timeout pro jistotu
          setTimeout(() => {
            console.log('onTabChange - Načítám data pro záložku instances-comparison');

            // Pokud již máme data, zkusíme vykreslit graf
            if (this.instancesComparisonData && this.instancesComparisonChartRef) {
              console.log('onTabChange - Mám data, zkusím vykreslit graf');
              this.renderInstancesComparisonChart(this.instancesComparisonData);
            } else {
              // Jinak načteme nová data
              this.loadInstancesComparison();
            }
          }, 500);
        });
        break;
      case 'versions-comparison':
        // Načtení posledního filtru z localStorage
        this.loadLastFilter('performance_versions_comparison');
        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen
        requestAnimationFrame(() => {
          // Ještě přidáme timeout pro jistotu
          setTimeout(() => {
            console.log('onTabChange - Načítám data pro záložku versions-comparison');

            // Pokud již máme data, zkusíme vykreslit graf
            if (this.versionsComparisonData && this.versionsComparisonChartRef) {
              console.log('onTabChange - Mám data, zkusím vykreslit graf');
              this.renderVersionsComparisonChart(this.versionsComparisonData);
            } else {
              // Jinak načteme nová data
              this.loadVersionsComparison();
            }
          }, 500);
        });
        break;
      case 'methods':
        // Načtení posledního filtru z localStorage
        const lastFilterKey = `last_filter_performance_methods`;
        const lastFilterJson = localStorage.getItem(lastFilterKey);

        // Pokud není uložen žádný filtr, resetujeme instanceId na undefined
        if (!lastFilterJson) {
          console.log('onTabChange - Není uložen žádný filtr pro záložku methods, resetuji instanceId na undefined');
          this.aggregatedFilterData.instanceId = undefined;
        }

        this.loadLastFilter('performance_methods');

        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen
        requestAnimationFrame(() => {
          // Ještě přidáme timeout pro jistotu
          setTimeout(() => {
            console.log('onTabChange - Načítám data pro záložku methods, instanceId:', this.aggregatedFilterData.instanceId);
            this.loadMethodsByType();
          }, 100);
        });
        break;
      case 'method-detail':
        // Načtení posledního filtru z localStorage
        this.loadLastFilter('performance_method_detail');
        // Resetujeme indikátor, zda jsou k dispozici data pro graf
        this.hasMethodDetailChartData = true;

        // Pokud máme vybranou metodu, použijeme její hodnoty pro filtr
        if (this.selectedMethod) {
          console.log('onTabChange - Použití hodnot z vybrané metody pro filtr:', this.selectedMethod);

          // Zjištění aktuální hodnoty instanceId
          let instanceId = this.selectedMethod.instanceId;

          // Pokud instanceId je null nebo undefined, použijeme 0 (všechny instance)
          if (instanceId === null || instanceId === undefined || String(instanceId) === "null") {
            instanceId = 0;
          }

          // Aktualizace hodnot filtru
          this.methodDetailFilterData = {
            ...this.methodDetailFilterData,
            instanceId: instanceId,
            className: this.selectedMethod.className,
            methodName: this.selectedMethod.methodName
          };

          // Uložení filtru do localStorage
          this.saveFilter('performance_method_detail', this.methodDetailFilterData);
        }

        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen
        requestAnimationFrame(() => {
          // Ještě přidáme timeout pro jistotu
          setTimeout(() => {
            console.log('onTabChange - Načítám data pro záložku method-detail');

            // Vždy načteme nová data při přepnutí na záložku
            this.loadMethodDetail();
          }, 500);
        });
        break;
    }

    // Reinicializace popoverů po změně záložky
    setTimeout(() => {
      this.initPopovers();
    }, 500);
  }

  /**
   * Načtení posledního filtru z localStorage
   */
  loadLastFilter(entityType: string): void {
    console.log(`loadLastFilter - Načítám poslední filtr pro ${entityType}`);

    try {
      const lastFilterKey = `last_filter_${entityType}`;
      const lastFilterJson = localStorage.getItem(lastFilterKey);

      if (lastFilterJson) {
        const lastFilter = JSON.parse(lastFilterJson);
        console.log(`Načten poslední filtr pro ${entityType} z localStorage:`, lastFilter);

        // Aktualizace příslušného filtru podle typu entity
        switch (entityType) {
          case 'performance_aggregated':
            // Kontrola, zda se filtr skutečně změnil
            const currentAggregatedFilterStr = JSON.stringify(this.aggregatedFilterData);
            const newAggregatedFilterStr = JSON.stringify({ ...this.aggregatedFilterData, ...lastFilter });

            if (currentAggregatedFilterStr !== newAggregatedFilterStr) {
              this.aggregatedFilterData = { ...this.aggregatedFilterData, ...lastFilter };
              console.log(`Aktualizován filtr pro ${entityType}:`, this.aggregatedFilterData);
            } else {
              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);
            }
            break;
          case 'performance_instances_comparison':
            // Kontrola, zda se filtr skutečně změnil
            const currentInstancesComparisonFilterStr = JSON.stringify(this.instancesComparisonFilterData);
            const newInstancesComparisonFilterStr = JSON.stringify({ ...this.instancesComparisonFilterData, ...lastFilter });

            if (currentInstancesComparisonFilterStr !== newInstancesComparisonFilterStr) {
              this.instancesComparisonFilterData = { ...this.instancesComparisonFilterData, ...lastFilter };
              console.log(`Aktualizován filtr pro ${entityType}:`, this.instancesComparisonFilterData);
            } else {
              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);
            }
            break;
          case 'performance_versions_comparison':
            // Kontrola, zda se filtr skutečně změnil
            const currentVersionsComparisonFilterStr = JSON.stringify(this.versionsComparisonFilterData);
            const newVersionsComparisonFilterStr = JSON.stringify({ ...this.versionsComparisonFilterData, ...lastFilter });

            if (currentVersionsComparisonFilterStr !== newVersionsComparisonFilterStr) {
              this.versionsComparisonFilterData = { ...this.versionsComparisonFilterData, ...lastFilter };
              console.log(`Aktualizován filtr pro ${entityType}:`, this.versionsComparisonFilterData);
            } else {
              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);
            }
            break;
          case 'performance_methods':
            // Pro metody používáme stejný filtr jako pro agregované metriky
            // Kontrola, zda se filtr skutečně změnil
            const currentMethodsFilterStr = JSON.stringify(this.aggregatedFilterData);
            const newMethodsFilterStr = JSON.stringify({ ...this.aggregatedFilterData, ...lastFilter });

            if (currentMethodsFilterStr !== newMethodsFilterStr) {
              this.aggregatedFilterData = { ...this.aggregatedFilterData, ...lastFilter };

              // Aktualizace typu zobrazovaných metod
              if (lastFilter.methodsDisplayType) {
                this.methodsDisplayType = lastFilter.methodsDisplayType;
              }

              console.log(`Aktualizován filtr pro ${entityType}:`, this.aggregatedFilterData);
            } else {
              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);
            }
            break;
          case 'performance_method_detail':
            // Kontrola, zda se filtr skutečně změnil
            const currentMethodDetailFilterStr = JSON.stringify(this.methodDetailFilterData);
            const newMethodDetailFilterStr = JSON.stringify({ ...this.methodDetailFilterData, ...lastFilter });

            if (currentMethodDetailFilterStr !== newMethodDetailFilterStr) {
              this.methodDetailFilterData = { ...this.methodDetailFilterData, ...lastFilter };
              console.log(`Aktualizován filtr pro ${entityType}:`, this.methodDetailFilterData);
            } else {
              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);
            }
            break;
        }
      }
    } catch (error) {
      console.error(`Chyba při načítání posledního filtru pro ${entityType} z localStorage`, error);
    }
  }

  /**
   * Načtení agregovaných výkonnostních metrik
   */
  loadAggregatedPerformance(): void {
    console.log('loadAggregatedPerformance - Začínám načítat data');

    // Přidáme kontrolu, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('loadAggregatedPerformance - Načítání dat již probíhá, přeskakuji');
      return;
    }

    this.loading = true;
    this.error = '';

    // Kontrola, zda je reference na canvas element k dispozici
    if (!this.aggregatedPerformanceChartRef) {
      console.warn('loadAggregatedPerformance - Reference na canvas element není k dispozici, ale pokračuji v načítání dat');
    } else {
      console.log('loadAggregatedPerformance - Reference na canvas element je k dispozici');
    }

    // Kontrola, zda máme načtené instance
    if (!this.instances || this.instances.length === 0) {
      console.warn('loadAggregatedPerformance - Instance nejsou načteny, načítám je');

      // Načtení instancí
      this.instanceService.getAll().subscribe({
        next: (data: any) => {
          console.log('loadAggregatedPerformance - Instance úspěšně načteny:', data);
          this.instances = data;

          // Po načtení instancí inicializujeme pole filtrů
          this.initFilterFields();

          // Pokračujeme v načítání dat
          this.loadAggregatedPerformance();
        },
        error: (error: any) => {
          console.error('loadAggregatedPerformance - Chyba při načítání instancí:', error);
          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';
          this.loading = false;
        }
      });

      return;
    }

    // Kontrola, zda máme inicializovaný filtr
    if (!this.aggregatedFilterData) {
      console.warn('loadAggregatedPerformance - Filtr není inicializován, inicializuji výchozí hodnoty');
      this.aggregatedFilterData = {
        instanceId: undefined,
        period: '7',
        fromDate: undefined,
        toDate: undefined
      };
    }

    // Příprava parametrů
    // Pokud instanceId je řetězec "null" nebo hodnota null, použijeme undefined
    let instanceId = this.aggregatedFilterData.instanceId;
    if (instanceId === null || instanceId === undefined || String(instanceId) === "null") {
      instanceId = undefined;
    }
    console.log('loadAggregatedPerformance - instanceId:', instanceId);
    let fromDate: Date | undefined;
    let toDate: Date | undefined;

    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní
    if (!this.aggregatedFilterData.period) {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní
      toDate = today;
    }
    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
    else if (this.aggregatedFilterData.period !== 'custom') {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));
      toDate = today;
    }
    // Pokud je vybráno vlastní období, použijeme zadané hodnoty
    else {
      // Kontrola, zda fromDate a toDate jsou validní
      if (this.aggregatedFilterData.fromDate) {
        fromDate = new Date(this.aggregatedFilterData.fromDate);
      } else {
        // Výchozí hodnota, pokud fromDate není zadáno
        fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - 7);
      }

      if (this.aggregatedFilterData.toDate) {
        toDate = new Date(this.aggregatedFilterData.toDate);
      } else {
        // Výchozí hodnota, pokud toDate není zadáno
        toDate = new Date();
      }
    }

    console.log('loadAggregatedPerformance - Volám API pro načtení dat', { instanceId, fromDate, toDate });

    try {
      const subscription = this.performanceService.getAggregatedPerformance(instanceId, fromDate, toDate).subscribe({
        next: (data: any) => {
          console.log('loadAggregatedPerformance - Data úspěšně načtena:', data);

          if (!data) {
            console.error('loadAggregatedPerformance - API vrátilo prázdná data');
            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';
            this.loading = false;
            return;
          }

          // Kontrola, zda data obsahují potřebné údaje
          if (!data.labels && !data.dailyData) {
            console.warn('loadAggregatedPerformance - Data neobsahují labels ani dailyData, pokusím se je zpracovat');
          }

          // Uložení dat pro pozdější vykreslení
          this.aggregatedPerformanceData = data;

          // Vykreslení grafu
          this.renderAggregatedPerformanceChart(data);
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Chyba při načítání agregovaných výkonnostních metrik:', error);
          this.error = 'Nepodařilo se načíst agregované výkonnostní metriky. ' +
                      (error.message ? error.message : 'Zkuste to prosím znovu.');
          this.loading = false;

          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách
          if (error.status === 0) {
            console.log('loadAggregatedPerformance - Server neodpovídá, zkusím to znovu za 5 sekund');
            setTimeout(() => {
              console.log('loadAggregatedPerformance - Opakuji volání API');
              this.loadAggregatedPerformance();
            }, 5000);
          }
        },
        complete: () => {
          console.log('loadAggregatedPerformance - Volání API dokončeno');
        }
      });

      this.subscriptions.push(subscription);
    } catch (error) {
      console.error('loadAggregatedPerformance - Chyba při volání API:', error);
      this.error = 'Nepodařilo se načíst agregované výkonnostní metriky. Zkuste to prosím znovu.';
      this.loading = false;
    }

    // Načtení nejpomalejších metod - pouze pokud je aktivní záložka 'aggregated'
    if (this.activeTab === 'aggregated') {
      // Použijeme setTimeout, abychom zajistili, že se volání API nepřekrývají
      setTimeout(() => {
        this.loadSlowestMethods();
      }, 100);
    }
  }

  /**
   * Zpracování změny filtru pro agregované metriky
   */
  onAggregatedFilterChange(filterData: PerformanceFilterData): void {
    console.log('onAggregatedFilterChange - Přijatá data filtru:', filterData);

    // Kontrola, zda se filtr skutečně změnil
    const currentFilterStr = JSON.stringify(this.aggregatedFilterData);
    const newFilterStr = JSON.stringify(filterData);

    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat
    // Tím zajistíme, že tlačítko "Načíst data" bude vždy fungovat
    if (currentFilterStr === newFilterStr) {
      console.log('onAggregatedFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');
    }

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('onAggregatedFilterChange - Načítání dat již probíhá, přeskakuji');
      return;
    }

    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date
    if (filterData.period === 'custom') {
      if (filterData.fromDate && typeof filterData.fromDate === 'string') {
        filterData.fromDate = new Date(filterData.fromDate);
      }
      if (filterData.toDate && typeof filterData.toDate === 'string') {
        filterData.toDate = new Date(filterData.toDate);
      }
    }
    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)
    else if (filterData.fromDate && filterData.toDate) {
      if (typeof filterData.fromDate === 'string') {
        filterData.fromDate = new Date(filterData.fromDate);
      }
      if (typeof filterData.toDate === 'string') {
        filterData.toDate = new Date(filterData.toDate);
      }
    }

    this.aggregatedFilterData = filterData;
    console.log('onAggregatedFilterChange - Zpracovaná data filtru:', this.aggregatedFilterData);

    // Použijeme setTimeout, abychom zajistili, že se volání API nepřekrývají
    setTimeout(() => {
      this.loadAggregatedPerformance();
    }, 0);
  }

  /**
   * Načtení srovnání výkonu metod mezi instancemi
   */
  loadInstancesComparison(): void {
    console.log('loadInstancesComparison - Začínám načítat data');

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('loadInstancesComparison - Načítání dat již probíhá, přeskakuji');
      return;
    }

    this.loading = true;
    this.error = '';

    // Kontrola, zda máme načtené instance
    if (!this.instances || this.instances.length === 0) {
      console.warn('loadInstancesComparison - Instance nejsou načteny, načítám je');

      // Načtení instancí
      this.instanceService.getAll().subscribe({
        next: (data: any) => {
          console.log('loadInstancesComparison - Instance úspěšně načteny:', data);
          this.instances = data;

          // Po načtení instancí inicializujeme pole filtrů
          this.initFilterFields();

          // Pokračujeme v načítání dat
          this.loadInstancesComparison();
        },
        error: (error: any) => {
          console.error('loadInstancesComparison - Chyba při načítání instancí:', error);
          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';
          this.loading = false;
        }
      });

      return;
    }

    // Kontrola, zda jsou vybrány alespoň dvě instance
    if (!this.instancesComparisonFilterData.instanceIds || this.instancesComparisonFilterData.instanceIds.length < 2) {
      // Necháme validaci na formuláři, nezobrazujeme chybový blok
      this.loading = false;
      return;
    }

    // Příprava parametrů
    const instanceIds = this.instancesComparisonFilterData.instanceIds;
    const methodName = this.instancesComparisonFilterData.methodName;
    const className = this.instancesComparisonFilterData.className;
    let fromDate: Date | undefined;
    let toDate: Date | undefined;

    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní
    if (!this.instancesComparisonFilterData.period) {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní
      toDate = today;
    }
    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
    else if (this.instancesComparisonFilterData.period !== 'custom') {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - Number(this.instancesComparisonFilterData.period));
      toDate = today;
    }
    // Pokud je vybráno vlastní období, použijeme zadané hodnoty
    else {
      // Kontrola, zda fromDate a toDate jsou validní
      if (this.instancesComparisonFilterData.fromDate) {
        fromDate = new Date(this.instancesComparisonFilterData.fromDate);
      } else {
        // Výchozí hodnota, pokud fromDate není zadáno
        fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - 7);
      }

      if (this.instancesComparisonFilterData.toDate) {
        toDate = new Date(this.instancesComparisonFilterData.toDate);
      } else {
        // Výchozí hodnota, pokud toDate není zadáno
        toDate = new Date();
      }
    }

    try {
      console.log('loadInstancesComparison - Volám API pro načtení dat', { instanceIds, methodName, className, fromDate, toDate });

      const subscription = this.performanceService.getInstancesComparison(instanceIds, methodName, className, fromDate, toDate).subscribe({
        next: (data: any) => {
          console.log('loadInstancesComparison - Data úspěšně načtena:', data);

          if (!data) {
            console.error('loadInstancesComparison - API vrátilo prázdná data');
            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';
            this.loading = false;
            return;
          }

          // Uložení dat pro pozdější vykreslení
          this.instancesComparisonData = data;

          // Vykreslení grafu
          this.renderInstancesComparisonChart(data);
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Chyba při načítání srovnání instancí:', error);
          this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi instancemi. ' +
                      (error.message ? error.message : 'Zkuste to prosím znovu.');
          this.loading = false;

          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách
          if (error.status === 0) {
            console.log('loadInstancesComparison - Server neodpovídá, zkusím to znovu za 5 sekund');
            setTimeout(() => {
              console.log('loadInstancesComparison - Opakuji volání API');
              this.loadInstancesComparison();
            }, 5000);
          }
        },
        complete: () => {
          console.log('loadInstancesComparison - Volání API dokončeno');
        }
      });

      this.subscriptions.push(subscription);
    } catch (error) {
      console.error('loadInstancesComparison - Chyba při volání API:', error);
      this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi instancemi. Zkuste to prosím znovu.';
      this.loading = false;
    }
  }

  /**
   * Zpracování změny filtru pro srovnání instancí
   */
  onInstancesComparisonFilterChange(filterData: PerformanceFilterData): void {
    console.log('onInstancesComparisonFilterChange - Přijatá data filtru:', filterData);

    // Kontrola, zda se filtr skutečně změnil
    const currentFilterStr = JSON.stringify(this.instancesComparisonFilterData);
    const newFilterStr = JSON.stringify(filterData);

    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat
    // Tím zajistíme, že tlačítko "Načíst data" bude vždy fungovat
    if (currentFilterStr === newFilterStr) {
      console.log('onInstancesComparisonFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');
    }

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('onInstancesComparisonFilterChange - Načítání dat již probíhá, přeskakuji');
      return;
    }

    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date
    if (filterData.period === 'custom') {
      if (filterData.fromDate && typeof filterData.fromDate === 'string') {
        filterData.fromDate = new Date(filterData.fromDate);
      }
      if (filterData.toDate && typeof filterData.toDate === 'string') {
        filterData.toDate = new Date(filterData.toDate);
      }
    }
    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)
    else if (filterData.fromDate && filterData.toDate) {
      if (typeof filterData.fromDate === 'string') {
        filterData.fromDate = new Date(filterData.fromDate);
      }
      if (typeof filterData.toDate === 'string') {
        filterData.toDate = new Date(filterData.toDate);
      }
    }

    this.instancesComparisonFilterData = filterData;
    console.log('onInstancesComparisonFilterChange - Zpracovaná data filtru:', this.instancesComparisonFilterData);
    this.loadInstancesComparison();
  }

  /**
   * Načtení srovnání výkonu metod mezi verzemi
   */
  loadVersionsComparison(): void {
    console.log('loadVersionsComparison - Začínám načítat data');

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('loadVersionsComparison - Načítání dat již probíhá, přeskakuji');
      return;
    }

    this.loading = true;
    this.error = '';

    // Kontrola, zda máme načtené instance
    if (!this.instances || this.instances.length === 0) {
      console.warn('loadVersionsComparison - Instance nejsou načteny, načítám je');

      // Načtení instancí
      this.instanceService.getAll().subscribe({
        next: (data: any) => {
          console.log('loadVersionsComparison - Instance úspěšně načteny:', data);
          this.instances = data;

          // Po načtení instancí inicializujeme pole filtrů
          this.initFilterFields();

          // Pokračujeme v načítání dat
          this.loadVersionsComparison();
        },
        error: (error: any) => {
          console.error('loadVersionsComparison - Chyba při načítání instancí:', error);
          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';
          this.loading = false;
        }
      });

      return;
    }

    // Kontrola, zda je vybrána instance
    if (!this.versionsComparisonFilterData.instanceId ||
        this.versionsComparisonFilterData.instanceId === null ||
        String(this.versionsComparisonFilterData.instanceId) === "null") {
      console.warn('loadVersionsComparison - Není vybrána instance, přeskakuji načítání dat');
      // Necháme validaci na formuláři, nezobrazujeme chybový blok
      this.loading = false;
      return;
    }

    // Příprava parametrů
    const instanceId = this.versionsComparisonFilterData.instanceId;
    const methodName = this.versionsComparisonFilterData.methodName;
    const className = this.versionsComparisonFilterData.className;
    let fromDate: Date | undefined;
    let toDate: Date | undefined;

    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní
    if (!this.versionsComparisonFilterData.period) {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní
      toDate = today;
    }
    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
    else if (this.versionsComparisonFilterData.period !== 'custom') {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - Number(this.versionsComparisonFilterData.period));
      toDate = today;
    }
    // Pokud je vybráno vlastní období, použijeme zadané hodnoty
    else {
      // Kontrola, zda fromDate a toDate jsou validní
      if (this.versionsComparisonFilterData.fromDate) {
        fromDate = new Date(this.versionsComparisonFilterData.fromDate);
      } else {
        // Výchozí hodnota, pokud fromDate není zadáno
        fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - 7);
      }

      if (this.versionsComparisonFilterData.toDate) {
        toDate = new Date(this.versionsComparisonFilterData.toDate);
      } else {
        // Výchozí hodnota, pokud toDate není zadáno
        toDate = new Date();
      }
    }

    try {
      console.log('loadVersionsComparison - Volám API pro načtení dat', { instanceId, methodName, className, fromDate, toDate });

      const subscription = this.performanceService.getVersionsComparison(instanceId, methodName, className, fromDate, toDate).subscribe({
        next: (data: any) => {
          console.log('loadVersionsComparison - Data úspěšně načtena:', data);

          if (!data) {
            console.error('loadVersionsComparison - API vrátilo prázdná data');
            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';
            this.loading = false;
            return;
          }

          // Uložení dat pro pozdější vykreslení
          this.versionsComparisonData = data;

          // Vykreslení grafu
          this.renderVersionsComparisonChart(data);
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Chyba při načítání srovnání verzí:', error);
          this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi verzemi. ' +
                      (error.message ? error.message : 'Zkuste to prosím znovu.');
          this.loading = false;

          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách
          if (error.status === 0) {
            console.log('loadVersionsComparison - Server neodpovídá, zkusím to znovu za 5 sekund');
            setTimeout(() => {
              console.log('loadVersionsComparison - Opakuji volání API');
              this.loadVersionsComparison();
            }, 5000);
          }
        },
        complete: () => {
          console.log('loadVersionsComparison - Volání API dokončeno');
        }
      });

      this.subscriptions.push(subscription);
    } catch (error) {
      console.error('loadVersionsComparison - Chyba při volání API:', error);
      this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi verzemi. Zkuste to prosím znovu.';
      this.loading = false;
    }
  }

  /**
   * Zpracování změny filtru pro srovnání verzí
   */
  onVersionsComparisonFilterChange(filterData: PerformanceFilterData): void {
    console.log('onVersionsComparisonFilterChange - Přijatá data filtru:', filterData);

    // Kontrola, zda se filtr skutečně změnil
    const currentFilterStr = JSON.stringify(this.versionsComparisonFilterData);
    const newFilterStr = JSON.stringify(filterData);

    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat
    // Tím zajistíme, že tlačítko "Načíst data" bude vždy fungovat
    if (currentFilterStr === newFilterStr) {
      console.log('onVersionsComparisonFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');
    }

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('onVersionsComparisonFilterChange - Načítání dat již probíhá, přeskakuji');
      return;
    }

    // Kontrola, zda je vybrána instance
    if (filterData.instanceId === null || filterData.instanceId === undefined || String(filterData.instanceId) === "null") {
      console.warn('onVersionsComparisonFilterChange - Není vybrána instance, přeskakuji načítání dat');
      // Aktualizujeme data filtru, ale nevoláme načítání dat
      this.versionsComparisonFilterData = filterData;
      return;
    }

    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date
    if (filterData.period === 'custom') {
      if (filterData.fromDate && typeof filterData.fromDate === 'string') {
        filterData.fromDate = new Date(filterData.fromDate);
      }
      if (filterData.toDate && typeof filterData.toDate === 'string') {
        filterData.toDate = new Date(filterData.toDate);
      }
    }
    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)
    else if (filterData.fromDate && filterData.toDate) {
      if (typeof filterData.fromDate === 'string') {
        filterData.fromDate = new Date(filterData.fromDate);
      }
      if (typeof filterData.toDate === 'string') {
        filterData.toDate = new Date(filterData.toDate);
      }
    }

    this.versionsComparisonFilterData = filterData;
    console.log('onVersionsComparisonFilterChange - Zpracovaná data filtru:', this.versionsComparisonFilterData);
    this.loadVersionsComparison();
  }

  /**
   * Načtení nejčastěji volaných metod
   */
  loadMostCalledMethods(): void {
    console.log('loadMostCalledMethods - Začínám načítat data');

    // Přidáme kontrolu, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('loadMostCalledMethods - Načítání dat již probíhá, přeskakuji');
      return;
    }

    // Nastavíme příznak načítání
    this.loading = true;

    // Kontrola, zda máme načtené instance
    if (!this.instances || this.instances.length === 0) {
      console.warn('loadMostCalledMethods - Instance nejsou načteny, načítám je');

      // Načtení instancí
      this.instanceService.getAll().subscribe({
        next: (data: any) => {
          console.log('loadMostCalledMethods - Instance úspěšně načteny:', data);
          this.instances = data;

          // Po načtení instancí inicializujeme pole filtrů
          this.initFilterFields();

          // Pokračujeme v načítání dat
          this.loadMostCalledMethods();
        },
        error: (error: any) => {
          console.error('loadMostCalledMethods - Chyba při načítání instancí:', error);
          this.mostCalledMethods = [];
          this.loading = false;
        }
      });

      return;
    }

    // Příprava parametrů
    // Pokud instanceId je řetězec "null", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined
    let instanceId = this.aggregatedFilterData.instanceId;
    if (instanceId === null || instanceId === undefined || String(instanceId) === "null" || instanceId === 0) {
      instanceId = undefined;
      console.log('loadMostCalledMethods - Použití undefined pro instanceId (všechny instance)');
    }
    let fromDate: Date | undefined;
    let toDate: Date | undefined;

    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní
    if (!this.aggregatedFilterData.period) {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní
      toDate = today;
    }
    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
    else if (this.aggregatedFilterData.period !== 'custom') {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));
      toDate = today;
    }
    // Pokud je vybráno vlastní období, použijeme zadané hodnoty
    else {
      // Kontrola, zda fromDate a toDate jsou validní
      if (this.aggregatedFilterData.fromDate) {
        fromDate = new Date(this.aggregatedFilterData.fromDate);
      } else {
        // Výchozí hodnota, pokud fromDate není zadáno
        fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - 7);
      }

      if (this.aggregatedFilterData.toDate) {
        toDate = new Date(this.aggregatedFilterData.toDate);
      } else {
        // Výchozí hodnota, pokud toDate není zadáno
        toDate = new Date();
      }
    }

    try {
      console.log('loadMostCalledMethods - Volám API pro načtení dat', { instanceId, fromDate, toDate });

      const subscription = this.performanceService.getMostCalledMethods(instanceId, fromDate, toDate, 10).subscribe({
        next: (data: any) => {
          console.log('loadMostCalledMethods - Data úspěšně načtena:', data);

          if (!data) {
            console.error('loadMostCalledMethods - API vrátilo prázdná data');
            this.mostCalledMethods = [];
            this.loading = false;
            return;
          }

          this.mostCalledMethods = data;

          // Aplikujeme řazení na načtená data
          this.applySortToMethods();

          this.loading = false;
        },
        error: (error: any) => {
          console.error('Chyba při načítání nejčastěji volaných metod:', error);
          this.mostCalledMethods = [];
          this.loading = false;

          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách
          if (error.status === 0) {
            console.log('loadMostCalledMethods - Server neodpovídá, zkusím to znovu za 5 sekund');
            setTimeout(() => {
              console.log('loadMostCalledMethods - Opakuji volání API');
              this.loadMostCalledMethods();
            }, 5000);
          }
        },
        complete: () => {
          console.log('loadMostCalledMethods - Volání API dokončeno');
        }
      });

      this.subscriptions.push(subscription);
    } catch (error) {
      console.error('loadMostCalledMethods - Chyba při volání API:', error);
      this.mostCalledMethods = [];
      this.loading = false;
    }
  }

  /**
   * Načtení nejpomalejších metod
   */
  loadSlowestMethods(): void {
    console.log('loadSlowestMethods - Začínám načítat data');

    // Přidáme kontrolu, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('loadSlowestMethods - Načítání dat již probíhá, přeskakuji');
      return;
    }

    // Nastavíme příznak načítání
    this.loading = true;

    // Kontrola, zda máme načtené instance
    if (!this.instances || this.instances.length === 0) {
      console.warn('loadSlowestMethods - Instance nejsou načteny, načítám je');

      // Načtení instancí
      this.instanceService.getAll().subscribe({
        next: (data: any) => {
          console.log('loadSlowestMethods - Instance úspěšně načteny:', data);
          this.instances = data;

          // Po načtení instancí inicializujeme pole filtrů
          this.initFilterFields();

          // Pokračujeme v načítání dat
          this.loadSlowestMethods();
        },
        error: (error: any) => {
          console.error('loadSlowestMethods - Chyba při načítání instancí:', error);
          this.slowestMethods = [];
        }
      });

      return;
    }

    // Příprava parametrů
    // Pokud instanceId je řetězec "null", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined
    let instanceId = this.aggregatedFilterData.instanceId;
    if (instanceId === null || instanceId === undefined || String(instanceId) === "null" || instanceId === 0) {
      instanceId = undefined;
      console.log('loadSlowestMethods - Použití undefined pro instanceId (všechny instance)');
    }
    let fromDate: Date | undefined;
    let toDate: Date | undefined;

    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní
    if (!this.aggregatedFilterData.period) {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní
      toDate = today;
    }
    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
    else if (this.aggregatedFilterData.period !== 'custom') {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));
      toDate = today;
    }
    // Pokud je vybráno vlastní období, použijeme zadané hodnoty
    else {
      // Kontrola, zda fromDate a toDate jsou validní
      if (this.aggregatedFilterData.fromDate) {
        fromDate = new Date(this.aggregatedFilterData.fromDate);
      } else {
        // Výchozí hodnota, pokud fromDate není zadáno
        fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - 7);
      }

      if (this.aggregatedFilterData.toDate) {
        toDate = new Date(this.aggregatedFilterData.toDate);
      } else {
        // Výchozí hodnota, pokud toDate není zadáno
        toDate = new Date();
      }
    }

    try {
      console.log('loadSlowestMethods - Volám API pro načtení dat', { instanceId, fromDate, toDate });

      const subscription = this.performanceService.getSlowestMethods(instanceId, fromDate, toDate, 10).subscribe({
        next: (data: any) => {
          console.log('loadSlowestMethods - Data úspěšně načtena:', data);

          if (!data) {
            console.error('loadSlowestMethods - API vrátilo prázdná data');
            this.slowestMethods = [];
            this.loading = false;
            return;
          }

          this.slowestMethods = data;

          // Aplikujeme řazení na načtená data
          this.applySortToMethods();

          this.loading = false;
        },
        error: (error: any) => {
          console.error('Chyba při načítání nejpomalejších metod:', error);
          this.slowestMethods = [];
          this.loading = false;

          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách
          if (error.status === 0) {
            console.log('loadSlowestMethods - Server neodpovídá, zkusím to znovu za 5 sekund');
            setTimeout(() => {
              console.log('loadSlowestMethods - Opakuji volání API');
              this.loadSlowestMethods();
            }, 5000);
          }
        },
        complete: () => {
          console.log('loadSlowestMethods - Volání API dokončeno');
        }
      });

      this.subscriptions.push(subscription);
    } catch (error) {
      console.error('loadSlowestMethods - Chyba při volání API:', error);
      this.slowestMethods = [];
      this.loading = false;
    }
  }

  /**
   * Načtení všech metod
   */
  loadAllMethods(): void {
    // Příprava parametrů
    // Pokud instanceId je řetězec "null", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined
    let instanceId = this.aggregatedFilterData.instanceId;
    if (instanceId === null || instanceId === undefined || String(instanceId) === "null" || instanceId === 0) {
      instanceId = undefined;
      console.log('loadAllMethods - Použití undefined pro instanceId (všechny instance)');
    }
    let fromDate: Date | undefined;
    let toDate: Date | undefined;

    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní
    if (!this.aggregatedFilterData.period) {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní
      toDate = today;
    }
    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
    else if (this.aggregatedFilterData.period !== 'custom') {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));
      toDate = today;
    }
    // Pokud je vybráno vlastní období, použijeme zadané hodnoty
    else {
      // Kontrola, zda fromDate a toDate jsou validní
      if (this.aggregatedFilterData.fromDate) {
        fromDate = new Date(this.aggregatedFilterData.fromDate);
      } else {
        // Výchozí hodnota, pokud fromDate není zadáno
        fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - 7);
      }

      if (this.aggregatedFilterData.toDate) {
        toDate = new Date(this.aggregatedFilterData.toDate);
      } else {
        // Výchozí hodnota, pokud toDate není zadáno
        toDate = new Date();
      }
    }

    try {
      console.log('loadAllMethods - Volám API pro načtení dat', { instanceId, fromDate, toDate });

      const subscription = this.performanceService.getAllMethods(instanceId, fromDate, toDate).subscribe({
        next: (data: any) => {
          console.log('loadAllMethods - Data úspěšně načtena:', data);

          if (!data) {
            console.error('loadAllMethods - API vrátilo prázdná data');
            this.allMethods = [];
            return;
          }

          this.allMethods = data;

          // Aplikujeme řazení na načtená data
          this.applySortToMethods();
        },
        error: (error: any) => {
          console.error('Chyba při načítání všech metod:', error);
          this.allMethods = [];

          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách
          if (error.status === 0) {
            console.log('loadAllMethods - Server neodpovídá, zkusím to znovu za 5 sekund');
            setTimeout(() => {
              console.log('loadAllMethods - Opakuji volání API');
              this.loadAllMethods();
            }, 5000);
          }
        },
        complete: () => {
          console.log('loadAllMethods - Volání API dokončeno');
        }
      });

      this.subscriptions.push(subscription);
    } catch (error) {
      console.error('loadAllMethods - Chyba při volání API:', error);
      this.allMethods = [];
    }
  }

  /**
   * Načtení detailu metody
   */
  loadMethodDetail(): void {
    console.log('loadMethodDetail - Začínám načítat data');

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('loadMethodDetail - Načítání dat již probíhá, přeskakuji');
      return;
    }

    this.loading = true;
    this.error = '';

    // Kontrola, zda máme vybranou metodu nebo zadané povinné parametry
    if (!this.selectedMethod && (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName)) {
      console.warn('loadMethodDetail - Není vybrána metoda a nejsou zadány povinné parametry');
      // Necháme validaci na formuláři, nezobrazujeme chybový blok
      this.loading = false;
      return;
    }

    // Pokud máme vybranou metodu, ale nemáme zadané parametry, použijeme parametry z vybrané metody
    if (this.selectedMethod && (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName)) {
      console.log('loadMethodDetail - Použití parametrů z vybrané metody');
      this.methodDetailFilterData.className = this.selectedMethod.className;
      this.methodDetailFilterData.methodName = this.selectedMethod.methodName;

      // Zjištění aktuální hodnoty instanceId
      let instanceId = this.selectedMethod.instanceId;

      // Pokud instanceId je null nebo undefined, použijeme 0 (všechny instance)
      if (instanceId === null || instanceId === undefined || String(instanceId) === "null") {
        instanceId = 0;
      }

      // Nastavení instanceId
      this.methodDetailFilterData.instanceId = instanceId;

      // Uložení filtru do localStorage
      this.saveFilter('performance_method_detail', this.methodDetailFilterData);
    }

    // Kontrola, zda máme načtené instance
    if (!this.instances || this.instances.length === 0) {
      console.warn('loadMethodDetail - Instance nejsou načteny, načítám je');

      // Načtení instancí
      this.instanceService.getAll().subscribe({
        next: (data: any) => {
          console.log('loadMethodDetail - Instance úspěšně načteny:', data);
          this.instances = data;

          // Po načtení instancí inicializujeme pole filtrů
          this.initFilterFields();

          // Pokračujeme v načítání dat
          this.loadMethodDetail();
        },
        error: (error: any) => {
          console.error('loadMethodDetail - Chyba při načítání instancí:', error);
          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';
          this.loading = false;
        }
      });

      return;
    }

    // Kontrola, zda jsou vyplněny všechny povinné parametry
    if (!this.methodDetailFilterData.instanceId || !this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName) {
      // Necháme validaci na formuláři, nezobrazujeme chybový blok
      this.loading = false;
      return;
    }

    // Příprava parametrů
    // Pro metodu getMethodDetail potřebujeme instanceId jako number, null nebo 0
    let instanceId: number | null = this.methodDetailFilterData.instanceId ?? null;
    // Pokud instanceId je null, undefined nebo řetězec "null", použijeme 0 (všechny instance)
    if (instanceId === null || instanceId === undefined || String(instanceId) === "null") {
      instanceId = 0; // 0 = všechny instance
      console.log('loadMethodDetail - Použití hodnoty 0 pro instanceId (všechny instance)');

      // Aktualizujeme hodnotu v objektu filtru
      this.methodDetailFilterData.instanceId = 0;
    }

    console.log('loadMethodDetail - Finální hodnota instanceId:', instanceId);
    const className = this.methodDetailFilterData.className;
    const methodName = this.methodDetailFilterData.methodName;
    let fromDate: Date | undefined;
    let toDate: Date | undefined;

    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní
    if (!this.methodDetailFilterData.period) {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní
      toDate = today;
    }
    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
    else if (this.methodDetailFilterData.period !== 'custom') {
      const today = new Date();
      fromDate = new Date();
      fromDate.setDate(today.getDate() - Number(this.methodDetailFilterData.period));
      toDate = today;
    }
    // Pokud je vybráno vlastní období, použijeme zadané hodnoty
    else {
      // Kontrola, zda fromDate a toDate jsou validní
      if (this.methodDetailFilterData.fromDate) {
        fromDate = new Date(this.methodDetailFilterData.fromDate);
      } else {
        // Výchozí hodnota, pokud fromDate není zadáno
        fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - 7);
      }

      if (this.methodDetailFilterData.toDate) {
        toDate = new Date(this.methodDetailFilterData.toDate);
      } else {
        // Výchozí hodnota, pokud toDate není zadáno
        toDate = new Date();
      }
    }

    try {
      console.log('loadMethodDetail - Volám API pro načtení dat', { instanceId, className, methodName, fromDate, toDate });

      // Kontrola, zda jsou všechny povinné parametry nastaveny
      if (!className || !methodName) {
        console.error('loadMethodDetail - Nejsou zadány povinné parametry (třída nebo metoda)');
        this.error = 'Pro zobrazení detailu metody je nutné zadat název třídy a metody.';
        this.loading = false;
        return;
      }

      // Pokud instanceId je 0, použijeme null, aby se parametr instanceId vůbec neodeslal
      const finalInstanceId = instanceId === 0 ? null : instanceId;
      const subscription = this.performanceService.getMethodDetail(finalInstanceId, className, methodName, fromDate, toDate).subscribe({
        next: (data: any) => {
          console.log('loadMethodDetail - Data úspěšně načtena:', data);

          if (!data) {
            console.error('loadMethodDetail - API vrátilo prázdná data');
            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';
            this.loading = false;
            return;
          }

          // Aktualizace hodnot filtru podle načtených dat
          // Tím zajistíme, že hodnoty ve filtru budou odpovídat načteným datům
          if (data.className && data.methodName) {
            console.log('loadMethodDetail - Aktualizuji hodnoty filtru podle načtených dat:', {
              className: data.className,
              methodName: data.methodName
            });

            this.methodDetailFilterData.className = data.className;
            this.methodDetailFilterData.methodName = data.methodName;

            // Uložení filtru do localStorage
            this.saveFilter('performance_method_detail', this.methodDetailFilterData);
          }

          this.methodDetailData = data;
          this.methodDetailChartData = data;

          // Kontrola, zda jsou data prázdná
          const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0;
          this.hasMethodDetailChartData = hasData;

          // Vykreslení grafu
          this.renderMethodDetailChart(data);
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Chyba při načítání detailu metody:', error);
          this.error = 'Nepodařilo se načíst detail metody. ' +
                      (error.message ? error.message : 'Zkuste to prosím znovu.');
          this.loading = false;

          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách
          if (error.status === 0) {
            console.log('loadMethodDetail - Server neodpovídá, zkusím to znovu za 5 sekund');
            setTimeout(() => {
              console.log('loadMethodDetail - Opakuji volání API');
              this.loadMethodDetail();
            }, 5000);
          }
        },
        complete: () => {
          console.log('loadMethodDetail - Volání API dokončeno');
        }
      });

      this.subscriptions.push(subscription);
    } catch (error) {
      console.error('loadMethodDetail - Chyba při volání API:', error);
      this.error = 'Nepodařilo se načíst detail metody. Zkuste to prosím znovu.';
      this.loading = false;
    }
  }

  /**
   * Načtení dat podle vybraného typu metod
   */
  loadMethodsByType(): void {
    console.log('loadMethodsByType - Načítám data podle typu:', this.methodsDisplayType);

    switch (this.methodsDisplayType) {
      case 'slowest':
        this.loadSlowestMethods();
        break;
      case 'most-called':
        this.loadMostCalledMethods();
        break;
      case 'all':
        this.loadAllMethods();
        break;
      default:
        this.loadSlowestMethods();
        break;
    }
  }



  /**
   * Zpracování změny filtru pro metody
   */
  onMethodsFilterChange(filterData: PerformanceFilterData): void {
    console.log('onMethodsFilterChange - Přijatá data filtru:', filterData);

    // Kontrola, zda se filtr skutečně změnil
    const currentFilterStr = JSON.stringify(this.aggregatedFilterData);
    const newFilterStr = JSON.stringify(filterData);

    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat
    // Tím zajistíme, že tlačítko "Načíst data" bude vždy fungovat
    if (currentFilterStr === newFilterStr) {
      console.log('onMethodsFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');
    }

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('onMethodsFilterChange - Načítání dat již probíhá, přeskakuji');
      return;
    }

    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date
    if (filterData.period === 'custom') {
      if (filterData.fromDate && typeof filterData.fromDate === 'string') {
        filterData.fromDate = new Date(filterData.fromDate);
      }
      if (filterData.toDate && typeof filterData.toDate === 'string') {
        filterData.toDate = new Date(filterData.toDate);
      }
    }
    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)
    else if (filterData.fromDate && filterData.toDate) {
      if (typeof filterData.fromDate === 'string') {
        filterData.fromDate = new Date(filterData.fromDate);
      }
      if (typeof filterData.toDate === 'string') {
        filterData.toDate = new Date(filterData.toDate);
      }
    }

    // Aktualizace typu zobrazovaných metod
    if (filterData.methodsDisplayType) {
      this.methodsDisplayType = filterData.methodsDisplayType;
    } else {
      // Výchozí hodnota, pokud není zadána
      this.methodsDisplayType = 'slowest';
      filterData.methodsDisplayType = 'slowest';
    }

    this.aggregatedFilterData = filterData;
    console.log('onMethodsFilterChange - Zpracovaná data filtru:', this.aggregatedFilterData);
    this.loadMethodsByType();

    // Reinicializace popoverů po změně typu zobrazovaných metod
    setTimeout(() => {
      this.initPopovers();
    }, 500);
  }

  /**
   * Zpracování změny filtru pro detail metody
   */
  onMethodDetailFilterChange(filterData: PerformanceFilterData): void {
    console.log('onMethodDetailFilterChange - Přijatá data filtru:', filterData);

    // Kontrola, zda již neprobíhá načítání dat
    if (this.loading) {
      console.warn('onMethodDetailFilterChange - Načítání dat již probíhá, přeskakuji');
      return;
    }

    // Aktualizujeme data filtru
    this.methodDetailFilterData = { ...filterData };

    // Kontrola, zda je hodnota instanceId nastavena
    if (filterData.instanceId === null || filterData.instanceId === undefined || String(filterData.instanceId) === "null") {
      console.log('onMethodDetailFilterChange - Hodnota instanceId není nastavena, použijeme 0 (všechny instance)');
      this.methodDetailFilterData.instanceId = 0;
    } else {
      console.log('onMethodDetailFilterChange - Hodnota instanceId je nastavena:', filterData.instanceId);
    }

    // Pokud se změnil filtr, resetujeme vybranou metodu, aby se použily hodnoty z filtru
    if (this.selectedMethod) {
      if (this.selectedMethod.className !== this.methodDetailFilterData.className ||
          this.selectedMethod.methodName !== this.methodDetailFilterData.methodName ||
          this.selectedMethod.instanceId !== this.methodDetailFilterData.instanceId) {
        console.log('onMethodDetailFilterChange - Resetuji vybranou metodu, protože se změnil filtr');
        this.selectedMethod = null;
      }
    }

    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date
    if (filterData.period === 'custom') {
      if (filterData.fromDate && typeof filterData.fromDate === 'string') {
        this.methodDetailFilterData.fromDate = new Date(filterData.fromDate);
      }
      if (filterData.toDate && typeof filterData.toDate === 'string') {
        this.methodDetailFilterData.toDate = new Date(filterData.toDate);
      }
    }
    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)
    else if (filterData.fromDate && filterData.toDate) {
      if (typeof filterData.fromDate === 'string') {
        this.methodDetailFilterData.fromDate = new Date(filterData.fromDate);
      }
      if (typeof filterData.toDate === 'string') {
        this.methodDetailFilterData.toDate = new Date(filterData.toDate);
      }
    }

    console.log('onMethodDetailFilterChange - Zpracovaná data filtru:', this.methodDetailFilterData);

    // Kontrola, zda jsou vyplněny povinné parametry
    if (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName) {
      console.warn('onMethodDetailFilterChange - Nejsou vyplněny povinné parametry (třída nebo metoda), přeskakuji načítání dat');
      return;
    }

    // Kontrola, zda se filtr skutečně změnil
    const currentFilterStr = JSON.stringify(this.methodDetailFilterData);
    const newFilterStr = JSON.stringify(filterData);

    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat
    // Tím zajistíme, že tlačítko "Načíst data" bude vždy fungovat
    if (currentFilterStr === newFilterStr) {
      console.log('onMethodDetailFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');
    } else {
      // Pokud se filtr změnil, uložíme ho do localStorage
      this.saveFilter('performance_method_detail', this.methodDetailFilterData);
    }

    // Resetujeme indikátor, zda jsou k dispozici data pro graf
    this.hasMethodDetailChartData = true;

    // Vždy načteme data a překreslíme graf
    this.loadMethodDetail();
  }

  /**
   * Zobrazení detailu metody
   */
  showMethodDetail(method: any): void {
    console.log('showMethodDetail - Zobrazuji detail metody:', method);
    this.selectedMethod = method;

    // Zjištění aktuální hodnoty instanceId z aktivního filtru podle záložky
    let instanceId = method.instanceId;

    // Pro záložky "aggregated" a "methods" použij hodnotu z this.aggregatedFilterData.instanceId
    if (this.activeTab === 'aggregated' || this.activeTab === 'methods') {
      if (this.aggregatedFilterData && this.aggregatedFilterData.instanceId !== undefined) {
        instanceId = this.aggregatedFilterData.instanceId;
        // Pokud instanceId je řetězec "null" nebo hodnota null, použijeme 0 (všechny instance)
        if (instanceId === null || String(instanceId) === "null") {
          instanceId = 0;
        }
      }
    }
    // Pro záložku "versions-comparison" použij hodnotu z this.versionsComparisonFilterData.instanceId
    else if (this.activeTab === 'versions-comparison') {
      if (this.versionsComparisonFilterData && this.versionsComparisonFilterData.instanceId !== undefined) {
        instanceId = this.versionsComparisonFilterData.instanceId;
        // Pokud instanceId je řetězec "null" nebo hodnota null, použijeme undefined
        if (instanceId === null || String(instanceId) === "null") {
          instanceId = undefined;
        }
      }
    }
    // Pro záložku "instances-comparison" a ostatní záložky použij hodnotu z method.instanceId
    // Toto je již nastaveno výchozí hodnotou instanceId = method.instanceId

    // Nastavení hodnot filtru
    // Pro metodu getMethodDetail potřebujeme instanceId jako number nebo null
    // Pokud instanceId je undefined, použijeme hodnotu z metody
    if (instanceId === undefined) {
      instanceId = method.instanceId;
    }

    // Pokud instanceId je null, nastavíme ho na 0 (všechny instance)
    if (instanceId === null) {
      instanceId = 0;
    }

    console.log('showMethodDetail - Nastavuji hodnoty filtru:', {
      instanceId: instanceId,
      className: method.className,
      methodName: method.methodName
    });

    // Aktualizace hodnot filtru
    this.methodDetailFilterData = {
      ...this.methodDetailFilterData,
      instanceId: instanceId,
      className: method.className,
      methodName: method.methodName
    };

    // Uložení filtru do localStorage, aby byl dostupný i po obnovení stránky
    this.saveFilter('performance_method_detail', this.methodDetailFilterData);

    // Resetujeme indikátor, zda jsou k dispozici data pro graf
    this.hasMethodDetailChartData = true;

    // Přepnutí na záložku s detailem metody
    this.activeTab = 'method-detail';

    // Uložení aktivní záložky do localStorage
    this.saveActiveTab('method-detail');

    // Načtení detailu metody
    this.loadMethodDetail();
  }

  /**
   * Uložení nastavení viditelnosti metrik do localStorage
   */
  saveMetricsVisibility(chartType: string, datasets: any[]): void {
    try {
      const visibility: Record<string, boolean> = {};

      // Procházíme všechny datasety a ukládáme jejich viditelnost
      datasets.forEach(dataset => {
        if (dataset.label) {
          // Použijeme metodu isDatasetVisible z Chart.js, pokud je k dispozici
          if (this.aggregatedPerformanceChart && chartType === 'aggregated') {
            const index = datasets.indexOf(dataset);
            visibility[dataset.label] = this.aggregatedPerformanceChart.isDatasetVisible(index);
          } else if (this.instancesComparisonChart && chartType === 'instances-comparison') {
            const index = datasets.indexOf(dataset);
            visibility[dataset.label] = this.instancesComparisonChart.isDatasetVisible(index);
          } else if (this.versionsComparisonChart && chartType === 'versions-comparison') {
            const index = datasets.indexOf(dataset);
            visibility[dataset.label] = this.versionsComparisonChart.isDatasetVisible(index);
          } else if (this.methodDetailChart && chartType === 'method-detail') {
            const index = datasets.indexOf(dataset);
            visibility[dataset.label] = this.methodDetailChart.isDatasetVisible(index);
          } else {
            // Fallback na původní logiku
            visibility[dataset.label] = !dataset.hidden;
          }
        }
      });

      // Uložení do localStorage
      localStorage.setItem(`metrics_visibility_${chartType}`, JSON.stringify(visibility));
      console.log(`Nastavení viditelnosti metrik pro ${chartType} uloženo:`, visibility);
    } catch (error) {
      console.error(`Chyba při ukládání nastavení viditelnosti metrik pro ${chartType}:`, error);
    }
  }

  /**
   * Uložení aktivní záložky do localStorage
   */
  saveActiveTab(tabId: string): void {
    try {
      localStorage.setItem('performance_active_tab', tabId);
      console.log(`Uložena aktivní záložka: ${tabId}`);
    } catch (error) {
      console.error(`Chyba při ukládání aktivní záložky:`, error);
    }
  }

  /**
   * Načtení aktivní záložky z localStorage
   */
  loadActiveTab(): string | null {
    try {
      const activeTab = localStorage.getItem('performance_active_tab');
      console.log(`Načtena aktivní záložka z localStorage: ${activeTab}`);
      return activeTab;
    } catch (error) {
      console.error(`Chyba při načítání aktivní záložky:`, error);
      return null;
    }
  }

  /**
   * Uložení filtru do localStorage
   * @param entityType Typ entity, pro kterou se ukládá filtr
   * @param filterData Data filtru
   */
  saveFilter(entityType: string, filterData: any): void {
    try {
      const lastFilterKey = `last_filter_${entityType}`;
      localStorage.setItem(lastFilterKey, JSON.stringify(filterData));
      console.log(`Uložen filtr pro ${entityType} do localStorage:`, filterData);
    } catch (error) {
      console.error(`Chyba při ukládání filtru pro ${entityType} do localStorage:`, error);
    }
  }

  /**
   * Zpracování kliknutí na záhlaví sloupce pro řazení
   * @param column Název sloupce, podle kterého se má řadit
   */
  onSort(column: string): void {
    console.log(`onSort - Řazení podle sloupce: ${column}`);

    const newSortingState = this.tableSortingService.createNewSortingStateWithDefaults(
      this.sortColumn,
      this.sortDirection,
      column,
      ['totalCount', 'min', 'max', 'avg', 'median', 'percentil95'] // číselné sloupce
    );

    // Aktualizace lokálního stavu
    this.sortColumn = newSortingState.column;
    this.sortDirection = newSortingState.direction;

    // Uložení do localStorage podle aktivní záložky
    const tableName = this.activeTab === 'methods' ? 'methods' : 'methods';
    this.tableSortingService.saveSortingState('performance', tableName, newSortingState);

    console.log(`onSort - Nové řazení: ${this.sortColumn} ${this.sortDirection}`);

    // Aplikujeme řazení na data
    this.applySortToMethods();
  }

  /**
   * Aplikace řazení na data metod
   */
  applySortToMethods(): void {
    console.log(`applySortToMethods - Aplikuji řazení: ${this.sortColumn} ${this.sortDirection}`);

    // Řazení nejpomalejších metod
    if (this.slowestMethods && this.slowestMethods.length > 0) {
      this.slowestMethods = this.sortMethods([...this.slowestMethods]);
    }

    // Řazení nejčastěji volaných metod
    if (this.mostCalledMethods && this.mostCalledMethods.length > 0) {
      this.mostCalledMethods = this.sortMethods([...this.mostCalledMethods]);
    }

    // Řazení všech metod
    if (this.allMethods && this.allMethods.length > 0) {
      this.allMethods = this.sortMethods([...this.allMethods]);
    }
  }

  /**
   * Řazení pole metod podle aktuálního sloupce a směru
   * @param methods Pole metod k seřazení
   * @returns Seřazené pole metod
   */
  sortMethods(methods: any[]): any[] {
    return methods.sort((a, b) => {
      let valueA: any;
      let valueB: any;

      // Získání hodnot pro porovnání podle názvu sloupce
      switch (this.sortColumn) {
        case 'instanceName':
          valueA = a.instanceName?.toLowerCase() || '';
          valueB = b.instanceName?.toLowerCase() || '';
          break;
        case 'className':
          valueA = a.className?.toLowerCase() || '';
          valueB = b.className?.toLowerCase() || '';
          break;
        case 'methodName':
          valueA = a.methodName?.toLowerCase() || '';
          valueB = b.methodName?.toLowerCase() || '';
          break;
        case 'totalCount':
          valueA = a.totalCount || 0;
          valueB = b.totalCount || 0;
          break;
        case 'min':
          valueA = a.min || 0;
          valueB = b.min || 0;
          break;
        case 'max':
          valueA = a.max || 0;
          valueB = b.max || 0;
          break;
        case 'avg':
          valueA = a.avg || 0;
          valueB = b.avg || 0;
          break;
        case 'median':
          valueA = a.median || 0;
          valueB = b.median || 0;
          break;
        case 'percentil95':
          valueA = a.percentil95 || 0;
          valueB = b.percentil95 || 0;
          break;
        default:
          valueA = 0;
          valueB = 0;
      }

      // Porovnání hodnot podle směru řazení
      if (this.sortDirection === 'asc') {
        if (typeof valueA === 'string') {
          return valueA.localeCompare(valueB);
        } else {
          return valueA - valueB;
        }
      } else {
        if (typeof valueA === 'string') {
          return valueB.localeCompare(valueA);
        } else {
          return valueB - valueA;
        }
      }
    });
  }

  /**
   * Načtení stavu třídění z localStorage
   */
  private loadSortingState(): void {
    const defaultSort = { column: 'avg', direction: 'desc' as 'asc' | 'desc' };
    const savedSortingState = this.tableSortingService.loadSortingState('performance', 'methods', defaultSort);

    if (savedSortingState) {
      this.sortColumn = savedSortingState.column;
      this.sortDirection = savedSortingState.direction;
    }
  }

  /**
   * Načtení nastavení viditelnosti metrik z localStorage
   */
  loadMetricsVisibility(chartType: string): any {
    try {
      const visibilityJson = localStorage.getItem(`metrics_visibility_${chartType}`);

      if (visibilityJson) {
        const visibility = JSON.parse(visibilityJson);
        console.log(`Načteno nastavení viditelnosti metrik pro ${chartType}:`, visibility);
        return visibility;
      }
    } catch (error) {
      console.error(`Chyba při načítání nastavení viditelnosti metrik pro ${chartType}:`, error);
    }

    return null;
  }

  /**
   * Vykreslení grafu agregovaných výkonnostních metrik
   */
  renderAggregatedPerformanceChart(data: any): void {
    console.log('renderAggregatedPerformanceChart - data:', data);

    // Nastavíme příznak, že se graf vykresluje
    this.renderingAggregatedChart = true;

    // Uložíme data pro pozdější vykreslení
    this.aggregatedPerformanceData = data;

    // Příprava dat pro graf
    const chartData = this.prepareAggregatedPerformanceChartData(data);
    console.log('renderAggregatedPerformanceChart - chartData:', chartData);

    // Kontrola, zda jsou data prázdná
    const hasData = chartData.labels && Array.isArray(chartData.labels) && chartData.labels.length > 0;
    console.log('renderAggregatedPerformanceChart - hasData:', hasData, 'labels:', chartData.labels);

    if (!hasData) {
      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování
      this.hasAggregatedChartData = false;
      this.renderingAggregatedChart = false;

      // Zničíme existující graf, pokud existuje
      if (this.aggregatedPerformanceChart) {
        this.aggregatedPerformanceChart.destroy();
        this.aggregatedPerformanceChart = null;
      }

      return;
    }

    // Pokud máme data, nastavíme indikátor
    this.hasAggregatedChartData = true;

    // Kontrola, zda je reference na canvas element k dispozici
    if (!this.aggregatedPerformanceChartRef) {
      console.warn('renderAggregatedPerformanceChart - Reference na canvas element není k dispozici, zkusím později');

      // Zkusíme počkat a zkusit to znovu po delším timeoutu
      setTimeout(() => {
        console.log('renderAggregatedPerformanceChart - Zkouším znovu po delším timeoutu (2000ms)');
        if (this.aggregatedPerformanceChartRef) {
          this.renderAggregatedPerformanceChart(data);
        } else {
          console.error('renderAggregatedPerformanceChart - Canvas element stále není k dispozici po 2000ms!');
          this.renderingAggregatedChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci
        }
      }, 2000);

      return;
    }

    console.log('renderAggregatedPerformanceChart - Reference na canvas element je k dispozici');

    try {
      // Kontrola, zda je canvas element v DOM
      if (!document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {
        console.warn('renderAggregatedPerformanceChart - Canvas element není v DOM, zkusím později');

        // Zkusíme počkat a zkusit to znovu po delším timeoutu
        setTimeout(() => {
          console.log('renderAggregatedPerformanceChart - Zkouším znovu po delším timeoutu (element není v DOM)');
          if (this.aggregatedPerformanceChartRef && document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {
            this.renderAggregatedPerformanceChart(data);
          } else {
            console.error('renderAggregatedPerformanceChart - Canvas element stále není v DOM po 2000ms!');
          }
        }, 2000);

        return;
      }

      // Získání kontextu canvas elementu
      const ctx = this.aggregatedPerformanceChartRef.nativeElement.getContext('2d');
      if (!ctx) {
        console.error('renderAggregatedPerformanceChart - Nepodařilo se získat kontext canvas elementu!');
        return;
      }
      console.log('renderAggregatedPerformanceChart - Kontext canvas elementu je k dispozici');

    if (this.aggregatedPerformanceChart) {
      console.log('renderAggregatedPerformanceChart - Ničím existující graf');
      this.aggregatedPerformanceChart.destroy();
    }

    // Kontrola, zda jsou všechna pole dat stejné délky
    const labelsLength = chartData.labels.length;
    const avgDataLength = chartData.avgData.length;
    const medianDataLength = chartData.medianData.length;
    const p95DataLength = chartData.p95Data.length;
    const p99DataLength = chartData.p99Data.length;
    const minDataLength = chartData.minData.length;
    const maxDataLength = chartData.maxData.length;
    const totalCountDataLength = chartData.totalCountData.length;

    console.log('renderAggregatedPerformanceChart - Délky polí:', {
      labelsLength,
      avgDataLength,
      medianDataLength,
      p95DataLength,
      p99DataLength,
      minDataLength,
      maxDataLength,
      totalCountDataLength
    });

    // Pokud některé pole má jinou délku než labels, upravíme ho
    if (avgDataLength !== labelsLength) {
      console.warn('renderAggregatedPerformanceChart - avgData má jinou délku než labels, upravuji');
      chartData.avgData = this.adjustArrayLength(chartData.avgData, labelsLength);
    }
    if (medianDataLength !== labelsLength) {
      console.warn('renderAggregatedPerformanceChart - medianData má jinou délku než labels, upravuji');
      chartData.medianData = this.adjustArrayLength(chartData.medianData, labelsLength);
    }
    if (p95DataLength !== labelsLength) {
      console.warn('renderAggregatedPerformanceChart - p95Data má jinou délku než labels, upravuji');
      chartData.p95Data = this.adjustArrayLength(chartData.p95Data, labelsLength);
    }
    if (p99DataLength !== labelsLength) {
      console.warn('renderAggregatedPerformanceChart - p99Data má jinou délku než labels, upravuji');
      chartData.p99Data = this.adjustArrayLength(chartData.p99Data, labelsLength);
    }
    if (minDataLength !== labelsLength) {
      console.warn('renderAggregatedPerformanceChart - minData má jinou délku než labels, upravuji');
      chartData.minData = this.adjustArrayLength(chartData.minData, labelsLength);
    }
    if (maxDataLength !== labelsLength) {
      console.warn('renderAggregatedPerformanceChart - maxData má jinou délku než labels, upravuji');
      chartData.maxData = this.adjustArrayLength(chartData.maxData, labelsLength);
    }
    if (totalCountDataLength !== labelsLength) {
      console.warn('renderAggregatedPerformanceChart - totalCountData má jinou délku než labels, upravuji');
      chartData.totalCountData = this.adjustArrayLength(chartData.totalCountData, labelsLength);
    }

    // Pokud jsou data k dispozici, vykreslíme graf
    try {
      console.log('renderAggregatedPerformanceChart - Vytvářím nový graf');

      // Kontrola, zda je canvas element stále platný
      if (!this.aggregatedPerformanceChartRef || !document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {
        console.error('renderAggregatedPerformanceChart - Canvas element již není platný!');
        return;
      }

      // Kontrola, zda má canvas element rozměry
      const width = this.aggregatedPerformanceChartRef.nativeElement.width;
      const height = this.aggregatedPerformanceChartRef.nativeElement.height;
      console.log('renderAggregatedPerformanceChart - Canvas rozměry:', { width, height });

      // Načtení nastavení viditelnosti metrik z localStorage
      const visibility = this.loadMetricsVisibility('aggregated');

      // Vytvoření datasetů s nastavením viditelnosti
      const datasets = [
        {
          label: 'Průměr (ms)',
          data: chartData.avgData,
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['Průměr (ms)'] : false
        },
        {
          label: 'Medián (ms)',
          data: chartData.medianData,
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['Medián (ms)'] : false
        },
        {
          label: '95. percentil (ms)',
          data: chartData.p95Data,
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['95. percentil (ms)'] : false
        },
        {
          label: '99. percentil (ms)',
          data: chartData.p99Data,
          borderColor: 'rgba(255, 159, 64, 1)',
          backgroundColor: 'rgba(255, 159, 64, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['99. percentil (ms)'] : true
        },
        {
          label: 'Minimum (ms)',
          data: chartData.minData,
          borderColor: 'rgba(153, 102, 255, 1)',
          backgroundColor: 'rgba(153, 102, 255, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['Minimum (ms)'] : true
        },
        {
          label: 'Maximum (ms)',
          data: chartData.maxData,
          borderColor: 'rgba(255, 205, 86, 1)',
          backgroundColor: 'rgba(255, 205, 86, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['Maximum (ms)'] : true
        },
        {
          label: 'Počet volání',
          data: chartData.totalCountData,
          borderColor: 'rgba(201, 203, 207, 1)',
          backgroundColor: 'rgba(201, 203, 207, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['Počet volání'] : true,
          yAxisID: 'y1'
        }
      ];

      // Určení, které osy Y mají být zobrazené na základě viditelných metrik
      const visibleAxes = this.getVisibleAxes(datasets);

      // Vytvoření nového grafu
      this.aggregatedPerformanceChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: chartData.labels,
          datasets: datasets
        },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: chartData.title || 'Výkon v čase'
          },
          tooltip: {
            mode: 'index',
            intersect: false
          },
          legend: {
            position: 'top',
            labels: {
              boxWidth: 12
            },
            onClick: (_e, legendItem, legend) => {
              // Standardní chování - přepnutí viditelnosti datasetu
              const index = legendItem.datasetIndex;
              const ci = legend.chart;
              if (index !== undefined) {
                if (ci.isDatasetVisible(index)) {
                  ci.hide(index);
                  legendItem.hidden = true;
                } else {
                  ci.show(index);
                  legendItem.hidden = false;
                }

                // Aktualizace viditelnosti os Y na základě nově viditelných metrik
                this.updateAxisVisibility(ci);

                // Počkáme, až se graf aktualizuje
                setTimeout(() => {
                  // Uložení nastavení viditelnosti do localStorage
                  this.saveMetricsVisibility('aggregated', ci.data.datasets);
                }, 0);
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Datum'
            }
          },
          y: {
            display: visibleAxes.showLeftAxis,
            title: {
              display: visibleAxes.showLeftAxis,
              text: 'Doba odezvy (ms)'
            },
            beginAtZero: true
          },
          y1: {
            display: visibleAxes.showRightAxis,
            position: 'right',
            title: {
              display: visibleAxes.showRightAxis,
              text: 'Počet volání'
            },
            beginAtZero: true,
            grid: {
              drawOnChartArea: !visibleAxes.showLeftAxis // Zobrazit mřížku pouze když levá osa není viditelná
            }
          }
        }
      }
    });
      console.log('renderAggregatedPerformanceChart - Graf úspěšně vytvořen');

      // Uložení aktuálního nastavení viditelnosti metrik do localStorage
      setTimeout(() => {
        if (this.aggregatedPerformanceChart) {
          this.saveMetricsVisibility('aggregated', this.aggregatedPerformanceChart.data.datasets);
        }
        // Ukončíme vykreslování
        this.renderingAggregatedChart = false;
      }, 100);
    } catch (error) {
      console.error('renderAggregatedPerformanceChart - Chyba při vytváření grafu:', error);
      this.renderingAggregatedChart = false; // Ukončíme vykreslování v případě chyby
    }
  } catch (error) {
    console.error('renderAggregatedPerformanceChart - Chyba při získávání kontextu canvas elementu:', error);
    this.renderingAggregatedChart = false; // Ukončíme vykreslování v případě chyby
  }
  }

  /**
   * Vykreslení grafu srovnání výkonu metod mezi instancemi
   */
  renderInstancesComparisonChart(data: any): void {
    console.log('renderInstancesComparisonChart - data:', data);

    // Nastavíme příznak, že se graf vykresluje
    this.renderingInstancesComparisonChart = true;

    // Uložíme data pro pozdější vykreslení
    this.instancesComparisonData = data;

    // Kontrola, zda jsou data prázdná
    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0 &&
                    data.datasets && Array.isArray(data.datasets) && data.datasets.length > 0;

    if (!hasData) {
      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování
      this.hasInstancesComparisonChartData = false;
      this.renderingInstancesComparisonChart = false;

      // Zničíme existující graf, pokud existuje
      if (this.instancesComparisonChart) {
        this.instancesComparisonChart.destroy();
        this.instancesComparisonChart = null;
      }

      return;
    }

    // Pokud máme data, nastavíme indikátor
    this.hasInstancesComparisonChartData = true;

    // Kontrola, zda je reference na canvas element k dispozici
    if (!this.instancesComparisonChartRef) {
      console.warn('renderInstancesComparisonChart - Reference na canvas element není k dispozici, zkusím později');

      // Zkusíme počkat a zkusit to znovu po delším timeoutu
      setTimeout(() => {
        console.log('renderInstancesComparisonChart - Zkouším znovu po delším timeoutu (2000ms)');
        if (this.instancesComparisonChartRef) {
          this.renderInstancesComparisonChart(data);
        } else {
          console.error('renderInstancesComparisonChart - Canvas element stále není k dispozici po 2000ms!');
          this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci
        }
      }, 2000);

      return;
    }

    console.log('renderInstancesComparisonChart - Reference na canvas element je k dispozici');

    try {
      // Kontrola, zda je canvas element v DOM
      if (!document.body.contains(this.instancesComparisonChartRef.nativeElement)) {
        console.warn('renderInstancesComparisonChart - Canvas element není v DOM, zkusím později');

        // Zkusíme počkat a zkusit to znovu po delším timeoutu
        setTimeout(() => {
          console.log('renderInstancesComparisonChart - Zkouším znovu po delším timeoutu (element není v DOM)');
          if (this.instancesComparisonChartRef && document.body.contains(this.instancesComparisonChartRef.nativeElement)) {
            this.renderInstancesComparisonChart(data);
          } else {
            console.error('renderInstancesComparisonChart - Canvas element stále není v DOM po 2000ms!');
          }
        }, 2000);

        return;
      }

      // Získání kontextu canvas elementu
      const ctx = this.instancesComparisonChartRef.nativeElement.getContext('2d');
      if (!ctx) {
        console.error('renderInstancesComparisonChart - Nepodařilo se získat kontext canvas elementu!');
        return;
      }
      console.log('renderInstancesComparisonChart - Kontext canvas elementu je k dispozici');

      if (this.instancesComparisonChart) {
        console.log('renderInstancesComparisonChart - Ničím existující graf');
        this.instancesComparisonChart.destroy();
      }

    try {
      console.log('renderInstancesComparisonChart - Vytvářím nový graf');

      // Kontrola, zda je canvas element stále platný
      if (!this.instancesComparisonChartRef || !document.body.contains(this.instancesComparisonChartRef.nativeElement)) {
        console.error('renderInstancesComparisonChart - Canvas element již není platný!');
        return;
      }

      // Kontrola, zda má canvas element rozměry
      const width = this.instancesComparisonChartRef.nativeElement.width;
      const height = this.instancesComparisonChartRef.nativeElement.height;
      console.log('renderInstancesComparisonChart - Canvas rozměry:', { width, height });

      // Načtení nastavení viditelnosti metrik z localStorage
      const visibility = this.loadMetricsVisibility('instances-comparison');

      // Aplikace nastavení viditelnosti na datasety
      if (visibility && data.datasets) {
        data.datasets.forEach((dataset: any) => {
          if (dataset.label && visibility[dataset.label] !== undefined) {
            dataset.hidden = !visibility[dataset.label];
          }
        });
      }

      // Uložení originálních labels pro tooltip
      const originalLabels = [...data.labels];

      // Konfigurace pro zkrácené popisky na ose X
      const truncatedLabelsConfig = this.createTruncatedLabelsConfig(originalLabels, {
        enabled: true,        // Zapnout zkrácení popisků
        maxLength: 30,        // Maximální délka popisku
        showFullInTooltip: true // Zobrazit plný název v tooltip
      });

      // Základní konfigurace grafu
      const baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: data.title || 'Srovnání výkonu metod mezi instancemi'
          },
          tooltip: {
            mode: 'index',
            intersect: false
          },
          legend: {
            position: 'top',
            labels: {
              boxWidth: 12
            },
            onClick: (_e: any, legendItem: any, legend: any) => {
              // Standardní chování - přepnutí viditelnosti datasetu
              const index = legendItem.datasetIndex;
              const ci = legend.chart;
              if (index !== undefined) {
                if (ci.isDatasetVisible(index)) {
                  ci.hide(index);
                  legendItem.hidden = true;
                } else {
                  ci.show(index);
                  legendItem.hidden = false;
                }

                // Počkáme, až se graf aktualizuje
                setTimeout(() => {
                  // Uložení nastavení viditelnosti do localStorage
                  this.saveMetricsVisibility('instances-comparison', ci.data.datasets);
                }, 0);

                // Překreslení grafu
                ci.update();
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Metoda'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Doba odezvy (ms)'
            },
            beginAtZero: true
          }
        }
      };

      // Sloučení s konfigurací pro zkrácené popisky
      const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);

      // Vytvoření nového grafu
      this.instancesComparisonChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: data.labels,
          datasets: data.datasets
        },
        options: finalOptions
      });
      console.log('renderInstancesComparisonChart - Graf úspěšně vytvořen');

      // Uložení aktuálního nastavení viditelnosti metrik do localStorage
      setTimeout(() => {
        if (this.instancesComparisonChart) {
          this.saveMetricsVisibility('instances-comparison', this.instancesComparisonChart.data.datasets);
        }
        // Ukončíme vykreslování
        this.renderingInstancesComparisonChart = false;
      }, 100);
    } catch (error) {
      console.error('renderInstancesComparisonChart - Chyba při vytváření grafu:', error);
      this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování v případě chyby
    }
  } catch (error) {
    console.error('renderInstancesComparisonChart - Chyba při získávání kontextu canvas elementu:', error);
    this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování v případě chyby
  }
  }

  /**
   * Vykreslení grafu srovnání výkonu metod mezi verzemi
   */
  renderVersionsComparisonChart(data: any): void {
    console.log('renderVersionsComparisonChart - data:', data);

    // Nastavíme příznak, že se graf vykresluje
    this.renderingVersionsComparisonChart = true;

    // Uložíme data pro pozdější vykreslení
    this.versionsComparisonData = data;

    // Kontrola, zda jsou data prázdná
    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0 &&
                    data.datasets && Array.isArray(data.datasets) && data.datasets.length > 0;

    if (!hasData) {
      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování
      this.hasVersionsComparisonChartData = false;
      this.renderingVersionsComparisonChart = false;

      // Zničíme existující graf, pokud existuje
      if (this.versionsComparisonChart) {
        this.versionsComparisonChart.destroy();
        this.versionsComparisonChart = null;
      }

      return;
    }

    // Pokud máme data, nastavíme indikátor
    this.hasVersionsComparisonChartData = true;

    // Kontrola, zda je reference na canvas element k dispozici
    if (!this.versionsComparisonChartRef) {
      console.warn('renderVersionsComparisonChart - Reference na canvas element není k dispozici, zkusím později');

      // Zkusíme počkat a zkusit to znovu po delším timeoutu
      setTimeout(() => {
        console.log('renderVersionsComparisonChart - Zkouším znovu po delším timeoutu (2000ms)');
        if (this.versionsComparisonChartRef) {
          this.renderVersionsComparisonChart(data);
        } else {
          console.error('renderVersionsComparisonChart - Canvas element stále není k dispozici po 2000ms!');
          this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci
        }
      }, 2000);

      return;
    }

    console.log('renderVersionsComparisonChart - Reference na canvas element je k dispozici');

    try {
      // Kontrola, zda je canvas element v DOM
      if (!document.body.contains(this.versionsComparisonChartRef.nativeElement)) {
        console.warn('renderVersionsComparisonChart - Canvas element není v DOM, zkusím později');

        // Zkusíme počkat a zkusit to znovu po delším timeoutu
        setTimeout(() => {
          console.log('renderVersionsComparisonChart - Zkouším znovu po delším timeoutu (element není v DOM)');
          if (this.versionsComparisonChartRef && document.body.contains(this.versionsComparisonChartRef.nativeElement)) {
            this.renderVersionsComparisonChart(data);
          } else {
            console.error('renderVersionsComparisonChart - Canvas element stále není v DOM po 2000ms!');
          }
        }, 2000);

        return;
      }

      // Získání kontextu canvas elementu
      const ctx = this.versionsComparisonChartRef.nativeElement.getContext('2d');
      if (!ctx) {
        console.error('renderVersionsComparisonChart - Nepodařilo se získat kontext canvas elementu!');
        return;
      }
      console.log('renderVersionsComparisonChart - Kontext canvas elementu je k dispozici');

      if (this.versionsComparisonChart) {
        console.log('renderVersionsComparisonChart - Ničím existující graf');
        this.versionsComparisonChart.destroy();
      }

    try {
      console.log('renderVersionsComparisonChart - Vytvářím nový graf');

      // Kontrola, zda je canvas element stále platný
      if (!this.versionsComparisonChartRef || !document.body.contains(this.versionsComparisonChartRef.nativeElement)) {
        console.error('renderVersionsComparisonChart - Canvas element již není platný!');
        return;
      }

      // Kontrola, zda má canvas element rozměry
      const width = this.versionsComparisonChartRef.nativeElement.width;
      const height = this.versionsComparisonChartRef.nativeElement.height;
      console.log('renderVersionsComparisonChart - Canvas rozměry:', { width, height });

      // Načtení nastavení viditelnosti metrik z localStorage
      const visibility = this.loadMetricsVisibility('versions-comparison');

      // Aplikace nastavení viditelnosti na datasety
      if (visibility && data.datasets) {
        data.datasets.forEach((dataset: any) => {
          if (dataset.label && visibility[dataset.label] !== undefined) {
            dataset.hidden = !visibility[dataset.label];
          }
        });
      }

      // Uložení originálních labels pro tooltip
      const originalLabels = [...data.labels];

      // Konfigurace pro zkrácené popisky na ose X
      const truncatedLabelsConfig = this.createTruncatedLabelsConfig(originalLabels, {
        enabled: true,        // Zapnout zkrácení popisků
        maxLength: 30,        // Maximální délka popisku
        showFullInTooltip: true // Zobrazit plný název v tooltip
      });

      // Základní konfigurace grafu
      const baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: data.title || 'Srovnání výkonu metod mezi verzemi'
          },
          tooltip: {
            mode: 'index',
            intersect: false
          },
          legend: {
            position: 'top',
            labels: {
              boxWidth: 12
            },
            onClick: (_e: any, legendItem: any, legend: any) => {
              // Standardní chování - přepnutí viditelnosti datasetu
              const index = legendItem.datasetIndex;
              const ci = legend.chart;
              if (index !== undefined) {
                if (ci.isDatasetVisible(index)) {
                  ci.hide(index);
                  legendItem.hidden = true;
                } else {
                  ci.show(index);
                  legendItem.hidden = false;
                }

                // Počkáme, až se graf aktualizuje
                setTimeout(() => {
                  // Uložení nastavení viditelnosti do localStorage
                  this.saveMetricsVisibility('versions-comparison', ci.data.datasets);
                }, 0);

                // Překreslení grafu
                ci.update();
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Metoda'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Doba odezvy (ms)'
            },
            beginAtZero: true
          }
        }
      };

      // Sloučení s konfigurací pro zkrácené popisky
      const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);

      // Vytvoření nového grafu
      this.versionsComparisonChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: data.labels,
          datasets: data.datasets
        },
        options: finalOptions
      });
      console.log('renderVersionsComparisonChart - Graf úspěšně vytvořen');

      // Uložení aktuálního nastavení viditelnosti metrik do localStorage
      setTimeout(() => {
        if (this.versionsComparisonChart) {
          this.saveMetricsVisibility('versions-comparison', this.versionsComparisonChart.data.datasets);
        }
        // Ukončíme vykreslování
        this.renderingVersionsComparisonChart = false;
      }, 100);
    } catch (error) {
      console.error('renderVersionsComparisonChart - Chyba při vytváření grafu:', error);
      this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování v případě chyby
    }
  } catch (error) {
    console.error('renderVersionsComparisonChart - Chyba při získávání kontextu canvas elementu:', error);
    this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování v případě chyby
  }
  }

  /**
   * Vykreslení grafu detailu metody
   */
  renderMethodDetailChart(data: any): void {
    console.log('renderMethodDetailChart - data:', data);

    // Nastavíme příznak, že se graf vykresluje
    this.renderingMethodDetailChart = true;

    // Uložíme data pro pozdější vykreslení
    this.methodDetailChartData = data;

    // Kontrola, zda jsou data prázdná
    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0;

    if (!hasData) {
      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování
      this.hasMethodDetailChartData = false;
      this.renderingMethodDetailChart = false;

      // Zničíme existující graf, pokud existuje
      if (this.methodDetailChart) {
        this.methodDetailChart.destroy();
        this.methodDetailChart = null;
      }

      return;
    }

    // Aktualizace hodnot filtru podle dat grafu, pokud jsou k dispozici
    if (data.className && data.methodName) {
      console.log('renderMethodDetailChart - Aktualizuji hodnoty filtru podle dat grafu:', {
        className: data.className,
        methodName: data.methodName
      });

      // Aktualizace pouze pokud se hodnoty liší
      if (this.methodDetailFilterData.className !== data.className ||
          this.methodDetailFilterData.methodName !== data.methodName) {

        this.methodDetailFilterData.className = data.className;
        this.methodDetailFilterData.methodName = data.methodName;

        // Uložení filtru do localStorage
        this.saveFilter('performance_method_detail', this.methodDetailFilterData);
      }
    }

    // Pokud máme data, nastavíme indikátor
    this.hasMethodDetailChartData = true;

    // Kontrola, zda je reference na canvas element k dispozici
    if (!this.methodDetailChartRef) {
      console.warn('renderMethodDetailChart - Reference na canvas element není k dispozici, zkusím později');

      // Zkusíme počkat a zkusit to znovu po delším timeoutu
      setTimeout(() => {
        console.log('renderMethodDetailChart - Zkouším znovu po delším timeoutu (2000ms)');
        if (this.methodDetailChartRef) {
          this.renderMethodDetailChart(data);
        } else {
          console.error('renderMethodDetailChart - Canvas element stále není k dispozici po 2000ms!');
          this.renderingMethodDetailChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci
        }
      }, 2000);

      return;
    }

    console.log('renderMethodDetailChart - Reference na canvas element je k dispozici');

    try {
      // Kontrola, zda je canvas element v DOM
      if (!document.body.contains(this.methodDetailChartRef.nativeElement)) {
        console.warn('renderMethodDetailChart - Canvas element není v DOM, zkusím později');

        // Zkusíme počkat a zkusit to znovu po delším timeoutu
        setTimeout(() => {
          console.log('renderMethodDetailChart - Zkouším znovu po delším timeoutu (element není v DOM)');
          if (this.methodDetailChartRef && document.body.contains(this.methodDetailChartRef.nativeElement)) {
            this.renderMethodDetailChart(data);
          } else {
            console.error('renderMethodDetailChart - Canvas element stále není v DOM po 2000ms!');
          }
        }, 2000);

        return;
      }

      // Získání kontextu canvas elementu
      const ctx = this.methodDetailChartRef.nativeElement.getContext('2d');
      if (!ctx) {
        console.error('renderMethodDetailChart - Nepodařilo se získat kontext canvas elementu!');
        return;
      }
      console.log('renderMethodDetailChart - Kontext canvas elementu je k dispozici');

      if (this.methodDetailChart) {
        console.log('renderMethodDetailChart - Ničím existující graf');
        this.methodDetailChart.destroy();
      }

    try {
      console.log('renderMethodDetailChart - Vytvářím nový graf');

      // Kontrola, zda je canvas element stále platný
      if (!this.methodDetailChartRef || !document.body.contains(this.methodDetailChartRef.nativeElement)) {
        console.error('renderMethodDetailChart - Canvas element již není platný!');
        return;
      }

      // Kontrola, zda má canvas element rozměry
      const width = this.methodDetailChartRef.nativeElement.width;
      const height = this.methodDetailChartRef.nativeElement.height;
      console.log('renderMethodDetailChart - Canvas rozměry:', { width, height });

      // Kontrola, zda jsou všechna pole dat stejné délky
      if (hasData) {
        const labelsLength = data.labels.length;

        // Kontrola existence a délky polí dat
        if (!data.avgData || data.avgData.length !== labelsLength) {
          console.warn('renderMethodDetailChart - avgData má jinou délku než labels, upravuji');
          data.avgData = this.adjustArrayLength(data.avgData || [], labelsLength);
        }
        if (!data.medianData || data.medianData.length !== labelsLength) {
          console.warn('renderMethodDetailChart - medianData má jinou délku než labels, upravuji');
          data.medianData = this.adjustArrayLength(data.medianData || [], labelsLength);
        }
        if (!data.p95Data || data.p95Data.length !== labelsLength) {
          console.warn('renderMethodDetailChart - p95Data má jinou délku než labels, upravuji');
          data.p95Data = this.adjustArrayLength(data.p95Data || [], labelsLength);
        }
        if (!data.totalCountData || data.totalCountData.length !== labelsLength) {
          console.warn('renderMethodDetailChart - totalCountData má jinou délku než labels, upravuji');
          data.totalCountData = this.adjustArrayLength(data.totalCountData || [], labelsLength);
        }
      }

      // Načtení nastavení viditelnosti metrik z localStorage
      const visibility = this.loadMetricsVisibility('method-detail');

      // Vytvoření datasetů s nastavením viditelnosti
      const datasets = [
        {
          label: 'Průměr (ms)',
          data: data.avgData,
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['Průměr (ms)'] : false
        },
        {
          label: 'Medián (ms)',
          data: data.medianData,
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['Medián (ms)'] : false
        },
        {
          label: '95. percentil (ms)',
          data: data.p95Data,
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['95. percentil (ms)'] : false
        },
        {
          label: 'Počet volání',
          data: data.totalCountData,
          borderColor: 'rgba(201, 203, 207, 1)',
          backgroundColor: 'rgba(201, 203, 207, 0.2)',
          borderWidth: 2,
          tension: 0.3,
          fill: false,
          hidden: visibility ? !visibility['Počet volání'] : false,
          yAxisID: 'y1'
        }
      ];

      // Vytvoření nového grafu
      this.methodDetailChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: data.labels,
          datasets: datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: data.title || 'Detail metody'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            },
            legend: {
              position: 'top',
              labels: {
                boxWidth: 12
              },
              onClick: (_e, legendItem, legend) => {
                // Standardní chování - přepnutí viditelnosti datasetu
                const index = legendItem.datasetIndex;
                const ci = legend.chart;
                if (index !== undefined) {
                  if (ci.isDatasetVisible(index)) {
                    ci.hide(index);
                    legendItem.hidden = true;
                  } else {
                    ci.show(index);
                    legendItem.hidden = false;
                  }

                  // Počkáme, až se graf aktualizuje
                  setTimeout(() => {
                    // Uložení nastavení viditelnosti do localStorage
                    this.saveMetricsVisibility('method-detail', ci.data.datasets);
                  }, 0);

                  // Překreslení grafu
                  ci.update();
                }
              }
            }
          },
          scales: {
            x: {
              display: true,
              title: {
                display: true,
                text: 'Datum'
              }
            },
            y: {
              display: true,
              title: {
                display: true,
                text: 'Doba odezvy (ms)'
              },
              beginAtZero: true
            },
            y1: {
              display: true,
              position: 'right',
              title: {
                display: true,
                text: 'Počet volání'
              },
              beginAtZero: true,
              grid: {
                drawOnChartArea: false
              }
            }
          }
        }
      });
      console.log('renderMethodDetailChart - Graf úspěšně vytvořen');

      // Uložení aktuálního nastavení viditelnosti metrik do localStorage
      setTimeout(() => {
        if (this.methodDetailChart) {
          this.saveMetricsVisibility('method-detail', this.methodDetailChart.data.datasets);
        }
        // Ukončíme vykreslování
        this.renderingMethodDetailChart = false;
      }, 100);
    } catch (error) {
      console.error('renderMethodDetailChart - Chyba při vytváření grafu:', error);
      this.renderingMethodDetailChart = false; // Ukončíme vykreslování v případě chyby
    }
  } catch (error) {
    console.error('renderMethodDetailChart - Chyba při získávání kontextu canvas elementu:', error);
    this.renderingMethodDetailChart = false; // Ukončíme vykreslování v případě chyby
  }
  }

  /**
   * Inteligentní zkrácení názvu metody pro zobrazení v grafu
   * @param text Text k zkrácení
   * @param maxLength Maximální délka (výchozí 30 znaků)
   * @returns Zkrácený text s "..." pokud je potřeba
   */
  private truncateMethodName(text: string, maxLength: number = 30): string {
    if (!text || text.length <= maxLength) {
      return text;
    }

    // Pokusíme se zkrátit na posledním "." před limitem pro zachování čitelnosti
    const truncated = text.substring(0, maxLength - 3);
    const lastDotIndex = truncated.lastIndexOf('.');

    // Pokud najdeme tečku a není příliš na začátku, zkrátíme na ní
    if (lastDotIndex > 0 && lastDotIndex > maxLength * 0.4) {
      return text.substring(0, lastDotIndex) + '...';
    }

    // Jinak zkrátíme na pevné délce
    return truncated + '...';
  }

  /**
   * Vytvoří konfiguraci pro zkrácení popisků na ose X
   *
   * POUŽITÍ:
   * 1. Vytvořte konfiguraci: const config = this.createTruncatedLabelsConfig(originalLabels, { enabled: true });
   * 2. Sloučte s existující konfigurací: const finalOptions = this.mergeChartConfig(baseOptions, config);
   * 3. Použijte ve vytváření grafu: new Chart(ctx, { options: finalOptions });
   *
   * PŘÍKLAD:
   * const truncatedConfig = this.createTruncatedLabelsConfig(data.labels, {
   *   enabled: true,           // Zapnout/vypnout zkrácení (výchozí: true)
   *   maxLength: 25,          // Maximální délka popisku (výchozí: 30)
   *   showFullInTooltip: true // Zobrazit plný název v tooltip (výchozí: true)
   * });
   *
   * @param originalLabels Originální popisky
   * @param options Možnosti konfigurace
   * @returns Objekt s konfigurací pro Chart.js
   */
  private createTruncatedLabelsConfig(originalLabels: string[], options: {
    enabled?: boolean;
    maxLength?: number;
    showFullInTooltip?: boolean;
  } = {}) {
    const config = {
      enabled: options.enabled ?? true,
      maxLength: options.maxLength ?? 30,
      showFullInTooltip: options.showFullInTooltip ?? true
    };

    if (!config.enabled) {
      return {};
    }

    const result: any = {
      scales: {
        x: {
          ticks: {
            callback: (value: any, index: number) => {
              const label = originalLabels[index];
              return this.truncateMethodName(label, config.maxLength);
            }
          }
        }
      }
    };

    if (config.showFullInTooltip) {
      result.plugins = {
        tooltip: {
          callbacks: {
            title: (context: any) => {
              const dataIndex = context[0].dataIndex;
              return originalLabels[dataIndex] || context[0].label;
            }
          }
        }
      };
    }

    return result;
  }

  /**
   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu
   * @param baseConfig Základní konfigurace grafu
   * @param truncatedConfig Konfigurace pro zkrácené popisky
   * @returns Sloučená konfigurace
   */
  private mergeChartConfig(baseConfig: any, truncatedConfig: any): any {
    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {
      return baseConfig;
    }

    const merged = { ...baseConfig };

    // Sloučení scales
    if (truncatedConfig.scales) {
      merged.scales = merged.scales || {};
      if (truncatedConfig.scales.x) {
        merged.scales.x = merged.scales.x || {};
        merged.scales.x.ticks = {
          ...merged.scales.x.ticks,
          ...truncatedConfig.scales.x.ticks
        };
      }
    }

    // Sloučení plugins
    if (truncatedConfig.plugins) {
      merged.plugins = merged.plugins || {};
      if (truncatedConfig.plugins.tooltip) {
        merged.plugins.tooltip = merged.plugins.tooltip || {};
        merged.plugins.tooltip.callbacks = {
          ...merged.plugins.tooltip.callbacks,
          ...truncatedConfig.plugins.tooltip.callbacks
        };
      }
    }

    return merged;
  }

  // ========================================
  // PŘÍKLAD POUŽITÍ ZKRÁCENÝCH POPISKŮ:
  // ========================================
  //
  // // 1. Vytvořte konfiguraci pro zkrácené popisky
  // const truncatedLabelsConfig = this.createTruncatedLabelsConfig(data.labels, {
  //   enabled: true,           // Zapnout zkrácení
  //   maxLength: 25,          // Max 25 znaků
  //   showFullInTooltip: true // Plný název v tooltip
  // });
  //
  // // 2. Vytvořte základní konfiguraci grafu
  // const baseOptions = {
  //   responsive: true,
  //   plugins: { /* vaše plugins */ },
  //   scales: { /* vaše scales */ }
  // };
  //
  // // 3. Sloučte konfigurace
  // const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);
  //
  // // 4. Vytvořte graf
  // new Chart(ctx, {
  //   type: 'bar',
  //   data: { labels: data.labels, datasets: data.datasets },
  //   options: finalOptions
  // });
  //
  // ========================================

  /**
   * Příprava dat pro graf agregovaných výkonnostních metrik
   */
  private prepareAggregatedPerformanceChartData(data: any): any {
    console.log('prepareAggregatedPerformanceChartData - vstupní data:', data);

    // Výchozí prázdná struktura pro data grafu
    const chartData: {
      labels: string[];
      avgData: number[];
      medianData: number[];
      p95Data: number[];
      p99Data: number[];
      minData: number[];
      maxData: number[];
      totalCountData: number[];
      title: string;
    } = {
      labels: [],
      avgData: [],
      medianData: [],
      p95Data: [],
      p99Data: [],
      minData: [],
      maxData: [],
      totalCountData: [],
      title: 'Výkon v čase'
    };

    // Kontrola, zda data obsahují potřebné údaje
    if (!data) {
      console.warn('prepareAggregatedPerformanceChartData - Chybí data pro graf');
      return chartData;
    }

    try {
      // Pokud jsou data již připravena v požadovaném formátu z API
      if (data.labels && Array.isArray(data.labels) && data.labels.length > 0) {
        console.log('prepareAggregatedPerformanceChartData - Používám předpřipravená data z API');
        chartData.labels = data.labels;
        chartData.avgData = data.avgData || [];
        chartData.medianData = data.medianData || [];
        chartData.p95Data = data.p95Data || [];
        chartData.p99Data = data.p99Data || [];
        chartData.minData = data.minData || [];
        chartData.maxData = data.maxData || [];
        chartData.totalCountData = data.totalCountData || [];

        // Nastavení titulku grafu
        if (data.title) {
          chartData.title = data.title;
        }
      }
      // Pokud jsou data ve formátu denních dat
      else if (data.dailyData && Array.isArray(data.dailyData) && data.dailyData.length > 0) {
        console.log('prepareAggregatedPerformanceChartData - Zpracovávám denní data');
        const dailyData = data.dailyData;

        // Seřazení dat podle data
        dailyData.sort((a: any, b: any) => {
          const dateA = a.date ? new Date(a.date).getTime() : 0;
          const dateB = b.date ? new Date(b.date).getTime() : 0;
          return dateA - dateB;
        });

        // Naplnění polí pro graf
        chartData.labels = dailyData.map((item: any) => {
          if (item.date) {
            const date = new Date(item.date);
            return this.formatDate(date);
          }
          return '';
        }).filter((label: string) => label !== '');

        // Mapování dat s kontrolou existence vlastností
        chartData.avgData = dailyData.map((item: any) => item.avg !== undefined ? item.avg : 0);
        chartData.medianData = dailyData.map((item: any) => item.median !== undefined ? item.median : 0);
        chartData.p95Data = dailyData.map((item: any) => item.percentil95 !== undefined ? item.percentil95 : 0);
        chartData.p99Data = dailyData.map((item: any) => item.percentil99 !== undefined ? item.percentil99 : 0);
        chartData.minData = dailyData.map((item: any) => item.min !== undefined ? item.min : 0);
        chartData.maxData = dailyData.map((item: any) => item.max !== undefined ? item.max : 0);
        chartData.totalCountData = dailyData.map((item: any) => item.totalCount !== undefined ? item.totalCount : 0);

        // Nastavení titulku grafu
        if (data.title) {
          chartData.title = data.title;
        }
      }
      else {
        console.warn('prepareAggregatedPerformanceChartData - Neznámý formát dat');

        // Pokus o extrakci dat z neznámého formátu
        if (typeof data === 'object') {
          // Pokus o nalezení polí s daty
          for (const key in data) {
            if (key === 'labels' && Array.isArray(data[key])) {
              chartData.labels = data[key].map((item: any) => String(item));
            } else if (key === 'avgData' && Array.isArray(data[key])) {
              chartData.avgData = data[key].map((item: any) => Number(item) || 0);
            } else if (key === 'medianData' && Array.isArray(data[key])) {
              chartData.medianData = data[key].map((item: any) => Number(item) || 0);
            } else if (key === 'p95Data' && Array.isArray(data[key])) {
              chartData.p95Data = data[key].map((item: any) => Number(item) || 0);
            } else if (key === 'p99Data' && Array.isArray(data[key])) {
              chartData.p99Data = data[key].map((item: any) => Number(item) || 0);
            } else if (key === 'minData' && Array.isArray(data[key])) {
              chartData.minData = data[key].map((item: any) => Number(item) || 0);
            } else if (key === 'maxData' && Array.isArray(data[key])) {
              chartData.maxData = data[key].map((item: any) => Number(item) || 0);
            } else if (key === 'totalCountData' && Array.isArray(data[key])) {
              chartData.totalCountData = data[key].map((item: any) => Number(item) || 0);
            } else if (key === 'title' && typeof data[key] === 'string') {
              chartData.title = data[key];
            }
          }
        }
      }
    } catch (error) {
      console.error('prepareAggregatedPerformanceChartData - Chyba při zpracování dat:', error);
    }

    console.log('prepareAggregatedPerformanceChartData - výstupní data:', chartData);
    return chartData;
  }

  /**
   * Inicializace popoverů pro nápovědu
   */
  private initPopovers(): void {
    console.log('initPopovers - Inicializuji popovery');

    // Definice obsahu nápověd
    const helpContent: Record<string, string> = {
      'aggregated-performance': 'Graf zobrazuje agregované výkonnostní metriky DIS metod v čase. ' +
        'Pro každý den jsou zobrazeny hodnoty jako průměr, medián, 95. percentil, 99. percentil, minimum, maximum a počet volání. ' +
        'Kliknutím na legendu můžete zobrazit nebo skrýt jednotlivé metriky. ' +
        'Výchozí zobrazení obsahuje průměr, medián a 95. percentil, ostatní metriky jsou skryté.',
      'instances-comparison': 'Graf umožňuje srovnání výkonu stejných DIS metod mezi různými instancemi. ' +
        'Vyberte alespoň dvě instance, které chcete porovnat, a volitelně filtrujte podle konkrétní třídy nebo metody. ' +
        'Graf zobrazuje průměrnou dobu odezvy metod pro každou vybranou instanci.',
      'versions-comparison': 'Graf umožňuje srovnání výkonu DIS metod mezi různými verzemi jedné instance. ' +
        'Vyberte instanci a volitelně filtrujte podle konkrétní třídy nebo metody. ' +
        'Graf zobrazuje průměrnou dobu odezvy metod pro každou verzi aplikace v rámci vybrané instance.',
      'slowest-methods': 'Tabulka zobrazuje nejpomalejší DIS metody seřazené podle 95. percentilu doby odezvy. ' +
        'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' +
        'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',
      'methods': 'Tabulka zobrazuje DIS metody podle zvoleného filtru (nejpomalejší, nejčastěji volané nebo všechny). ' +
        'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' +
        'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',
      'most-called-methods': 'Tabulka zobrazuje nejčastěji volané DIS metody seřazené podle počtu volání. ' +
        'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' +
        'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',
      'all-methods': 'Tabulka zobrazuje všechny DIS metody. ' +
        'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' +
        'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',
      'method-detail': 'Graf zobrazuje výkon konkrétní DIS metody v čase. ' +
        'Pro každý den jsou zobrazeny hodnoty jako průměr, medián, 95. percentil, 99. percentil, minimum, maximum a počet volání. ' +
        'Kliknutím na legendu můžete zobrazit nebo skrýt jednotlivé metriky. ' +
        'Nad grafem jsou zobrazeny souhrnné statistiky metody za celé zvolené období.',
      'avg-response-time': 'Průměrná doba odezvy metody v milisekundách za celé zvolené období. ' +
        'Průměr je vypočítán jako aritmetický průměr všech naměřených hodnot. ' +
        'Může být ovlivněn extrémními hodnotami, proto je vhodné sledovat také medián.',
      'median-response-time': 'Medián doby odezvy metody v milisekundách za celé zvolené období. ' +
        'Medián představuje prostřední hodnotu ze všech naměřených časů (50. percentil). ' +
        'Polovina volání byla rychlejší a polovina pomalejší než tato hodnota. ' +
        'Medián není ovlivněn extrémními hodnotami, proto lépe reprezentuje typickou dobu odezvy než průměr.',
      'p95-response-time': '95. percentil doby odezvy metody v milisekundách za celé zvolené období. ' +
        'Tato hodnota znamená, že 95% všech volání metody bylo rychlejších než tato hodnota. ' +
        'Zbývajících 5% volání bylo pomalejších. ' +
        'Tento ukazatel je důležitý pro identifikaci problémů s výkonem, které ovlivňují menší část uživatelů.',
      'total-calls': 'Celkový počet volání metody za zvolené časové období. ' +
        'Tento údaj ukazuje, jak často je metoda používána. ' +
        'Vysoký počet volání v kombinaci s delší dobou odezvy může indikovat potenciální problém s výkonem aplikace.'
    };

    try {
      // Inicializace popoverů pomocí Bootstrap API
      setTimeout(() => {
        try {
          // Kontrola, zda je bootstrap k dispozici
          if (typeof bootstrap === 'undefined') {
            console.error('initPopovers - Bootstrap není k dispozici!');
            return;
          }

          // Kontrola, zda je Popover k dispozici
          if (typeof bootstrap.Popover === 'undefined') {
            console.error('initPopovers - Bootstrap.Popover není k dispozici!');
            return;
          }

          // Nejprve zrušíme všechny existující popovery
          const existingPopovers = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
          existingPopovers.forEach((el: any) => {
            try {
              // @ts-ignore
              const popover = bootstrap.Popover.getInstance(el);
              if (popover) {
                popover.dispose();
              }
            } catch (error) {
              console.warn('initPopovers - Chyba při rušení existujícího popoveru:', error);
            }
          });

          // Nyní inicializujeme nové popovery
          const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
          console.log('initPopovers - Nalezeno', popoverTriggerList.length, 'elementů s popovery');

          popoverTriggerList.forEach((popoverTriggerEl: any) => {
            try {
              const helpType = popoverTriggerEl.getAttribute('data-help-type');
              const content = helpContent[helpType] || 'Nápověda není k dispozici.';

              // Přidáme titulek popoveru
              let title = '';
              switch (helpType) {
                case 'aggregated-performance':
                  title = 'Agregovaný výkon v čase';
                  break;
                case 'instances-comparison':
                  title = 'Srovnání výkonu metod mezi instancemi';
                  break;
                case 'versions-comparison':
                  title = 'Srovnání výkonu metod mezi verzemi';
                  break;
                case 'slowest-methods':
                  title = 'Nejpomalejší metody';
                  break;
                case 'most-called-methods':
                  title = 'Nejčastěji volané metody';
                  break;
                case 'all-methods':
                  title = 'Všechny metody';
                  break;
                case 'method-detail':
                  title = 'Detail metody';
                  break;
                case 'avg-response-time':
                  title = 'Průměrná doba odezvy';
                  break;
                case 'median-response-time':
                  title = 'Medián doby odezvy';
                  break;
                case 'p95-response-time':
                  title = '95. percentil';
                  break;
                case 'total-calls':
                  title = 'Celkový počet volání';
                  break;
                default:
                  title = 'Nápověda';
              }

              // @ts-ignore
              new bootstrap.Popover(popoverTriggerEl, {
                container: 'body',
                trigger: 'hover',
                placement: 'top',
                title: title,
                content: content,
                html: true
              });
            } catch (error) {
              console.error('initPopovers - Chyba při inicializaci popoveru:', error);
            }
          });

          console.log('initPopovers - Popovery úspěšně inicializovány');
        } catch (error) {
          console.error('initPopovers - Chyba při inicializaci popoverů:', error);
        }
      }, 500);
    } catch (error) {
      console.error('initPopovers - Chyba při inicializaci popoverů:', error);
    }
  }



  /**
   * Formátování data
   */
  formatDate(date: Date): string {
    return this.datePipe.transform(date, 'dd.MM.yyyy') || '';
  }

  /**
   * Úprava délky pole na požadovanou délku
   * @param array Pole, které chceme upravit
   * @param length Požadovaná délka pole
   * @returns Upravené pole s požadovanou délkou
   */
  private adjustArrayLength(array: any[], length: number): any[] {
    if (!array) {
      return new Array(length).fill(0);
    }

    if (array.length === length) {
      return array;
    }

    if (array.length > length) {
      return array.slice(0, length);
    }

    // Pokud je pole kratší než požadovaná délka, doplníme ho nulami
    const result = [...array];
    while (result.length < length) {
      result.push(0);
    }

    return result;
  }

  /**
   * Určí, které osy Y mají být zobrazené na základě viditelných metrik
   */
  private getVisibleAxes(datasets: any[], chart?: any): { showLeftAxis: boolean, showRightAxis: boolean } {
    let showLeftAxis = false;
    let showRightAxis = false;

    datasets.forEach((dataset, index) => {
      // Pokud máme referenci na graf, použijeme Chart.js API pro zjištění viditelnosti
      const isVisible = chart ? chart.isDatasetVisible(index) : !dataset.hidden;

      if (isVisible) {
        if (dataset.yAxisID === 'y1') {
          // Metrika "Počet volání" používá pravou osu
          showRightAxis = true;
        } else {
          // Všechny ostatní metriky (doba odezvy) používají levou osu
          showLeftAxis = true;
        }
      }
    });

    return { showLeftAxis, showRightAxis };
  }

  /**
   * Aktualizuje viditelnost os Y na základě aktuálně viditelných metrik
   */
  private updateAxisVisibility(chart: any): void {
    if (!chart || !chart.data || !chart.data.datasets) {
      return;
    }

    const visibleAxes = this.getVisibleAxes(chart.data.datasets, chart);

    // Aktualizace konfigurace os
    if (chart.options && chart.options.scales) {
      if (chart.options.scales.y) {
        chart.options.scales.y.display = visibleAxes.showLeftAxis;
        chart.options.scales.y.title.display = visibleAxes.showLeftAxis;
      }

      if (chart.options.scales.y1) {
        chart.options.scales.y1.display = visibleAxes.showRightAxis;
        chart.options.scales.y1.title.display = visibleAxes.showRightAxis;
        // Zobrazit mřížku pro pravou osu pouze když levá osa není viditelná
        chart.options.scales.y1.grid.drawOnChartArea = !visibleAxes.showLeftAxis;
      }
    }

    // Překreslení grafu s novými nastaveními
    chart.update();
  }
}
