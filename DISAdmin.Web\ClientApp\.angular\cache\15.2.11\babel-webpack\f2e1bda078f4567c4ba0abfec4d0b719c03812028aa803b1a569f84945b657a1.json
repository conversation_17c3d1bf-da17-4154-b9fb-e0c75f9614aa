{"ast": null, "code": "import { Chart, registerables } from 'chart.js';\nimport { moveItemInArray } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/chart.service\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/alert.service\";\nimport * as i4 from \"../services/dashboard-config.service\";\nimport * as i5 from \"../services/chart-modal.service\";\nimport * as i6 from \"../services/monitoring.service\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/cdk/drag-drop\";\nconst _c0 = [\"apiCallsChart\"];\nconst _c1 = [\"apiPerformanceChart\"];\nconst _c2 = [\"instancesUsageChart\"];\nconst _c3 = [\"productionPerformanceChart\"];\nconst _c4 = [\"securityEventsChart\"];\nfunction DashboardComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.resetDashboardConfig());\n    });\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵelementStart(2, \"span\", 7);\n    i0.ɵɵtext(3, \"Resetovat\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"i\", 20);\n    i0.ɵɵtext(2, \" Nyn\\u00ED m\\u016F\\u017Eete p\\u0159et\\u00E1hnout widgety a zm\\u011Bnit jejich po\\u0159ad\\u00ED. Kliknut\\u00EDm na tla\\u010D\\u00EDtko \\\"Dokon\\u010Dit \\u00FApravy\\\" ulo\\u017E\\u00EDte zm\\u011Bny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"span\", 23);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DashboardComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction DashboardComponent_div_28_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 28);\n    i0.ɵɵtext(4, \"DISApi vol\\u00E1n\\u00ED v \\u010Dase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_28_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const _r12 = i0.ɵɵreference(10);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.openFullscreenChart(_r12, \"DISApi vol\\u00E1n\\u00ED v \\u010Dase\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_28_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r4.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r4.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.editMode);\n  }\n}\nfunction DashboardComponent_div_29_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 37);\n    i0.ɵɵtext(4, \"Odezva DIS metod\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_29_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const _r16 = i0.ɵɵreference(10);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.openFullscreenChart(_r16, \"Odezva DIS metod\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_29_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r5.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r5.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.editMode);\n  }\n}\nfunction DashboardComponent_div_30_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 39);\n    i0.ɵɵtext(4, \"Vol\\u00E1n\\u00ED DIS metod podle instanc\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_30_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const _r20 = i0.ɵɵreference(10);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.openFullscreenChart(_r20, \"Vol\\u00E1n\\u00ED DIS metod podle instanc\\u00ED\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_30_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r6.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r6.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.editMode);\n  }\n}\nfunction DashboardComponent_div_31_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 41);\n    i0.ɵɵtext(4, \"Pr\\u016Fm\\u011Brn\\u00E1 odezva a 95. percentil produk\\u010Dn\\u00EDch instanc\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const _r24 = i0.ɵɵreference(10);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.openFullscreenChart(_r24, \"Pr\\u016Fm\\u011Brn\\u00E1 odezva a 95. percentil produk\\u010Dn\\u00EDch instanc\\u00ED\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_31_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r7.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r7.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.editMode);\n  }\n}\nfunction DashboardComponent_div_32_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 43);\n    i0.ɵɵtext(4, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_32_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const _r28 = i0.ɵɵreference(10);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.openFullscreenChart(_r28, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\"));\n    });\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 32);\n    i0.ɵɵelement(9, \"canvas\", null, 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, DashboardComponent_div_32_div_11_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r8.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r8.editMode);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.editMode);\n  }\n}\nfunction DashboardComponent_div_33_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.toggleWidgetVisibility(\"system-info\"));\n    });\n    i0.ɵɵelement(2, \"i\", 62);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_33_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_div_16_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.navigateToSecurityEvents());\n    });\n    i0.ɵɵelementStart(2, \"div\", 53)(3, \"h3\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 55);\n    i0.ɵɵtext(6, \"Nevy\\u0159e\\u0161en\\u00FDch bezp. ud\\u00E1lost\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r33.dashboardSummary.securityEventsCount || 0);\n  }\n}\nfunction DashboardComponent_div_33_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"div\", 47)(3, \"div\", 27)(4, \"h5\", 48);\n    i0.ɵɵtext(5, \"Syst\\u00E9mov\\u00E9 informace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, DashboardComponent_div_33_div_6_Template, 3, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"div\", 50)(9, \"div\", 51)(10, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.navigateToAlerts());\n    });\n    i0.ɵɵelementStart(11, \"div\", 53)(12, \"h3\", 54);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 55);\n    i0.ɵɵtext(15, \"Aktivn\\u00ED upozorn\\u011Bn\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(16, DashboardComponent_div_33_div_16_Template, 7, 1, \"div\", 56);\n    i0.ɵɵelementStart(17, \"div\", 51)(18, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.navigateToMonitoring());\n    });\n    i0.ɵɵelementStart(19, \"div\", 53)(20, \"h3\", 54);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 55);\n    i0.ɵɵtext(23, \"Dostupnost DIS API\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 51)(25, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_Template_div_click_25_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.navigateToPerformance());\n    });\n    i0.ɵɵelementStart(26, \"div\", 53)(27, \"h3\", 54);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\", 55);\n    i0.ɵɵtext(30, \"Pr\\u016Fm\\u011Brn\\u00E1 odezva DIS metod\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 51)(32, \"div\", 59);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_33_Template_div_click_32_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.navigateToCertificates());\n    });\n    i0.ɵɵelementStart(33, \"div\", 53)(34, \"h3\", 54);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 55);\n    i0.ɵɵtext(37, \"Expiruj\\u00EDc\\u00ED certifik\\u00E1ty\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(38, DashboardComponent_div_33_div_38_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r9.editMode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r9.editMode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.editMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r9.dashboardSummary.alertsCount || 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isAdmin);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.systemStatistics.ApiAvailability || 100, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.systemStatistics.AvgApiResponseTime || 0, \"ms\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r9.dashboardSummary.expiringCertificatesCount || 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.editMode);\n  }\n}\nconst _c5 = function (a0, a1) {\n  return {\n    \"bi-pencil-square\": a0,\n    \"bi-check-lg\": a1\n  };\n};\n// Registrace všech komponent Chart.js\nChart.register(...registerables);\nexport class DashboardComponent {\n  constructor(chartService, authService, alertService, dashboardConfigService, chartModalService, monitoringService, router) {\n    this.chartService = chartService;\n    this.authService = authService;\n    this.alertService = alertService;\n    this.dashboardConfigService = dashboardConfigService;\n    this.chartModalService = chartModalService;\n    this.monitoringService = monitoringService;\n    this.router = router;\n    this.loading = true;\n    this.error = null;\n    this.isAdmin = false;\n    this.alerts = [];\n    this.systemStatistics = {};\n    this.dashboardSummary = {};\n    // Grafy\n    this.apiCallsChart = null;\n    this.apiPerformanceChart = null;\n    this.instancesUsageChart = null;\n    this.productionPerformanceChart = null;\n    this.securityEventsChart = null;\n    // Filtry\n    this.selectedDays = 30;\n    this.selectedInstanceId = null;\n    // Dashboard konfigurace\n    this.dashboardConfig = null;\n    this.widgets = [];\n    this.editMode = false;\n    // Aktualizace dat\n    this.updateSubscription = null;\n    // Event listener pro zavírání popoverů\n    this.documentClickListener = null;\n    this.authService.currentUser.subscribe(user => {\n      this.currentUser = user;\n      this.isAdmin = user?.isAdmin || false;\n    });\n    // Načtení uložené hodnoty počtu dní z local storage\n    const savedDateRange = localStorage.getItem('monitoring_dateRange');\n    if (savedDateRange) {\n      this.selectedDays = parseInt(savedDateRange, 10);\n    }\n  }\n  ngOnInit() {\n    // Načtení konfigurace dashboardu\n    this.loadDashboardConfig();\n    this.loadAlerts();\n    this.loadSystemStatistics();\n    this.loadDashboardSummary();\n  }\n  /**\r\n   * Načte konfiguraci dashboardu pro aktuálního uživatele\r\n   */\n  loadDashboardConfig() {\n    this.dashboardConfigService.getUserDashboardConfig().subscribe({\n      next: config => {\n        this.dashboardConfig = config;\n        this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n      },\n      error: error => {\n        console.error('Chyba při načítání konfigurace dashboardu', error);\n        // Použijeme výchozí konfiguraci\n        this.dashboardConfigService.resetDashboardConfig().subscribe(config => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n        });\n      }\n    });\n  }\n  ngAfterViewInit() {\n    // Inicializace grafů po načtení view\n    setTimeout(() => {\n      this.initCharts();\n      // Inicializace popoverů s delším zpožděním, aby se zajistilo, že DOM je plně načten\n      setTimeout(() => {\n        this.initPopovers();\n      }, 300);\n    }, 500);\n  }\n  ngOnDestroy() {\n    // Zrušení subscription při zničení komponenty\n    if (this.updateSubscription) {\n      this.updateSubscription.unsubscribe();\n    }\n    // Odstranění globálního event listeneru\n    if (this.documentClickListener) {\n      document.removeEventListener('click', this.documentClickListener);\n      this.documentClickListener = null;\n    }\n    // Zničení všech popoverů\n    this.destroyPopovers();\n    // Zničení grafů\n    this.destroyCharts();\n  }\n  /**\r\n   * Načtení upozornění\r\n   */\n  loadAlerts() {\n    this.alertService.getDashboardAlerts().subscribe({\n      next: data => {\n        this.alerts = data.filter(alert => !alert.isResolved);\n      },\n      error: err => {\n        console.error('Chyba při načítání upozornění', err);\n        this.error = 'Nepodařilo se načíst upozornění';\n      }\n    });\n  }\n  /**\r\n   * Načtení systémových statistik\r\n   */\n  loadSystemStatistics() {\n    this.monitoringService.getSystemStatistics().subscribe({\n      next: data => {\n        this.systemStatistics = data;\n      },\n      error: err => {\n        console.error('Chyba při načítání systémových statistik', err);\n        this.error = 'Nepodařilo se načíst systémové statistiky';\n      }\n    });\n  }\n  /**\r\n   * Načtení dashboard summary dat\r\n   */\n  loadDashboardSummary() {\n    this.monitoringService.getDashboardSummary().subscribe({\n      next: data => {\n        this.dashboardSummary = data;\n      },\n      error: err => {\n        console.error('Chyba při načítání dashboard summary', err);\n        this.error = 'Nepodařilo se načíst dashboard summary';\n      }\n    });\n  }\n  /**\r\n   * Inicializace všech grafů\r\n   */\n  initCharts() {\n    this.loading = true;\n    // Načtení dat pro grafy\n    this.loadApiCallsChart();\n    this.loadApiPerformanceChart();\n    this.loadInstancesUsageChart();\n    this.loadProductionPerformanceChart();\n    if (this.isAdmin) {\n      this.loadSecurityEventsChart();\n    }\n    this.loading = false;\n  }\n  /**\r\n   * Aktualizace všech grafů\r\n   */\n  refreshCharts() {\n    this.loadApiCallsChart();\n    this.loadApiPerformanceChart();\n    this.loadInstancesUsageChart();\n    this.loadProductionPerformanceChart();\n    if (this.isAdmin) {\n      this.loadSecurityEventsChart();\n    }\n    this.loadAlerts();\n    this.loadSystemStatistics();\n    this.loadDashboardSummary();\n    // Reinicializace popoverů po aktualizaci dat\n    setTimeout(() => {\n      this.initPopovers();\n    }, 100);\n  }\n  /**\r\n   * Zničení všech grafů\r\n   */\n  destroyCharts() {\n    if (this.apiCallsChart) {\n      this.apiCallsChart.destroy();\n      this.apiCallsChart = null;\n    }\n    if (this.apiPerformanceChart) {\n      this.apiPerformanceChart.destroy();\n      this.apiPerformanceChart = null;\n    }\n    if (this.instancesUsageChart) {\n      this.instancesUsageChart.destroy();\n      this.instancesUsageChart = null;\n    }\n    if (this.securityEventsChart) {\n      this.securityEventsChart.destroy();\n      this.securityEventsChart = null;\n    }\n  }\n  /**\r\n   * Načtení grafu API volání\r\n   */\n  loadApiCallsChart() {\n    this.chartService.getApiCallsChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n      next: data => {\n        if (this.apiCallsChart) {\n          this.apiCallsChart.data.labels = data.labels;\n          this.apiCallsChart.data.datasets[0].data = data.data;\n          this.apiCallsChart.update();\n        } else if (this.apiCallsChartRef) {\n          this.apiCallsChart = new Chart(this.apiCallsChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Počet DISApi volání',\n                data: data.data,\n                backgroundColor: 'rgba(54, 162, 235, 0.2)',\n                borderColor: 'rgba(54, 162, 235, 1)',\n                borderWidth: 2,\n                tension: 0.3,\n                fill: true\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf API volání', err);\n        this.error = 'Nepodařilo se načíst data pro graf API volání';\n      }\n    });\n  }\n  /**\r\n   * Načtení grafu výkonu API\r\n   */\n  loadApiPerformanceChart() {\n    this.chartService.getApiPerformanceChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n      next: data => {\n        if (this.apiPerformanceChart) {\n          this.apiPerformanceChart.data.labels = data.labels;\n          this.apiPerformanceChart.data.datasets[0].data = data.avgData;\n          this.apiPerformanceChart.data.datasets[1].data = data.maxData;\n          this.apiPerformanceChart.update();\n        } else if (this.apiPerformanceChartRef) {\n          // Uložení originálních labels pro tooltip\n          const originalLabels = [...data.labels];\n          // Konfigurace pro zkrácené popisky na ose X\n          const truncatedLabelsConfig = this.createTruncatedLabelsConfig(originalLabels, {\n            enabled: true,\n            maxLength: 30,\n            showFullInTooltip: true // Zobrazit plný název v tooltip\n          });\n          // Základní konfigurace grafu\n          const baseOptions = {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              title: {\n                display: true,\n                text: data.title,\n                font: {\n                  size: 16\n                }\n              },\n              legend: {\n                display: true,\n                position: 'top'\n              },\n              tooltip: {\n                mode: 'index',\n                intersect: false\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: true,\n                title: {\n                  display: true,\n                  text: 'Doba odezvy (ms)'\n                }\n              },\n              x: {\n                title: {\n                  display: true,\n                  text: 'Endpoint'\n                }\n              }\n            }\n          };\n          // Sloučení s konfigurací pro zkrácené popisky\n          const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n          this.apiPerformanceChart = new Chart(this.apiPerformanceChartRef.nativeElement, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Průměrná doba odezvy (ms)',\n                data: data.avgData,\n                backgroundColor: 'rgba(54, 162, 235, 0.5)',\n                borderColor: 'rgba(54, 162, 235, 1)',\n                borderWidth: 1\n              }, {\n                label: 'Maximální doba odezvy (ms)',\n                data: data.maxData,\n                backgroundColor: 'rgba(255, 99, 132, 0.5)',\n                borderColor: 'rgba(255, 99, 132, 1)',\n                borderWidth: 1\n              }]\n            },\n            options: finalOptions\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf výkonu API', err);\n        this.error = 'Nepodařilo se načíst data pro graf výkonu API';\n      }\n    });\n  }\n  /**\r\n   * Načtení grafu využití instancí\r\n   */\n  loadInstancesUsageChart() {\n    this.chartService.getInstancesUsageChartData(this.selectedDays).subscribe({\n      next: data => {\n        if (this.instancesUsageChart) {\n          this.instancesUsageChart.data.labels = data.labels;\n          this.instancesUsageChart.data.datasets[0].data = data.data;\n          this.instancesUsageChart.update();\n        } else if (this.instancesUsageChartRef) {\n          this.instancesUsageChart = new Chart(this.instancesUsageChartRef.nativeElement, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Počet volání DIS metod',\n                data: data.data,\n                backgroundColor: ['rgba(255, 99, 132, 0.5)', 'rgba(54, 162, 235, 0.5)', 'rgba(255, 206, 86, 0.5)', 'rgba(75, 192, 192, 0.5)', 'rgba(153, 102, 255, 0.5)', 'rgba(255, 159, 64, 0.5)', 'rgba(199, 199, 199, 0.5)', 'rgba(83, 102, 255, 0.5)', 'rgba(255, 99, 255, 0.5)', 'rgba(99, 255, 132, 0.5)'],\n                borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)', 'rgba(199, 199, 199, 1)', 'rgba(83, 102, 255, 1)', 'rgba(255, 99, 255, 1)', 'rgba(99, 255, 132, 1)'],\n                borderWidth: 1\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Instance'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf využití instancí', err);\n        this.error = 'Nepodařilo se načíst data pro graf využití instancí';\n      }\n    });\n  }\n  /**\r\n   * Načtení grafu výkonu produkčních instancí\r\n   */\n  loadProductionPerformanceChart() {\n    this.chartService.getProductionPerformanceChartData(this.selectedDays).subscribe({\n      next: data => {\n        if (this.productionPerformanceChart) {\n          this.productionPerformanceChart.data.labels = data.labels;\n          this.productionPerformanceChart.data.datasets = data.datasets;\n          this.productionPerformanceChart.update();\n        } else if (this.productionPerformanceChartRef) {\n          this.productionPerformanceChart = new Chart(this.productionPerformanceChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: data.datasets\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Doba odezvy (ms)'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf výkonu produkčních instancí', err);\n        this.error = 'Nepodařilo se načíst data pro graf výkonu produkčních instancí';\n      }\n    });\n  }\n  /**\r\n   * Načtení grafu bezpečnostních událostí\r\n   */\n  loadSecurityEventsChart() {\n    this.chartService.getSecurityEventsChartData(this.selectedDays).subscribe({\n      next: data => {\n        if (this.securityEventsChart) {\n          this.securityEventsChart.data.labels = data.labels;\n          this.securityEventsChart.data.datasets = data.datasets;\n          this.securityEventsChart.update();\n        } else if (this.securityEventsChartRef) {\n          this.securityEventsChart = new Chart(this.securityEventsChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: data.datasets\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet událostí'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf bezpečnostních událostí', err);\n        this.error = 'Nepodařilo se načíst data pro graf bezpečnostních událostí';\n      }\n    });\n  }\n  /**\r\n   * Změna filtru dnů\r\n   */\n  onDaysChange(days) {\n    this.selectedDays = days;\n    // Uložení vybraného počtu dní do local storage\n    localStorage.setItem('monitoring_dateRange', days.toString());\n    this.refreshCharts();\n  }\n  /**\r\n   * Změna filtru instance\r\n   */\n  onInstanceChange(instanceId) {\n    this.selectedInstanceId = instanceId;\n    this.refreshCharts();\n  }\n  /**\r\n   * Získání textu pro typ alertu\r\n   */\n  getAlertTypeText(alertType) {\n    switch (alertType) {\n      case 'SecurityBreach':\n        return 'Bezpečnostní incident';\n      case 'CertificateExpiration':\n        return 'Expirace certifikátu';\n      case 'FailedConnectionAttempts':\n        return 'Selhané připojení';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      default:\n        return alertType;\n    }\n  }\n  /**\r\n   * Přepnutí režimu úprav dashboardu\r\n   */\n  toggleEditMode() {\n    this.editMode = !this.editMode;\n  }\n  /**\r\n   * Zpracování přetažení widgetu\r\n   */\n  onDrop(event) {\n    if (event.previousIndex === event.currentIndex) {\n      return;\n    }\n    // Získáme všechny viditelné widgety v pořadí, jak jsou zobrazeny v DOM\n    const visibleWidgets = Array.from(document.querySelectorAll('.col-lg-6[cdkDrag]')).map(element => {\n      // Získáme ID widgetu z atributu *ngIf\n      const widget = this.widgets.find(w => {\n        const widgetId = w.id;\n        return element.innerHTML.includes(`isWidgetVisible('${widgetId}')`);\n      });\n      return widget;\n    }).filter(widget => widget !== undefined);\n    // Provedeme přesun v poli viditelných widgetů\n    moveItemInArray(visibleWidgets, event.previousIndex, event.currentIndex);\n    // Aktualizujeme pozice všech widgetů\n    let position = 0;\n    // Nejprve aktualizujeme pozice viditelných widgetů\n    visibleWidgets.forEach(visibleWidget => {\n      const widget = this.widgets.find(w => w.id === visibleWidget.id);\n      if (widget) {\n        widget.position = position++;\n      }\n    });\n    // Poté aktualizujeme pozice skrytých widgetů\n    this.widgets.filter(widget => !visibleWidgets.some(vw => vw.id === widget.id)).forEach(widget => {\n      widget.position = position++;\n    });\n    // Seřadíme widgety podle pozice\n    this.widgets.sort((a, b) => a.position - b.position);\n    // Uložení konfigurace\n    this.saveDashboardConfig();\n  }\n  /**\r\n   * Změna viditelnosti widgetu\r\n   */\n  toggleWidgetVisibility(widgetId) {\n    const widget = this.widgets.find(w => w.id === widgetId);\n    if (widget) {\n      widget.visible = !widget.visible;\n      this.saveDashboardConfig();\n    }\n  }\n  /**\r\n   * Kontrola viditelnosti widgetu\r\n   */\n  isWidgetVisible(widgetId) {\n    const widget = this.widgets.find(w => w.id === widgetId);\n    return widget ? widget.visible : true;\n  }\n  /**\r\n   * Uložení konfigurace dashboardu\r\n   */\n  saveDashboardConfig() {\n    if (!this.dashboardConfig) {\n      return;\n    }\n    this.dashboardConfig.widgets = [...this.widgets];\n    this.dashboardConfig.lastModified = new Date();\n    this.dashboardConfigService.saveDashboardConfig(this.dashboardConfig).subscribe({\n      next: config => {\n        console.log('Konfigurace dashboardu byla úspěšně uložena');\n      },\n      error: error => {\n        console.error('Chyba při ukládání konfigurace dashboardu', error);\n      }\n    });\n  }\n  /**\r\n   * Reset konfigurace dashboardu na výchozí hodnoty\r\n   */\n  resetDashboardConfig() {\n    if (confirm('Opravdu chcete resetovat dashboard na výchozí nastavení?')) {\n      this.dashboardConfigService.resetDashboardConfig().subscribe({\n        next: config => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n          this.editMode = false;\n          this.refreshCharts();\n        },\n        error: error => {\n          console.error('Chyba při resetování konfigurace dashboardu', error);\n        }\n      });\n    }\n  }\n  /**\r\n   * Otevře modální okno s grafem v režimu \"full screen\"\r\n   * @param chart Instance grafu nebo reference na canvas element\r\n   * @param title Titulek grafu\r\n   */\n  openFullscreenChart(chart, title) {\n    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart\n    if (chart instanceof HTMLCanvasElement) {\n      // Najdeme instanci Chart pro daný canvas element\n      const chartInstance = Chart.getChart(chart);\n      this.chartModalService.openChartModal(chartInstance || null, title);\n    } else {\n      this.chartModalService.openChartModal(chart, title);\n    }\n  }\n  /**\r\n   * Inicializace popoverů pro nápovědu\r\n   */\n  initPopovers() {\n    // Nejprve zničíme existující popovers\n    this.destroyPopovers();\n    // Definice obsahu nápověd\n    const helpContent = {\n      'api-performance': 'Graf zobrazuje top 10 nejpomalejších metod seřazených podle 95. percentilu doby odezvy. ' + 'Pro každou metodu jsou zobrazeny dva sloupce: průměrná doba odezvy (modrý sloupec) a maximální doba odezvy (červený sloupec). ' + 'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',\n      'api-calls': 'Graf zobrazuje počet DISApi volání v čase pro vybrané období. ' + 'Zobrazuje volání z DIS aplikací na DISAdmin.DISApi službu. ' + 'Data jsou získávána z tabulky ActivityLogs za zvolené období.',\n      'instances-usage': 'Graf zobrazuje celkový počet volání DIS metod podle instancí. ' + 'Ukazuje, které instance jsou nejvíce zatížené podle skutečného počtu volání metod. ' + 'Data jsou získávána z tabulky PerformanceMetrics jako suma TotalCount za zvolené období.',\n      'production-performance': 'Graf zobrazuje průměrnou dobu odezvy a 95. percentil pouze pro produkční instance. ' + 'Produkční instance jsou kritické pro běžný provoz a jejich výkon je klíčový pro uživatelskou spokojenost. ' + '95. percentil ukazuje, že 95% všech volání bylo rychlejších než zobrazená hodnota, což je lepší indikátor než prostý průměr. ' + 'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',\n      'security-events': 'Graf zobrazuje počet bezpečnostních událostí v čase. ' + 'Zahrnuje události jako neúspěšné pokusy o přihlášení, podezřelé aktivity a porušení bezpečnosti. ' + 'Data jsou získávána z tabulky SecurityLogs za zvolené období.',\n      'system-info': 'Tento widget zobrazuje souhrnné systémové informace včetně počtu aktivních upozornění, ' + 'dostupnosti API, průměrné odezvy DIS metod a počtu expirujících certifikátů. ' + 'Poskytuje rychlý přehled o celkovém stavu systému.',\n      // Nápovědy pro karty systémových informací\n      'alerts-count': 'Počet aktivních upozornění v systému. Upozornění jsou automaticky generována při detekci problémů s výkonem, ' + 'bezpečností nebo dostupností služeb. Kliknutím přejdete na modul Alerty a upozornění.',\n      'security-events-count': 'Počet nevyřešených bezpečnostních událostí včetně neúspěšných pokusů o přihlášení, ' + 'podezřelých aktivit a narušení bezpečnosti. Kliknutím přejdete na modul Bezpečnostní události.',\n      'api-availability': 'Procentuální dostupnost DIS API za posledních 24 hodin. Počítá se jako poměr úspěšných volání ' + 'k celkovému počtu volání API. Data jsou získávána z tabulky ActivityLogs. Kliknutím přejdete na modul Monitoring.',\n      'api-response-time': 'Průměrná doba odezvy DIS API metod v milisekundách za posledních 24 hodin. ' + 'Nižší hodnota znamená lepší výkon systému. Data jsou získávána z tabulky PerformanceMetrics. ' + 'Kliknutím přejdete na modul Výkon DIS.',\n      'expiring-certificates': 'Počet certifikátů, které vyprší během následujících 30 dnů. ' + 'Včasná obnova certifikátů je kritická pro bezpečnost a nepřerušený provoz systému. ' + 'Kliknutím přejdete na modul Správa certifikátů.'\n    };\n    // Nejprve zrušíme všechny existující popovery\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popover = bootstrap.Popover.getInstance(el);\n      if (popover) {\n        popover.dispose();\n      }\n    });\n    // Inicializace popoverů pomocí Bootstrap API\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const helpType = el.getAttribute('data-help-type');\n      //console.log('Initializing popover for element with help-type:', helpType);\n      if (helpType && helpType in helpContent) {\n        try {\n          const popover = new bootstrap.Popover(el, {\n            content: helpContent[helpType],\n            html: true,\n            trigger: 'hover focus',\n            placement: 'top',\n            container: 'body',\n            sanitize: false\n          });\n          // Přidáme event listener pro kliknutí na klikatelné karty\n          if (el.classList.contains('clickable-card') || el.closest('.clickable-card')) {\n            el.addEventListener('click', () => {\n              popover.hide();\n            });\n          }\n        } catch (error) {\n          console.error('Error initializing popover:', error);\n        }\n      } else if (helpType) {\n        console.warn('Help content not found for type:', helpType);\n      }\n    });\n    // Přidáme globální event listener pro zavření popoverů při kliknutí kamkoliv\n    this.documentClickListener = event => {\n      const target = event.target;\n      // Pokud se nekliklo na element s popoverem nebo na samotný popover, zavřeme všechny popovers\n      if (!target.closest('[data-bs-toggle=\"popover\"]') && !target.closest('.popover')) {\n        this.hideAllPopovers();\n      }\n    };\n    document.addEventListener('click', this.documentClickListener);\n  }\n  /**\r\n   * Zavře všechny aktivní popovers\r\n   */\n  hideAllPopovers() {\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popoverInstance = bootstrap.Popover.getInstance(el);\n      if (popoverInstance) {\n        popoverInstance.hide();\n      }\n    });\n  }\n  /**\r\n   * Zničí všechny popovers\r\n   */\n  destroyPopovers() {\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popoverInstance = bootstrap.Popover.getInstance(el);\n      if (popoverInstance) {\n        popoverInstance.dispose();\n      }\n    });\n  }\n  /**\r\n   * Navigace na modul Alerty a upozornění\r\n   */\n  navigateToAlerts() {\n    this.hideAllPopovers();\n    this.router.navigate(['/alerts']);\n  }\n  /**\r\n   * Navigace na modul Bezpečnostní události\r\n   */\n  navigateToSecurityEvents() {\n    this.hideAllPopovers();\n    this.router.navigate(['/security']);\n  }\n  /**\r\n   * Navigace na modul Certifikáty\r\n   */\n  navigateToCertificates() {\n    this.hideAllPopovers();\n    this.router.navigate(['/certificates']);\n  }\n  /**\r\n   * Navigace na modul Monitoring\r\n   */\n  navigateToMonitoring() {\n    this.hideAllPopovers();\n    this.router.navigate(['/monitoring']);\n  }\n  /**\r\n   * Navigace na modul Výkon DIS\r\n   */\n  navigateToPerformance() {\n    this.hideAllPopovers();\n    this.router.navigate(['/performance']);\n  }\n  /**\r\n   * Inteligentní zkrácení názvu metody pro zobrazení v grafu\r\n   * @param text Text k zkrácení\r\n   * @param maxLength Maximální délka (výchozí 30 znaků)\r\n   * @returns Zkrácený text s \"...\" pokud je potřeba\r\n   */\n  truncateMethodName(text, maxLength = 30) {\n    if (!text || text.length <= maxLength) {\n      return text;\n    }\n    // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n    const truncated = text.substring(0, maxLength - 3);\n    const lastDotIndex = truncated.lastIndexOf('.');\n    // Pokud najdeme tečku a není příliš na začátku, zkrátíme na ní\n    if (lastDotIndex > 0 && lastDotIndex > maxLength * 0.4) {\n      return text.substring(0, lastDotIndex) + '...';\n    }\n    // Jinak zkrátíme na pevné délce\n    return truncated + '...';\n  }\n  /**\r\n   * Vytvoří konfiguraci pro zkrácení popisků na ose X\r\n   * @param originalLabels Originální popisky\r\n   * @param options Možnosti konfigurace\r\n   * @returns Objekt s konfigurací pro Chart.js\r\n   */\n  createTruncatedLabelsConfig(originalLabels, options = {}) {\n    const config = {\n      enabled: options.enabled ?? true,\n      maxLength: options.maxLength ?? 30,\n      showFullInTooltip: options.showFullInTooltip ?? true\n    };\n    if (!config.enabled) {\n      return {};\n    }\n    const result = {\n      scales: {\n        x: {\n          ticks: {\n            callback: (value, index) => {\n              const label = originalLabels[index];\n              return this.truncateMethodName(label, config.maxLength);\n            }\n          }\n        }\n      }\n    };\n    if (config.showFullInTooltip) {\n      result.plugins = {\n        tooltip: {\n          callbacks: {\n            title: context => {\n              const dataIndex = context[0].dataIndex;\n              return originalLabels[dataIndex] || context[0].label;\n            }\n          }\n        }\n      };\n    }\n    return result;\n  }\n  /**\r\n   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu\r\n   * @param baseConfig Základní konfigurace grafu\r\n   * @param truncatedConfig Konfigurace pro zkrácené popisky\r\n   * @returns Sloučená konfigurace\r\n   */\n  mergeChartConfig(baseConfig, truncatedConfig) {\n    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {\n      return baseConfig;\n    }\n    const merged = {\n      ...baseConfig\n    };\n    // Sloučení scales\n    if (truncatedConfig.scales) {\n      merged.scales = merged.scales || {};\n      if (truncatedConfig.scales.x) {\n        merged.scales.x = merged.scales.x || {};\n        merged.scales.x.ticks = {\n          ...merged.scales.x.ticks,\n          ...truncatedConfig.scales.x.ticks\n        };\n      }\n    }\n    // Sloučení plugins\n    if (truncatedConfig.plugins) {\n      merged.plugins = merged.plugins || {};\n      if (truncatedConfig.plugins.tooltip) {\n        merged.plugins.tooltip = merged.plugins.tooltip || {};\n        merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};\n        // Sloučení tooltip callbacks - zachováme existující a přidáme nové\n        Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {\n          merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];\n        });\n      }\n    }\n    return merged;\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ChartService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.AlertService), i0.ɵɵdirectiveInject(i4.DashboardConfigService), i0.ɵɵdirectiveInject(i5.ChartModalService), i0.ɵɵdirectiveInject(i6.MonitoringService), i0.ɵɵdirectiveInject(i7.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      viewQuery: function DashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.apiCallsChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.apiPerformanceChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.instancesUsageChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.productionPerformanceChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.securityEventsChartRef = _t.first);\n        }\n      },\n      decls: 34,\n      vars: 36,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"d-flex\", \"gap-2\", \"flex-wrap\"], [\"role\", \"group\", 1, \"btn-group\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-1\"], [1, \"d-none\", \"d-sm-inline\"], [1, \"btn\", 3, \"click\"], [1, \"bi\", 3, \"ngClass\"], [\"class\", \"btn btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"alert alert-info mb-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"cdkDropList\", \"\", 1, \"row\", 3, \"cdkDropListDisabled\", \"cdkDropListDropped\"], [\"class\", \"col-lg-6 mb-4\", \"cdkDrag\", \"\", 3, \"cdkDragDisabled\", 4, \"ngIf\"], [\"class\", \"row\", \"cdkDrag\", \"\", 3, \"cdkDragDisabled\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-arrow-counterclockwise\", \"me-1\"], [1, \"alert\", \"alert-info\", \"mb-4\"], [1, \"bi\", \"bi-info-circle-fill\", \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\"], [\"cdkDrag\", \"\", 1, \"col-lg-6\", \"mb-4\", 3, \"cdkDragDisabled\"], [1, \"card\", \"h-100\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-calls\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"title\", \"Zobrazit graf na celou obrazovku\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-arrows-fullscreen\"], [1, \"card-body\"], [1, \"chart-container\", 2, \"position\", \"relative\", \"height\", \"300px\"], [\"apiCallsChart\", \"\"], [\"class\", \"drag-handle\", \"cdkDragHandle\", \"\", 4, \"ngIf\"], [\"cdkDragHandle\", \"\", 1, \"drag-handle\"], [1, \"bi\", \"bi-grip-horizontal\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-performance\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"apiPerformanceChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"instances-usage\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"instancesUsageChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"production-performance\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"productionPerformanceChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"security-events\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"securityEventsChart\", \"\"], [\"cdkDrag\", \"\", 1, \"row\", 3, \"cdkDragDisabled\"], [1, \"col-12\", \"mb-4\"], [1, \"card\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"system-info\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"class\", \"widget-controls\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-6\", \"col-lg-2-4\", \"mb-3\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"alerts-count\", 1, \"card\", \"bg-primary\", \"text-white\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"card-body\", \"text-center\", \"compact-card\"], [1, \"h2\", \"mb-1\"], [1, \"mb-0\", \"card-label\"], [\"class\", \"col-6 col-lg-2-4 mb-3\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-availability\", 1, \"card\", \"bg-success\", \"text-white\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-response-time\", 1, \"card\", \"bg-info\", \"text-white\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"expiring-certificates\", 1, \"card\", \"bg-warning\", \"text-dark\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"widget-controls\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"bi\", \"bi-eye-slash\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"security-events-count\", 1, \"card\", \"bg-danger\", \"text-white\", \"clickable-card\", 2, \"cursor\", \"pointer\", 3, \"click\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_6_listener() {\n            return ctx.onDaysChange(1);\n          });\n          i0.ɵɵtext(7, \"1 den\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_8_listener() {\n            return ctx.onDaysChange(7);\n          });\n          i0.ɵɵtext(9, \"7 dn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_10_listener() {\n            return ctx.onDaysChange(30);\n          });\n          i0.ɵɵtext(11, \"30 dn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_12_listener() {\n            return ctx.onDaysChange(90);\n          });\n          i0.ɵɵtext(13, \"90 dn\\u00ED\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 2)(15, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_15_listener() {\n            return ctx.refreshCharts();\n          });\n          i0.ɵɵelement(16, \"i\", 6);\n          i0.ɵɵelementStart(17, \"span\", 7);\n          i0.ɵɵtext(18, \"Aktualizovat\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_19_listener() {\n            return ctx.toggleEditMode();\n          });\n          i0.ɵɵelement(20, \"i\", 9);\n          i0.ɵɵelementStart(21, \"span\", 7);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, DashboardComponent_button_23_Template, 4, 0, \"button\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(24, DashboardComponent_div_24_Template, 3, 0, \"div\", 11);\n          i0.ɵɵtemplate(25, DashboardComponent_div_25_Template, 4, 0, \"div\", 12);\n          i0.ɵɵtemplate(26, DashboardComponent_div_26_Template, 2, 1, \"div\", 13);\n          i0.ɵɵelementStart(27, \"div\", 14);\n          i0.ɵɵlistener(\"cdkDropListDropped\", function DashboardComponent_Template_div_cdkDropListDropped_27_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(28, DashboardComponent_div_28_Template, 12, 4, \"div\", 15);\n          i0.ɵɵtemplate(29, DashboardComponent_div_29_Template, 12, 4, \"div\", 15);\n          i0.ɵɵtemplate(30, DashboardComponent_div_30_Template, 12, 4, \"div\", 15);\n          i0.ɵɵtemplate(31, DashboardComponent_div_31_Template, 12, 4, \"div\", 15);\n          i0.ɵɵtemplate(32, DashboardComponent_div_32_Template, 12, 4, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, DashboardComponent_div_33_Template, 39, 10, \"div\", 16);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 1)(\"btn-outline-primary\", ctx.selectedDays !== 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 7)(\"btn-outline-primary\", ctx.selectedDays !== 7);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 30)(\"btn-outline-primary\", ctx.selectedDays !== 30);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 90)(\"btn-outline-primary\", ctx.selectedDays !== 90);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"btn-outline-warning\", !ctx.editMode)(\"btn-warning\", ctx.editMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(33, _c5, !ctx.editMode, ctx.editMode));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.editMode ? \"Dokon\\u010Dit \\u00FApravy\" : \"Upravit dashboard\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"cdkDropListDisabled\", !ctx.editMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"api-calls\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"api-performance\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"instances-usage\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"production-performance\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin && ctx.isWidgetVisible(\"security-events\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin && ctx.isWidgetVisible(\"system-info\"));\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgIf, i9.CdkDropList, i9.CdkDrag, i9.CdkDragHandle],\n      styles: [\".chart-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: auto;\\n  height: 300px;\\n  width: 100%;\\n}\\n\\n\\n.draggable-card[_ngcontent-%COMP%] {\\n  cursor: move;\\n  transition: box-shadow 0.2s ease;\\n  position: relative;\\n}\\n\\n.draggable-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.drag-handle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 24px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: move;\\n  border-bottom-left-radius: 0.25rem;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n\\n.drag-handle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #6c757d;\\n}\\n\\n.widget-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n\\n.cdk-drag-preview[_ngcontent-%COMP%] {\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);\\n  opacity: 0.8;\\n  z-index: 1000;\\n}\\n\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0.3;\\n  background-color: rgba(0, 123, 255, 0.1);\\n  border: 2px dashed #0d6efd;\\n  border-radius: 8px;\\n}\\n\\n.cdk-drag-animating[_ngcontent-%COMP%] {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n.row.cdk-drop-list-dragging[_ngcontent-%COMP%]   .col-lg-6[_ngcontent-%COMP%]:not(.cdk-drag-placeholder) {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  background-color: rgba(13, 110, 253, 0.2);\\n  border: 2px dashed #0d6efd;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  padding: 0.5em 0.75em;\\n}\\n\\n\\n.alert-type-badge[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n}\\n\\n.alert-type-security-breach[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n  color: white;\\n}\\n\\n.alert-type-certificate-expiration[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  color: #212529;\\n}\\n\\n.alert-type-failed-connection[_ngcontent-%COMP%] {\\n  background-color: #fd7e14;\\n  color: white;\\n}\\n\\n.alert-type-suspicious-activity[_ngcontent-%COMP%] {\\n  background-color: #6f42c1;\\n  color: white;\\n}\\n\\n.severity-badge[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n}\\n\\n.severity-high[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n  color: white;\\n}\\n\\n.severity-medium[_ngcontent-%COMP%] {\\n  background-color: #fd7e14;\\n  color: white;\\n}\\n\\n.severity-low[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  color: #212529;\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .chart-container[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n\\n  .display-4[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n\\n  .card-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n\\n  .row[_ngcontent-%COMP%]    > [class*=\\\"col-\\\"][_ngcontent-%COMP%] {\\n    padding-left: 8px;\\n    padding-right: 8px;\\n  }\\n\\n  .row[_ngcontent-%COMP%] {\\n    margin-left: -8px;\\n    margin-right: -8px;\\n  }\\n\\n  .btn-group.role-group[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n\\n  .btn-group.role-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n    padding: 0.375rem 0.5rem;\\n    margin-bottom: 5px;\\n  }\\n\\n  .card[_ngcontent-%COMP%]   h5.mb-0[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .card[_ngcontent-%COMP%]   p.mb-0[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .widget-controls[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: 0.5rem;\\n    right: 0.5rem;\\n  }\\n}\\n\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  background-color: #2b3035;\\n  border-color: #373b3e;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  color: #e9ecef;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-bottom-color: #373b3e;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], body.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top-color: #373b3e;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.075);\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n  border-color: #0d6efd;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0d6efd;\\n  color: #fff;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%] {\\n  color: #198754;\\n  border-color: #198754;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]:hover {\\n  background-color: #198754;\\n  color: #fff;\\n}\\n\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .draggable-card[_ngcontent-%COMP%] {\\n  background-color: #1a2530;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .drag-handle[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.05);\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .drag-handle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n}\\n\\n\\n.clickable-card[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;\\n}\\n\\n.clickable-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px) scale(1.02);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);\\n  opacity: 0.9;\\n}\\n\\n.clickable-card[_ngcontent-%COMP%]:active {\\n  transform: translateY(-1px) scale(1.01);\\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .clickable-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n@media (min-width: 992px) {\\n  .col-lg-2-4[_ngcontent-%COMP%] {\\n    flex: 0 0 20%;\\n    max-width: 20%;\\n  }\\n}\\n\\n\\n@media (min-width: 992px) {\\n  .col-lg-2-4[_ngcontent-%COMP%]   .compact-card[_ngcontent-%COMP%] {\\n    padding: 0.75rem 0.25rem;\\n  }\\n\\n  .col-lg-2-4[_ngcontent-%COMP%]   .h2[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .col-lg-2-4[_ngcontent-%COMP%]   .card-label[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n    line-height: 1.2;\\n  }\\n}\\n\\n\\n.card-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  line-height: 1.3;\\n  font-weight: 500;\\n}\\n\\n\\n.compact-card[_ngcontent-%COMP%] {\\n  padding: 0.75rem 0.3rem;\\n}\\n\\n\\n@media (max-width: 991px) {\\n  .compact-card[_ngcontent-%COMP%] {\\n    padding: 0.75rem 0.5rem;\\n  }\\n\\n  .card-label[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n}\\n\\n\\n@media (max-width: 576px) {\\n  .col-lg-2-4[_ngcontent-%COMP%]   .card-label[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    line-height: 1.2;\\n  }\\n\\n  .col-lg-2-4[_ngcontent-%COMP%]   .h2[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAOA,SAASA,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAE/C,SAAsBC,eAAe,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;;;ICS7DC,kCAAyF;IAAjCA;MAAAA;MAAA;MAAA,OAASA,6CAAsB;IAAA,EAAC;IACtFA,wBAAiD;IAACA,+BAAiC;IAAAA,yBAAS;IAAAA,iBAAO;;;;;IAM3GA,+BAAoD;IAClDA,wBAA2C;IAC3CA,iNACF;IAAAA,iBAAM;;;;;IAENA,+BAAgE;IAE9BA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAA8C;IAC5CA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;IAoBMA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAfVA,+BAAsG;IAGLA,mDAAoB;IAAAA,iBAAK;IACpHA,kCAA0J;IAA9GA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAmC,qCAAsB,CAAC;IAAA,EAAC;IAC9GA,wBAAuC;IACzCA,iBAAS;IAEXA,+BAAuB;IAEnBA,mCAAgC;IAClCA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAhBgEA,kDAA6B;IAC3EA,eAAiC;IAAjCA,iDAAiC;IAYjDA,gBAAc;IAAdA,sCAAc;;;;;IAoBpBA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAfVA,+BAA4G;IAGLA,gCAAgB;IAAAA,iBAAK;IACtHA,kCAA4J;IAAhHA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAyC,kBAAkB,CAAC;IAAA,EAAC;IAChHA,wBAAuC;IACzCA,iBAAS;IAEXA,+BAAuB;IAEnBA,mCAAsC;IACxCA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAhBsEA,kDAA6B;IACjFA,eAAiC;IAAjCA,iDAAiC;IAYjDA,gBAAc;IAAdA,sCAAc;;;;;IAoBpBA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAfVA,+BAA4G;IAGLA,8DAA+B;IAAAA,iBAAK;IACrIA,kCAA2K;IAA/HA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAyC,gDAAiC,CAAC;IAAA,EAAC;IAC/HA,wBAAuC;IACzCA,iBAAS;IAEXA,+BAAuB;IAEnBA,mCAAsC;IACxCA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAhBsEA,kDAA6B;IACjFA,eAAiC;IAAjCA,iDAAiC;IAYjDA,gBAAc;IAAdA,sCAAc;;;;;IAoBpBA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAfVA,+BAAmH;IAGLA,kGAAoD;IAAAA,iBAAK;IACjKA,kCAAuM;IAA3JA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAgD,oFAAsD,CAAC;IAAA,EAAC;IAC3JA,wBAAuC;IACzCA,iBAAS;IAEXA,+BAAuB;IAEnBA,mCAA6C;IAC/CA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAhB6EA,kDAA6B;IACxFA,eAAiC;IAAjCA,iDAAiC;IAYjDA,gBAAc;IAAdA,sCAAc;;;;;IAoBpBA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAfVA,+BAAuH;IAGhBA,oDAAqB;IAAAA,iBAAK;IAC3HA,kCAAiK;IAArHA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAyC,sCAAuB,CAAC;IAAA,EAAC;IACrHA,wBAAuC;IACzCA,iBAAS;IAEXA,+BAAuB;IAEnBA,mCAAsC;IACxCA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAhBiFA,kDAA6B;IAC5FA,eAAiC;IAAjCA,iDAAiC;IAYjDA,gBAAc;IAAdA,sCAAc;;;;;;IAalBA,+BAA8C;IACKA;MAAAA;MAAA;MAAA,OAASA,8CAAuB,aAAa,CAAC;IAAA,EAAC;IAC9FA,wBAA+B;IACjCA,iBAAS;;;;;;IAiBTA,+BAAmD;IAE5CA;MAAAA;MAAA;MAAA,OAASA,iDAA0B;IAAA,EAAC;IAIvCA,+BAAgD;IAC1BA,YAA+C;IAAAA,iBAAK;IACxEA,6BAA2B;IAAAA,oEAA2B;IAAAA,iBAAI;;;;IADtCA,eAA+C;IAA/CA,uEAA+C;;;;;IA2C7EA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IA7EZA,+BAAyG;IAIJA,6CAAmB;IAAAA,iBAAK;IACrHA,2EAIM;IACRA,iBAAM;IACNA,+BAAuB;IAIZA;MAAAA;MAAA;MAAA,OAASA,yCAAkB;IAAA,EAAC;IAI/BA,gCAAgD;IAC1BA,aAAuC;IAAAA,iBAAK;IAChEA,8BAA2B;IAAAA,kDAAkB;IAAAA,iBAAI;IAIvDA,6EAWM;IACNA,gCAAmC;IAE5BA;MAAAA;MAAA;MAAA,OAASA,6CAAsB;IAAA,EAAC;IAInCA,gCAAgD;IAC1BA,aAA8C;IAAAA,iBAAK;IACvEA,8BAA2B;IAAAA,mCAAkB;IAAAA,iBAAI;IAIvDA,gCAAmC;IAE5BA;MAAAA;MAAA;MAAA,OAASA,8CAAuB;IAAA,EAAC;IAIpCA,gCAAgD;IAC1BA,aAAgD;IAAAA,iBAAK;IACzEA,8BAA2B;IAAAA,yDAAyB;IAAAA,iBAAI;IAI9DA,gCAAmC;IAE5BA;MAAAA;MAAA;MAAA,OAASA,+CAAwB;IAAA,EAAC;IAIrCA,gCAAgD;IAC1BA,aAAqD;IAAAA,iBAAK;IAC9EA,8BAA2B;IAAAA,sDAAsB;IAAAA,iBAAI;IAM/DA,6EAEM;IACRA,iBAAM;;;;IA9EiEA,kDAA6B;IAElFA,eAAiC;IAAjCA,iDAAiC;IAGzCA,eAAc;IAAdA,sCAAc;IAeQA,eAAuC;IAAvCA,8DAAuC;IAK7BA,eAAa;IAAbA,qCAAa;IAmBvBA,eAA8C;IAA9CA,8EAA8C;IAY9CA,eAAgD;IAAhDA,gFAAgD;IAYhDA,eAAqD;IAArDA,4EAAqD;IAO7EA,eAAc;IAAdA,sCAAc;;;;;;;;;ADhN5B;AACAH,KAAK,CAACI,QAAQ,CAAC,GAAGH,aAAa,CAAC;AAUhC,OAAM,MAAOI,kBAAkB;EAqC7BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,YAA0B,EAC1BC,sBAA8C,EAC9CC,iBAAoC,EACpCC,iBAAoC,EACpCC,MAAc;IANd,iBAAY,GAAZN,YAAY;IACZ,gBAAW,GAAXC,WAAW;IACX,iBAAY,GAAZC,YAAY;IACZ,2BAAsB,GAAtBC,sBAAsB;IACtB,sBAAiB,GAAjBC,iBAAiB;IACjB,sBAAiB,GAAjBC,iBAAiB;IACjB,WAAM,GAANC,MAAM;IArChB,YAAO,GAAG,IAAI;IACd,UAAK,GAAkB,IAAI;IAE3B,YAAO,GAAG,KAAK;IACf,WAAM,GAAU,EAAE;IAClB,qBAAgB,GAAQ,EAAE;IAC1B,qBAAgB,GAAQ,EAAE;IAE1B;IACA,kBAAa,GAAiB,IAAI;IAClC,wBAAmB,GAAiB,IAAI;IACxC,wBAAmB,GAAiB,IAAI;IACxC,+BAA0B,GAAiB,IAAI;IAC/C,wBAAmB,GAAiB,IAAI;IAExC;IACA,iBAAY,GAAG,EAAE;IACjB,uBAAkB,GAAkB,IAAI;IAExC;IACA,oBAAe,GAA2B,IAAI;IAC9C,YAAO,GAAsB,EAAE;IAC/B,aAAQ,GAAG,KAAK;IAEhB;IACQ,uBAAkB,GAAwB,IAAI;IAEtD;IACQ,0BAAqB,GAAoC,IAAI;IAWnE,IAAI,CAACL,WAAW,CAACM,WAAW,CAACC,SAAS,CAACC,IAAI,IAAG;MAC5C,IAAI,CAACF,WAAW,GAAGE,IAAI;MACvB,IAAI,CAACC,OAAO,GAAGD,IAAI,EAAEC,OAAO,IAAI,KAAK;IACvC,CAAC,CAAC;IAEF;IACA,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IACnE,IAAIF,cAAc,EAAE;MAClB,IAAI,CAACG,YAAY,GAAGC,QAAQ,CAACJ,cAAc,EAAE,EAAE,CAAC;;EAEpD;EAEAK,QAAQ;IACN;IACA,IAAI,CAACC,mBAAmB,EAAE;IAE1B,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;;;EAGAH,mBAAmB;IACjB,IAAI,CAACd,sBAAsB,CAACkB,sBAAsB,EAAE,CAACb,SAAS,CAAC;MAC7Dc,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACC,eAAe,GAAGD,MAAM;QAC7B,IAAI,CAACE,OAAO,GAAG,CAAC,GAAGF,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;MAC5E,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE;QACA,IAAI,CAAC3B,sBAAsB,CAAC6B,oBAAoB,EAAE,CAACxB,SAAS,CAACe,MAAM,IAAG;UACpE,IAAI,CAACC,eAAe,GAAGD,MAAM;UAC7B,IAAI,CAACE,OAAO,GAAG,CAAC,GAAGF,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;QAC5E,CAAC,CAAC;MACJ;KACD,CAAC;EACJ;EAEAI,eAAe;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,UAAU,EAAE;MACjB;MACAD,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAW;IACT;IACA,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACC,WAAW,EAAE;;IAGvC;IACA,IAAI,IAAI,CAACC,qBAAqB,EAAE;MAC9BC,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,qBAAqB,CAAC;MACjE,IAAI,CAACA,qBAAqB,GAAG,IAAI;;IAGnC;IACA,IAAI,CAACG,eAAe,EAAE;IAEtB;IACA,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;EAGA1B,UAAU;IACR,IAAI,CAAChB,YAAY,CAAC2C,kBAAkB,EAAE,CAACrC,SAAS,CAAC;MAC/Cc,IAAI,EAAGwB,IAAI,IAAI;QACb,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACE,MAAM,CAAEC,KAAU,IAAK,CAACA,KAAK,CAACC,UAAU,CAAC;MAC9D,CAAC;MACDpB,KAAK,EAAGqB,GAAG,IAAI;QACbpB,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEqB,GAAG,CAAC;QACnD,IAAI,CAACrB,KAAK,GAAG,iCAAiC;MAChD;KACD,CAAC;EACJ;EAEA;;;EAGAX,oBAAoB;IAClB,IAAI,CAACd,iBAAiB,CAAC+C,mBAAmB,EAAE,CAAC5C,SAAS,CAAC;MACrDc,IAAI,EAAGwB,IAAI,IAAI;QACb,IAAI,CAACO,gBAAgB,GAAGP,IAAI;MAC9B,CAAC;MACDhB,KAAK,EAAGqB,GAAG,IAAI;QACbpB,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEqB,GAAG,CAAC;QAC9D,IAAI,CAACrB,KAAK,GAAG,2CAA2C;MAC1D;KACD,CAAC;EACJ;EAEA;;;EAGAV,oBAAoB;IAClB,IAAI,CAACf,iBAAiB,CAACiD,mBAAmB,EAAE,CAAC9C,SAAS,CAAC;MACrDc,IAAI,EAAGwB,IAAI,IAAI;QACb,IAAI,CAACS,gBAAgB,GAAGT,IAAI;MAC9B,CAAC;MACDhB,KAAK,EAAGqB,GAAG,IAAI;QACbpB,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEqB,GAAG,CAAC;QAC1D,IAAI,CAACrB,KAAK,GAAG,wCAAwC;MACvD;KACD,CAAC;EACJ;EAEA;;;EAGAK,UAAU;IACR,IAAI,CAACqB,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,8BAA8B,EAAE;IAErC,IAAI,IAAI,CAAClD,OAAO,EAAE;MAChB,IAAI,CAACmD,uBAAuB,EAAE;;IAGhC,IAAI,CAACL,OAAO,GAAG,KAAK;EACtB;EAEA;;;EAGAM,aAAa;IACX,IAAI,CAACL,iBAAiB,EAAE;IACxB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,8BAA8B,EAAE;IAErC,IAAI,IAAI,CAAClD,OAAO,EAAE;MAChB,IAAI,CAACmD,uBAAuB,EAAE;;IAGhC,IAAI,CAAC3C,UAAU,EAAE;IACjB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,oBAAoB,EAAE;IAE3B;IACAc,UAAU,CAAC,MAAK;MACd,IAAI,CAACE,YAAY,EAAE;IACrB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGAQ,aAAa;IACX,IAAI,IAAI,CAACmB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;MAC5B,IAAI,CAACD,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACD,OAAO,EAAE;MAClC,IAAI,CAACC,mBAAmB,GAAG,IAAI;;IAGjC,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACF,OAAO,EAAE;MAClC,IAAI,CAACE,mBAAmB,GAAG,IAAI;;IAGjC,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACH,OAAO,EAAE;MAClC,IAAI,CAACG,mBAAmB,GAAG,IAAI;;EAEnC;EAEA;;;EAGAV,iBAAiB;IACf,IAAI,CAACzD,YAAY,CAACoE,oBAAoB,CAAC,IAAI,CAACC,kBAAkB,IAAIC,SAAS,EAAE,IAAI,CAACxD,YAAY,CAAC,CAACN,SAAS,CAAC;MACxGc,IAAI,EAAGwB,IAAI,IAAI;QACb,IAAI,IAAI,CAACiB,aAAa,EAAE;UACtB,IAAI,CAACA,aAAa,CAACjB,IAAI,CAACyB,MAAM,GAAGzB,IAAI,CAACyB,MAAM;UAC5C,IAAI,CAACR,aAAa,CAACjB,IAAI,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC1B,IAAI,GAAGA,IAAI,CAACA,IAAI;UACpD,IAAI,CAACiB,aAAa,CAACU,MAAM,EAAE;SAC5B,MAAM,IAAI,IAAI,CAACC,gBAAgB,EAAE;UAChC,IAAI,CAACX,aAAa,GAAG,IAAItE,KAAK,CAAC,IAAI,CAACiF,gBAAgB,CAACC,aAAa,EAAE;YAClEC,IAAI,EAAE,MAAM;YACZ9B,IAAI,EAAE;cACJyB,MAAM,EAAEzB,IAAI,CAACyB,MAAM;cACnBC,QAAQ,EAAE,CAAC;gBACTK,KAAK,EAAE,qBAAqB;gBAC5B/B,IAAI,EAAEA,IAAI,CAACA,IAAI;gBACfgC,eAAe,EAAE,yBAAyB;gBAC1CC,WAAW,EAAE,uBAAuB;gBACpCC,WAAW,EAAE,CAAC;gBACdC,OAAO,EAAE,GAAG;gBACZC,IAAI,EAAE;eACP;aACF;YACDC,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE3C,IAAI,CAACyC,KAAK;kBAChBG,IAAI,EAAE;oBACJC,IAAI,EAAE;;iBAET;gBACDC,MAAM,EAAE;kBACNJ,OAAO,EAAE,IAAI;kBACb3D,QAAQ,EAAE;iBACX;gBACDgE,OAAO,EAAE;kBACPC,IAAI,EAAE,OAAO;kBACbC,SAAS,EAAE;;eAEd;cACDC,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjBX,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;iBAET;gBACDU,CAAC,EAAE;kBACDZ,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;;;;WAKf,CAAC;;MAEN,CAAC;MACD3D,KAAK,EAAGqB,GAAG,IAAI;QACbpB,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEqB,GAAG,CAAC;QAChE,IAAI,CAACrB,KAAK,GAAG,+CAA+C;MAC9D;KACD,CAAC;EACJ;EAEA;;;EAGA4B,uBAAuB;IACrB,IAAI,CAAC1D,YAAY,CAACoG,0BAA0B,CAAC,IAAI,CAAC/B,kBAAkB,IAAIC,SAAS,EAAE,IAAI,CAACxD,YAAY,CAAC,CAACN,SAAS,CAAC;MAC9Gc,IAAI,EAAGwB,IAAI,IAAI;QACb,IAAI,IAAI,CAACmB,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAACnB,IAAI,CAACyB,MAAM,GAAGzB,IAAI,CAACyB,MAAM;UAClD,IAAI,CAACN,mBAAmB,CAACnB,IAAI,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC1B,IAAI,GAAGA,IAAI,CAACuD,OAAO;UAC7D,IAAI,CAACpC,mBAAmB,CAACnB,IAAI,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC1B,IAAI,GAAGA,IAAI,CAACwD,OAAO;UAC7D,IAAI,CAACrC,mBAAmB,CAACQ,MAAM,EAAE;SAClC,MAAM,IAAI,IAAI,CAAC8B,sBAAsB,EAAE;UACtC;UACA,MAAMC,cAAc,GAAG,CAAC,GAAG1D,IAAI,CAACyB,MAAM,CAAC;UAEvC;UACA,MAAMkC,qBAAqB,GAAG,IAAI,CAACC,2BAA2B,CAACF,cAAc,EAAE;YAC7EG,OAAO,EAAE,IAAI;YACbC,SAAS,EAAE,EAAE;YACbC,iBAAiB,EAAE,IAAI,CAAC;WACzB,CAAC;UAEF;UACA,MAAMC,WAAW,GAAG;YAClB1B,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACPC,KAAK,EAAE;gBACLC,OAAO,EAAE,IAAI;gBACbC,IAAI,EAAE3C,IAAI,CAACyC,KAAK;gBAChBG,IAAI,EAAE;kBACJC,IAAI,EAAE;;eAET;cACDC,MAAM,EAAE;gBACNJ,OAAO,EAAE,IAAI;gBACb3D,QAAQ,EAAE;eACX;cACDgE,OAAO,EAAE;gBACPC,IAAI,EAAE,OAAO;gBACbC,SAAS,EAAE;;aAEd;YACDC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,IAAI;gBACjBX,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE;;eAET;cACDU,CAAC,EAAE;gBACDZ,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE;;;;WAIb;UAED;UACA,MAAMsB,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACF,WAAW,EAAEL,qBAAqB,CAAC;UAE9E,IAAI,CAACxC,mBAAmB,GAAG,IAAIxE,KAAK,CAAC,IAAI,CAAC8G,sBAAsB,CAAC5B,aAAa,EAAE;YAC9EC,IAAI,EAAE,KAAK;YACX9B,IAAI,EAAE;cACJyB,MAAM,EAAEzB,IAAI,CAACyB,MAAM;cACnBC,QAAQ,EAAE,CACR;gBACEK,KAAK,EAAE,2BAA2B;gBAClC/B,IAAI,EAAEA,IAAI,CAACuD,OAAO;gBAClBvB,eAAe,EAAE,yBAAyB;gBAC1CC,WAAW,EAAE,uBAAuB;gBACpCC,WAAW,EAAE;eACd,EACD;gBACEH,KAAK,EAAE,4BAA4B;gBACnC/B,IAAI,EAAEA,IAAI,CAACwD,OAAO;gBAClBxB,eAAe,EAAE,yBAAyB;gBAC1CC,WAAW,EAAE,uBAAuB;gBACpCC,WAAW,EAAE;eACd;aAEJ;YACDG,OAAO,EAAE4B;WACV,CAAC;;MAEN,CAAC;MACDjF,KAAK,EAAGqB,GAAG,IAAI;QACbpB,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEqB,GAAG,CAAC;QAChE,IAAI,CAACrB,KAAK,GAAG,+CAA+C;MAC9D;KACD,CAAC;EACJ;EAEA;;;EAGA6B,uBAAuB;IACrB,IAAI,CAAC3D,YAAY,CAACiH,0BAA0B,CAAC,IAAI,CAACnG,YAAY,CAAC,CAACN,SAAS,CAAC;MACxEc,IAAI,EAAGwB,IAAI,IAAI;QACb,IAAI,IAAI,CAACoB,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAACpB,IAAI,CAACyB,MAAM,GAAGzB,IAAI,CAACyB,MAAM;UAClD,IAAI,CAACL,mBAAmB,CAACpB,IAAI,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC1B,IAAI,GAAGA,IAAI,CAACA,IAAI;UAC1D,IAAI,CAACoB,mBAAmB,CAACO,MAAM,EAAE;SAClC,MAAM,IAAI,IAAI,CAACyC,sBAAsB,EAAE;UACtC,IAAI,CAAChD,mBAAmB,GAAG,IAAIzE,KAAK,CAAC,IAAI,CAACyH,sBAAsB,CAACvC,aAAa,EAAE;YAC9EC,IAAI,EAAE,KAAK;YACX9B,IAAI,EAAE;cACJyB,MAAM,EAAEzB,IAAI,CAACyB,MAAM;cACnBC,QAAQ,EAAE,CAAC;gBACTK,KAAK,EAAE,wBAAwB;gBAC/B/B,IAAI,EAAEA,IAAI,CAACA,IAAI;gBACfgC,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,CAC1B;gBACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,CACxB;gBACDC,WAAW,EAAE;eACd;aACF;YACDG,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE3C,IAAI,CAACyC,KAAK;kBAChBG,IAAI,EAAE;oBACJC,IAAI,EAAE;;iBAET;gBACDC,MAAM,EAAE;kBACNJ,OAAO,EAAE;;eAEZ;cACDQ,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjBX,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;iBAET;gBACDU,CAAC,EAAE;kBACDZ,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;;;;WAKf,CAAC;;MAEN,CAAC;MACD3D,KAAK,EAAGqB,GAAG,IAAI;QACbpB,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEqB,GAAG,CAAC;QACtE,IAAI,CAACrB,KAAK,GAAG,qDAAqD;MACpE;KACD,CAAC;EACJ;EAEA;;;EAGA8B,8BAA8B;IAC5B,IAAI,CAAC5D,YAAY,CAACmH,iCAAiC,CAAC,IAAI,CAACrG,YAAY,CAAC,CAACN,SAAS,CAAC;MAC/Ec,IAAI,EAAGwB,IAAI,IAAI;QACb,IAAI,IAAI,CAACsE,0BAA0B,EAAE;UACnC,IAAI,CAACA,0BAA0B,CAACtE,IAAI,CAACyB,MAAM,GAAGzB,IAAI,CAACyB,MAAM;UACzD,IAAI,CAAC6C,0BAA0B,CAACtE,IAAI,CAAC0B,QAAQ,GAAG1B,IAAI,CAAC0B,QAAQ;UAC7D,IAAI,CAAC4C,0BAA0B,CAAC3C,MAAM,EAAE;SACzC,MAAM,IAAI,IAAI,CAAC4C,6BAA6B,EAAE;UAC7C,IAAI,CAACD,0BAA0B,GAAG,IAAI3H,KAAK,CAAC,IAAI,CAAC4H,6BAA6B,CAAC1C,aAAa,EAAE;YAC5FC,IAAI,EAAE,MAAM;YACZ9B,IAAI,EAAE;cACJyB,MAAM,EAAEzB,IAAI,CAACyB,MAAM;cACnBC,QAAQ,EAAE1B,IAAI,CAAC0B;aAChB;YACDW,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE3C,IAAI,CAACyC,KAAK;kBAChBG,IAAI,EAAE;oBACJC,IAAI,EAAE;;iBAET;gBACDC,MAAM,EAAE;kBACNJ,OAAO,EAAE,IAAI;kBACb3D,QAAQ,EAAE;iBACX;gBACDgE,OAAO,EAAE;kBACPC,IAAI,EAAE,OAAO;kBACbC,SAAS,EAAE;;eAEd;cACDC,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjBX,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;iBAET;gBACDU,CAAC,EAAE;kBACDZ,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;;;;WAKf,CAAC;;MAEN,CAAC;MACD3D,KAAK,EAAGqB,GAAG,IAAI;QACbpB,OAAO,CAACD,KAAK,CAAC,6DAA6D,EAAEqB,GAAG,CAAC;QACjF,IAAI,CAACrB,KAAK,GAAG,gEAAgE;MAC/E;KACD,CAAC;EACJ;EAEA;;;EAGA+B,uBAAuB;IACrB,IAAI,CAAC7D,YAAY,CAACsH,0BAA0B,CAAC,IAAI,CAACxG,YAAY,CAAC,CAACN,SAAS,CAAC;MACxEc,IAAI,EAAGwB,IAAI,IAAI;QACb,IAAI,IAAI,CAACqB,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAACrB,IAAI,CAACyB,MAAM,GAAGzB,IAAI,CAACyB,MAAM;UAClD,IAAI,CAACJ,mBAAmB,CAACrB,IAAI,CAAC0B,QAAQ,GAAG1B,IAAI,CAAC0B,QAAQ;UACtD,IAAI,CAACL,mBAAmB,CAACM,MAAM,EAAE;SAClC,MAAM,IAAI,IAAI,CAAC8C,sBAAsB,EAAE;UACtC,IAAI,CAACpD,mBAAmB,GAAG,IAAI1E,KAAK,CAAC,IAAI,CAAC8H,sBAAsB,CAAC5C,aAAa,EAAE;YAC9EC,IAAI,EAAE,MAAM;YACZ9B,IAAI,EAAE;cACJyB,MAAM,EAAEzB,IAAI,CAACyB,MAAM;cACnBC,QAAQ,EAAE1B,IAAI,CAAC0B;aAChB;YACDW,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE3C,IAAI,CAACyC,KAAK;kBAChBG,IAAI,EAAE;oBACJC,IAAI,EAAE;;iBAET;gBACDC,MAAM,EAAE;kBACNJ,OAAO,EAAE,IAAI;kBACb3D,QAAQ,EAAE;iBACX;gBACDgE,OAAO,EAAE;kBACPC,IAAI,EAAE,OAAO;kBACbC,SAAS,EAAE;;eAEd;cACDC,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjBX,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;iBAET;gBACDU,CAAC,EAAE;kBACDZ,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;;;;WAKf,CAAC;;MAEN,CAAC;MACD3D,KAAK,EAAGqB,GAAG,IAAI;QACbpB,OAAO,CAACD,KAAK,CAAC,yDAAyD,EAAEqB,GAAG,CAAC;QAC7E,IAAI,CAACrB,KAAK,GAAG,4DAA4D;MAC3E;KACD,CAAC;EACJ;EAEA;;;EAGA0F,YAAY,CAACC,IAAY;IACvB,IAAI,CAAC3G,YAAY,GAAG2G,IAAI;IAExB;IACA7G,YAAY,CAAC8G,OAAO,CAAC,sBAAsB,EAAED,IAAI,CAACE,QAAQ,EAAE,CAAC;IAE7D,IAAI,CAAC7D,aAAa,EAAE;EACtB;EAEA;;;EAGA8D,gBAAgB,CAACC,UAAyB;IACxC,IAAI,CAACxD,kBAAkB,GAAGwD,UAAU;IACpC,IAAI,CAAC/D,aAAa,EAAE;EACtB;EAIA;;;EAGAgE,gBAAgB,CAACC,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,gBAAgB;QACnB,OAAO,uBAAuB;MAChC,KAAK,uBAAuB;QAC1B,OAAO,sBAAsB;MAC/B,KAAK,0BAA0B;QAC7B,OAAO,mBAAmB;MAC5B,KAAK,oBAAoB;QACvB,OAAO,oBAAoB;MAC7B;QACE,OAAOA,SAAS;IAAC;EAEvB;EAEA;;;EAGAC,cAAc;IACZ,IAAI,CAACC,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;EAChC;EAEA;;;EAGAC,MAAM,CAACC,KAAqC;IAC1C,IAAIA,KAAK,CAACC,aAAa,KAAKD,KAAK,CAACE,YAAY,EAAE;MAC9C;;IAGF;IACA,MAAMC,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC/F,QAAQ,CAACgG,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,CAC/EC,GAAG,CAACC,OAAO,IAAG;MACb;MACA,MAAMC,MAAM,GAAG,IAAI,CAACnH,OAAO,CAACoH,IAAI,CAACC,CAAC,IAAG;QACnC,MAAMC,QAAQ,GAAGD,CAAC,CAACE,EAAE;QACrB,OAAOL,OAAO,CAACM,SAAS,CAACC,QAAQ,CAAC,oBAAoBH,QAAQ,IAAI,CAAC;MACrE,CAAC,CAAC;MACF,OAAOH,MAAM;IACf,CAAC,CAAC,CACD5F,MAAM,CAAC4F,MAAM,IAAIA,MAAM,KAAKtE,SAAS,CAAsB;IAE9D;IACA3E,eAAe,CAAC2I,cAAc,EAAEH,KAAK,CAACC,aAAa,EAAED,KAAK,CAACE,YAAY,CAAC;IAExE;IACA,IAAIxG,QAAQ,GAAG,CAAC;IAEhB;IACAyG,cAAc,CAACa,OAAO,CAACC,aAAa,IAAG;MACrC,MAAMR,MAAM,GAAG,IAAI,CAACnH,OAAO,CAACoH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKI,aAAa,CAACJ,EAAE,CAAC;MAChE,IAAIJ,MAAM,EAAE;QACVA,MAAM,CAAC/G,QAAQ,GAAGA,QAAQ,EAAE;;IAEhC,CAAC,CAAC;IAEF;IACA,IAAI,CAACJ,OAAO,CACTuB,MAAM,CAAC4F,MAAM,IAAI,CAACN,cAAc,CAACe,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACN,EAAE,KAAKJ,MAAM,CAACI,EAAE,CAAC,CAAC,CACjEG,OAAO,CAACP,MAAM,IAAG;MAChBA,MAAM,CAAC/G,QAAQ,GAAGA,QAAQ,EAAE;IAC9B,CAAC,CAAC;IAEJ;IACA,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;IAEpD;IACA,IAAI,CAAC0H,mBAAmB,EAAE;EAC5B;EAEA;;;EAGAC,sBAAsB,CAACT,QAAgB;IACrC,MAAMH,MAAM,GAAG,IAAI,CAACnH,OAAO,CAACoH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKD,QAAQ,CAAC;IACxD,IAAIH,MAAM,EAAE;MACVA,MAAM,CAACa,OAAO,GAAG,CAACb,MAAM,CAACa,OAAO;MAChC,IAAI,CAACF,mBAAmB,EAAE;;EAE9B;EAEA;;;EAGAG,eAAe,CAACX,QAAgB;IAC9B,MAAMH,MAAM,GAAG,IAAI,CAACnH,OAAO,CAACoH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKD,QAAQ,CAAC;IACxD,OAAOH,MAAM,GAAGA,MAAM,CAACa,OAAO,GAAG,IAAI;EACvC;EAEA;;;EAGAF,mBAAmB;IACjB,IAAI,CAAC,IAAI,CAAC/H,eAAe,EAAE;MACzB;;IAGF,IAAI,CAACA,eAAe,CAACC,OAAO,GAAG,CAAC,GAAG,IAAI,CAACA,OAAO,CAAC;IAChD,IAAI,CAACD,eAAe,CAACmI,YAAY,GAAG,IAAIC,IAAI,EAAE;IAE9C,IAAI,CAACzJ,sBAAsB,CAACoJ,mBAAmB,CAAC,IAAI,CAAC/H,eAAe,CAAC,CAAChB,SAAS,CAAC;MAC9Ec,IAAI,EAAGC,MAAM,IAAI;QACfQ,OAAO,CAAC8H,GAAG,CAAC,6CAA6C,CAAC;MAC5D,CAAC;MACD/H,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACnE;KACD,CAAC;EACJ;EAEA;;;EAGAE,oBAAoB;IAClB,IAAI8H,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAAC3J,sBAAsB,CAAC6B,oBAAoB,EAAE,CAACxB,SAAS,CAAC;QAC3Dc,IAAI,EAAGC,MAAM,IAAI;UACf,IAAI,CAACC,eAAe,GAAGD,MAAM;UAC7B,IAAI,CAACE,OAAO,GAAG,CAAC,GAAGF,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;UAC1E,IAAI,CAACoG,QAAQ,GAAG,KAAK;UACrB,IAAI,CAACnE,aAAa,EAAE;QACtB,CAAC;QACDhC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACrE;OACD,CAAC;;EAEN;EAEA;;;;;EAKAiI,mBAAmB,CAACC,KAAuC,EAAEzE,KAAa;IACxE;IACA,IAAIyE,KAAK,YAAYC,iBAAiB,EAAE;MACtC;MACA,MAAMC,aAAa,GAAGzK,KAAK,CAAC0K,QAAQ,CAACH,KAAK,CAAC;MAC3C,IAAI,CAAC5J,iBAAiB,CAACgK,cAAc,CAACF,aAAa,IAAI,IAAI,EAAE3E,KAAK,CAAC;KACpE,MAAM;MACL,IAAI,CAACnF,iBAAiB,CAACgK,cAAc,CAACJ,KAAK,EAAEzE,KAAK,CAAC;;EAEvD;EAEA;;;EAGQnD,YAAY;IAClB;IACA,IAAI,CAACO,eAAe,EAAE;IAEtB;IACA,MAAM0H,WAAW,GAA2B;MAC1C,iBAAiB,EAAE,0FAA0F,GAC3G,gIAAgI,GAChI,qEAAqE;MACvE,WAAW,EAAE,gEAAgE,GAC3E,6DAA6D,GAC7D,+DAA+D;MACjE,iBAAiB,EAAE,gEAAgE,GACjF,qFAAqF,GACrF,0FAA0F;MAC5F,wBAAwB,EAAE,qFAAqF,GAC7G,4GAA4G,GAC5G,+HAA+H,GAC/H,qEAAqE;MACvE,iBAAiB,EAAE,uDAAuD,GACxE,mGAAmG,GACnG,+DAA+D;MACjE,aAAa,EAAE,yFAAyF,GACtG,+EAA+E,GAC/E,oDAAoD;MACtD;MACA,cAAc,EAAE,+GAA+G,GAC7H,uFAAuF;MACzF,uBAAuB,EAAE,qFAAqF,GAC5G,gGAAgG;MAClG,kBAAkB,EAAE,gGAAgG,GAClH,mHAAmH;MACrH,mBAAmB,EAAE,6EAA6E,GAChG,+FAA+F,GAC/F,wCAAwC;MAC1C,uBAAuB,EAAE,8DAA8D,GACrF,qFAAqF,GACrF;KACH;IAED;IACA5H,QAAQ,CAACgG,gBAAgB,CAAC,4BAA4B,CAAC,CAACU,OAAO,CAACmB,EAAE,IAAG;MACnE,MAAMC,OAAO,GAAGC,SAAS,CAACC,OAAO,CAACC,WAAW,CAACJ,EAAE,CAAC;MACjD,IAAIC,OAAO,EAAE;QACXA,OAAO,CAACI,OAAO,EAAE;;IAErB,CAAC,CAAC;IAEF;IACAlI,QAAQ,CAACgG,gBAAgB,CAAC,4BAA4B,CAAC,CAACU,OAAO,CAACmB,EAAE,IAAG;MACnE,MAAMM,QAAQ,GAAGN,EAAE,CAACO,YAAY,CAAC,gBAAgB,CAAC;MAClD;MAEA,IAAID,QAAQ,IAAIA,QAAQ,IAAIP,WAAW,EAAE;QACvC,IAAI;UACF,MAAME,OAAO,GAAG,IAAIC,SAAS,CAACC,OAAO,CAACH,EAAE,EAAE;YACxCQ,OAAO,EAAET,WAAW,CAACO,QAAoC,CAAC;YAC1DG,IAAI,EAAE,IAAI;YACVC,OAAO,EAAE,aAAa;YACtBC,SAAS,EAAE,KAAK;YAChBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE;WACX,CAAC;UAEF;UACA,IAAIb,EAAE,CAACc,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,IAAIf,EAAE,CAACgB,OAAO,CAAC,iBAAiB,CAAC,EAAE;YAC5EhB,EAAE,CAACiB,gBAAgB,CAAC,OAAO,EAAE,MAAK;cAChChB,OAAO,CAACiB,IAAI,EAAE;YAChB,CAAC,CAAC;;SAEL,CAAC,OAAO1J,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;OAEtD,MAAM,IAAI8I,QAAQ,EAAE;QACnB7I,OAAO,CAAC0J,IAAI,CAAC,kCAAkC,EAAEb,QAAQ,CAAC;;IAE9D,CAAC,CAAC;IAEF;IACA,IAAI,CAACpI,qBAAqB,GAAI2F,KAAK,IAAI;MACrC,MAAMuD,MAAM,GAAGvD,KAAK,CAACuD,MAAqB;MAC1C;MACA,IAAI,CAACA,MAAM,CAACJ,OAAO,CAAC,4BAA4B,CAAC,IAAI,CAACI,MAAM,CAACJ,OAAO,CAAC,UAAU,CAAC,EAAE;QAChF,IAAI,CAACK,eAAe,EAAE;;IAE1B,CAAC;IACDlJ,QAAQ,CAAC8I,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC/I,qBAAqB,CAAC;EAChE;EAEA;;;EAGQmJ,eAAe;IACrBlJ,QAAQ,CAACgG,gBAAgB,CAAC,4BAA4B,CAAC,CAACU,OAAO,CAACmB,EAAE,IAAG;MACnE,MAAMsB,eAAe,GAAGpB,SAAS,CAACC,OAAO,CAACC,WAAW,CAACJ,EAAE,CAAC;MACzD,IAAIsB,eAAe,EAAE;QACnBA,eAAe,CAACJ,IAAI,EAAE;;IAE1B,CAAC,CAAC;EACJ;EAEA;;;EAGQ7I,eAAe;IACrBF,QAAQ,CAACgG,gBAAgB,CAAC,4BAA4B,CAAC,CAACU,OAAO,CAACmB,EAAE,IAAG;MACnE,MAAMsB,eAAe,GAAGpB,SAAS,CAACC,OAAO,CAACC,WAAW,CAACJ,EAAE,CAAC;MACzD,IAAIsB,eAAe,EAAE;QACnBA,eAAe,CAACjB,OAAO,EAAE;;IAE7B,CAAC,CAAC;EACJ;EAEA;;;EAGAkB,gBAAgB;IACd,IAAI,CAACF,eAAe,EAAE;IACtB,IAAI,CAACrL,MAAM,CAACwL,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;;;EAGAC,wBAAwB;IACtB,IAAI,CAACJ,eAAe,EAAE;IACtB,IAAI,CAACrL,MAAM,CAACwL,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEA;;;EAGAE,sBAAsB;IACpB,IAAI,CAACL,eAAe,EAAE;IACtB,IAAI,CAACrL,MAAM,CAACwL,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEA;;;EAGAG,oBAAoB;IAClB,IAAI,CAACN,eAAe,EAAE;IACtB,IAAI,CAACrL,MAAM,CAACwL,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEA;;;EAGAI,qBAAqB;IACnB,IAAI,CAACP,eAAe,EAAE;IACtB,IAAI,CAACrL,MAAM,CAACwL,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAEA;;;;;;EAMQK,kBAAkB,CAAC1G,IAAY,EAAEmB,YAAoB,EAAE;IAC7D,IAAI,CAACnB,IAAI,IAAIA,IAAI,CAAC2G,MAAM,IAAIxF,SAAS,EAAE;MACrC,OAAOnB,IAAI;;IAGb;IACA,MAAM4G,SAAS,GAAG5G,IAAI,CAAC6G,SAAS,CAAC,CAAC,EAAE1F,SAAS,GAAG,CAAC,CAAC;IAClD,MAAM2F,YAAY,GAAGF,SAAS,CAACG,WAAW,CAAC,GAAG,CAAC;IAE/C;IACA,IAAID,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAG3F,SAAS,GAAG,GAAG,EAAE;MACtD,OAAOnB,IAAI,CAAC6G,SAAS,CAAC,CAAC,EAAEC,YAAY,CAAC,GAAG,KAAK;;IAGhD;IACA,OAAOF,SAAS,GAAG,KAAK;EAC1B;EAEA;;;;;;EAMQ3F,2BAA2B,CAACF,cAAwB,EAAErB,UAI1D,EAAE;IACJ,MAAM5D,MAAM,GAAG;MACboF,OAAO,EAAExB,OAAO,CAACwB,OAAO,IAAI,IAAI;MAChCC,SAAS,EAAEzB,OAAO,CAACyB,SAAS,IAAI,EAAE;MAClCC,iBAAiB,EAAE1B,OAAO,CAAC0B,iBAAiB,IAAI;KACjD;IAED,IAAI,CAACtF,MAAM,CAACoF,OAAO,EAAE;MACnB,OAAO,EAAE;;IAGX,MAAM8F,MAAM,GAAQ;MAClBzG,MAAM,EAAE;QACNG,CAAC,EAAE;UACDuG,KAAK,EAAE;YACLC,QAAQ,EAAE,CAACC,KAAU,EAAEC,KAAa,KAAI;cACtC,MAAMhI,KAAK,GAAG2B,cAAc,CAACqG,KAAK,CAAC;cACnC,OAAO,IAAI,CAACV,kBAAkB,CAACtH,KAAK,EAAEtD,MAAM,CAACqF,SAAS,CAAC;YACzD;;;;KAIP;IAED,IAAIrF,MAAM,CAACsF,iBAAiB,EAAE;MAC5B4F,MAAM,CAACnH,OAAO,GAAG;QACfO,OAAO,EAAE;UACPiH,SAAS,EAAE;YACTvH,KAAK,EAAGwH,OAAY,IAAI;cACtB,MAAMC,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS;cACtC,OAAOxG,cAAc,CAACwG,SAAS,CAAC,IAAID,OAAO,CAAC,CAAC,CAAC,CAAClI,KAAK;YACtD;;;OAGL;;IAGH,OAAO4H,MAAM;EACf;EAEA;;;;;;EAMQzF,gBAAgB,CAACiG,UAAe,EAAEC,eAAoB;IAC5D,IAAI,CAACA,eAAe,IAAIC,MAAM,CAACC,IAAI,CAACF,eAAe,CAAC,CAACd,MAAM,KAAK,CAAC,EAAE;MACjE,OAAOa,UAAU;;IAGnB,MAAMI,MAAM,GAAG;MAAE,GAAGJ;IAAU,CAAE;IAEhC;IACA,IAAIC,eAAe,CAAClH,MAAM,EAAE;MAC1BqH,MAAM,CAACrH,MAAM,GAAGqH,MAAM,CAACrH,MAAM,IAAI,EAAE;MACnC,IAAIkH,eAAe,CAAClH,MAAM,CAACG,CAAC,EAAE;QAC5BkH,MAAM,CAACrH,MAAM,CAACG,CAAC,GAAGkH,MAAM,CAACrH,MAAM,CAACG,CAAC,IAAI,EAAE;QACvCkH,MAAM,CAACrH,MAAM,CAACG,CAAC,CAACuG,KAAK,GAAG;UACtB,GAAGW,MAAM,CAACrH,MAAM,CAACG,CAAC,CAACuG,KAAK;UACxB,GAAGQ,eAAe,CAAClH,MAAM,CAACG,CAAC,CAACuG;SAC7B;;;IAIL;IACA,IAAIQ,eAAe,CAAC5H,OAAO,EAAE;MAC3B+H,MAAM,CAAC/H,OAAO,GAAG+H,MAAM,CAAC/H,OAAO,IAAI,EAAE;MACrC,IAAI4H,eAAe,CAAC5H,OAAO,CAACO,OAAO,EAAE;QACnCwH,MAAM,CAAC/H,OAAO,CAACO,OAAO,GAAGwH,MAAM,CAAC/H,OAAO,CAACO,OAAO,IAAI,EAAE;QACrDwH,MAAM,CAAC/H,OAAO,CAACO,OAAO,CAACiH,SAAS,GAAGO,MAAM,CAAC/H,OAAO,CAACO,OAAO,CAACiH,SAAS,IAAI,EAAE;QAEzE;QACAK,MAAM,CAACC,IAAI,CAACF,eAAe,CAAC5H,OAAO,CAACO,OAAO,CAACiH,SAAS,CAAC,CAAC3D,OAAO,CAACmE,GAAG,IAAG;UACnED,MAAM,CAAC/H,OAAO,CAACO,OAAO,CAACiH,SAAS,CAACQ,GAAG,CAAC,GAAGJ,eAAe,CAAC5H,OAAO,CAACO,OAAO,CAACiH,SAAS,CAACQ,GAAG,CAAC;QACxF,CAAC,CAAC;;;IAIN,OAAOD,MAAM;EACf;;;uBAvhCWvN,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAyN;MAAAC;QAAA;;;;;;;;;;;;;;;;;;;;;UCxB/B5N,8BAAuB;UAEfA,yBAAS;UAAAA,iBAAK;UAClBA,8BAAoC;UAE4FA;YAAA,OAAS6N,iBAAa,CAAC,CAAC;UAAA,EAAC;UAAC7N,qBAAK;UAAAA,iBAAS;UACpKA,iCAAsJ;UAA1BA;YAAA,OAAS6N,iBAAa,CAAC,CAAC;UAAA,EAAC;UAAC7N,0BAAK;UAAAA,iBAAS;UACpKA,kCAAyJ;UAA3BA;YAAA,OAAS6N,iBAAa,EAAE,CAAC;UAAA,EAAC;UAAC7N,4BAAM;UAAAA,iBAAS;UACxKA,kCAAyJ;UAA3BA;YAAA,OAAS6N,iBAAa,EAAE,CAAC;UAAA,EAAC;UAAC7N,4BAAM;UAAAA,iBAAS;UAE1KA,+BAAoC;UACMA;YAAA,OAAS6N,mBAAe;UAAA,EAAC;UAC/D7N,wBAA0C;UAACA,gCAAiC;UAAAA,6BAAY;UAAAA,iBAAO;UAEjGA,kCAAsH;UAA3BA;YAAA,OAAS6N,oBAAgB;UAAA,EAAC;UACnH7N,wBAAuF;UACvFA,gCAAiC;UAAAA,aAAwD;UAAAA,iBAAO;UAElGA,4EAES;UACXA,iBAAM;UAIVA,sEAGM;UAENA,sEAIM;UAENA,sEAEM;UAKNA,gCAAqG;UAAxEA;YAAA,OAAsB6N,kBAAc;UAAA,EAAC;UAEhE7N,uEAiBM;UAGNA,uEAiBM;UAGNA,uEAiBM;UAGNA,uEAiBM;UAGNA,uEAiBM;UACRA,iBAAM;UAGNA,wEAgFM;UACRA,iBAAM;;;UA9NoCA,eAAwC;UAAxCA,qDAAwC;UACxCA,eAAwC;UAAxCA,qDAAwC;UACxCA,eAAyC;UAAzCA,sDAAyC;UACzCA,eAAyC;UAAzCA,sDAAyC;UAMvDA,eAAuC;UAAvCA,oDAAuC;UAC3CA,eAAoE;UAApEA,kFAAoE;UACjDA,eAAwD;UAAxDA,sFAAwD;UAElFA,eAAc;UAAdA,mCAAc;UAOvBA,eAAc;UAAdA,mCAAc;UAKdA,eAAa;UAAbA,kCAAa;UAMbA,eAAW;UAAXA,gCAAW;UAOkDA,eAAiC;UAAjCA,mDAAiC;UAEtEA,eAAkC;UAAlCA,uDAAkC;UAoBlCA,eAAwC;UAAxCA,6DAAwC;UAoBxCA,eAAwC;UAAxCA,6DAAwC;UAoBxCA,eAA+C;UAA/CA,oEAA+C;UAoB/CA,eAAmD;UAAnDA,4EAAmD;UAqB/DA,eAA+C;UAA/CA,wEAA+C", "names": ["Chart", "registerables", "moveItemInArray", "i0", "register", "DashboardComponent", "constructor", "chartService", "authService", "alertService", "dashboardConfigService", "chartModalService", "monitoringService", "router", "currentUser", "subscribe", "user", "isAdmin", "savedDateRange", "localStorage", "getItem", "selectedDays", "parseInt", "ngOnInit", "loadDashboardConfig", "loadAlerts", "loadSystemStatistics", "loadDashboardSummary", "getUserDashboardConfig", "next", "config", "dashboardConfig", "widgets", "sort", "a", "b", "position", "error", "console", "resetDashboardConfig", "ngAfterViewInit", "setTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initPopovers", "ngOnDestroy", "updateSubscription", "unsubscribe", "documentClickListener", "document", "removeEventListener", "destroyPopovers", "<PERSON><PERSON><PERSON><PERSON>", "getDashboardAlerts", "data", "alerts", "filter", "alert", "isResolved", "err", "getSystemStatistics", "systemStatistics", "getDashboardSummary", "dashboardSummary", "loading", "loadApiCallsChart", "loadApiPerformanceChart", "loadInstancesUsageChart", "loadProductionPerformanceChart", "loadSecurityEventsChart", "refresh<PERSON><PERSON><PERSON>", "apiCallsChart", "destroy", "apiPerformanceChart", "instancesUsageChart", "securityEventsChart", "getApiCallsChartData", "selectedInstanceId", "undefined", "labels", "datasets", "update", "apiCallsChartRef", "nativeElement", "type", "label", "backgroundColor", "borderColor", "borderWidth", "tension", "fill", "options", "responsive", "maintainAspectRatio", "plugins", "title", "display", "text", "font", "size", "legend", "tooltip", "mode", "intersect", "scales", "y", "beginAtZero", "x", "getApiPerformanceChartData", "avgData", "maxData", "apiPerformanceChartRef", "originalLabels", "truncatedLabelsConfig", "createTruncatedLabelsConfig", "enabled", "max<PERSON><PERSON><PERSON>", "showFullInTooltip", "baseOptions", "finalOptions", "mergeChartConfig", "getInstancesUsageChartData", "instancesUsageChartRef", "getProductionPerformanceChartData", "productionPerformanceChart", "productionPerformanceChartRef", "getSecurityEventsChartData", "securityEventsChartRef", "onDaysChange", "days", "setItem", "toString", "onInstanceChange", "instanceId", "getAlertTypeText", "alertType", "toggleEditMode", "editMode", "onDrop", "event", "previousIndex", "currentIndex", "visibleWidgets", "Array", "from", "querySelectorAll", "map", "element", "widget", "find", "w", "widgetId", "id", "innerHTML", "includes", "for<PERSON>ach", "visibleWidget", "some", "vw", "saveDashboardConfig", "toggleWidgetVisibility", "visible", "isWidgetVisible", "lastModified", "Date", "log", "confirm", "openFullscreenChart", "chart", "HTMLCanvasElement", "chartInstance", "<PERSON><PERSON><PERSON>", "openChartModal", "helpContent", "el", "popover", "bootstrap", "Popover", "getInstance", "dispose", "helpType", "getAttribute", "content", "html", "trigger", "placement", "container", "sanitize", "classList", "contains", "closest", "addEventListener", "hide", "warn", "target", "hideAllPopovers", "popoverInstance", "navigateTo<PERSON><PERSON><PERSON>", "navigate", "navigateToSecurityEvents", "navigateToCertificates", "navigateToMonitoring", "navigateToPerformance", "truncateMethodName", "length", "truncated", "substring", "lastDotIndex", "lastIndexOf", "result", "ticks", "callback", "value", "index", "callbacks", "context", "dataIndex", "baseConfig", "truncatedConfig", "Object", "keys", "merged", "key", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, ElementRef, ViewChild, AfterViewInit } from '@angular/core';\nimport { ChartService } from '../services/chart.service';\nimport { AuthService } from '../services/auth.service';\nimport { AlertService } from '../services/alert.service';\nimport { DashboardConfigService } from '../services/dashboard-config.service';\nimport { ChartModalService } from '../services/chart-modal.service';\nimport { MonitoringService } from '../services/monitoring.service';\nimport { Chart, registerables } from 'chart.js';\nimport { Subscription, interval } from 'rxjs';\nimport { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';\nimport { DashboardConfig, DashboardWidget } from '../models/dashboard-config.model';\nimport { Router } from '@angular/router';\n\n// Registrace všech komponent Chart.js\nChart.register(...registerables);\n\n// Import Bootstrap pro popover\ndeclare var bootstrap: any;\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('apiCallsChart') apiCallsChartRef!: ElementRef;\n  @ViewChild('apiPerformanceChart') apiPerformanceChartRef!: ElementRef;\n  @ViewChild('instancesUsageChart') instancesUsageChartRef!: ElementRef;\n  @ViewChild('productionPerformanceChart') productionPerformanceChartRef!: ElementRef;\n  @ViewChild('securityEventsChart') securityEventsChartRef!: ElementRef;\n\n  loading = true;\n  error: string | null = null;\n  currentUser: any;\n  isAdmin = false;\n  alerts: any[] = [];\n  systemStatistics: any = {};\n  dashboardSummary: any = {};\n\n  // Grafy\n  apiCallsChart: Chart | null = null;\n  apiPerformanceChart: Chart | null = null;\n  instancesUsageChart: Chart | null = null;\n  productionPerformanceChart: Chart | null = null;\n  securityEventsChart: Chart | null = null;\n\n  // Filtry\n  selectedDays = 30;\n  selectedInstanceId: number | null = null;\n\n  // Dashboard konfigurace\n  dashboardConfig: DashboardConfig | null = null;\n  widgets: DashboardWidget[] = [];\n  editMode = false;\n\n  // Aktualizace dat\n  private updateSubscription: Subscription | null = null;\n\n  // Event listener pro zavírání popoverů\n  private documentClickListener: ((event: Event) => void) | null = null;\n\n  constructor(\n    private chartService: ChartService,\n    private authService: AuthService,\n    private alertService: AlertService,\n    private dashboardConfigService: DashboardConfigService,\n    private chartModalService: ChartModalService,\n    private monitoringService: MonitoringService,\n    private router: Router\n  ) {\n    this.authService.currentUser.subscribe(user => {\n      this.currentUser = user;\n      this.isAdmin = user?.isAdmin || false;\n    });\n\n    // Načtení uložené hodnoty počtu dní z local storage\n    const savedDateRange = localStorage.getItem('monitoring_dateRange');\n    if (savedDateRange) {\n      this.selectedDays = parseInt(savedDateRange, 10);\n    }\n  }\n\n  ngOnInit(): void {\n    // Načtení konfigurace dashboardu\n    this.loadDashboardConfig();\n\n    this.loadAlerts();\n    this.loadSystemStatistics();\n    this.loadDashboardSummary();\n  }\n\n  /**\n   * Načte konfiguraci dashboardu pro aktuálního uživatele\n   */\n  loadDashboardConfig(): void {\n    this.dashboardConfigService.getUserDashboardConfig().subscribe({\n      next: (config) => {\n        this.dashboardConfig = config;\n        this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n      },\n      error: (error) => {\n        console.error('Chyba při načítání konfigurace dashboardu', error);\n        // Použijeme výchozí konfiguraci\n        this.dashboardConfigService.resetDashboardConfig().subscribe(config => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n        });\n      }\n    });\n  }\n\n  ngAfterViewInit(): void {\n    // Inicializace grafů po načtení view\n    setTimeout(() => {\n      this.initCharts();\n      // Inicializace popoverů s delším zpožděním, aby se zajistilo, že DOM je plně načten\n      setTimeout(() => {\n        this.initPopovers();\n      }, 300);\n    }, 500);\n  }\n\n  ngOnDestroy(): void {\n    // Zrušení subscription při zničení komponenty\n    if (this.updateSubscription) {\n      this.updateSubscription.unsubscribe();\n    }\n\n    // Odstranění globálního event listeneru\n    if (this.documentClickListener) {\n      document.removeEventListener('click', this.documentClickListener);\n      this.documentClickListener = null;\n    }\n\n    // Zničení všech popoverů\n    this.destroyPopovers();\n\n    // Zničení grafů\n    this.destroyCharts();\n  }\n\n  /**\n   * Načtení upozornění\n   */\n  loadAlerts(): void {\n    this.alertService.getDashboardAlerts().subscribe({\n      next: (data) => {\n        this.alerts = data.filter((alert: any) => !alert.isResolved);\n      },\n      error: (err) => {\n        console.error('Chyba při načítání upozornění', err);\n        this.error = 'Nepodařilo se načíst upozornění';\n      }\n    });\n  }\n\n  /**\n   * Načtení systémových statistik\n   */\n  loadSystemStatistics(): void {\n    this.monitoringService.getSystemStatistics().subscribe({\n      next: (data) => {\n        this.systemStatistics = data;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání systémových statistik', err);\n        this.error = 'Nepodařilo se načíst systémové statistiky';\n      }\n    });\n  }\n\n  /**\n   * Načtení dashboard summary dat\n   */\n  loadDashboardSummary(): void {\n    this.monitoringService.getDashboardSummary().subscribe({\n      next: (data) => {\n        this.dashboardSummary = data;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dashboard summary', err);\n        this.error = 'Nepodařilo se načíst dashboard summary';\n      }\n    });\n  }\n\n  /**\n   * Inicializace všech grafů\n   */\n  initCharts(): void {\n    this.loading = true;\n\n    // Načtení dat pro grafy\n    this.loadApiCallsChart();\n    this.loadApiPerformanceChart();\n    this.loadInstancesUsageChart();\n    this.loadProductionPerformanceChart();\n\n    if (this.isAdmin) {\n      this.loadSecurityEventsChart();\n    }\n\n    this.loading = false;\n  }\n\n  /**\n   * Aktualizace všech grafů\n   */\n  refreshCharts(): void {\n    this.loadApiCallsChart();\n    this.loadApiPerformanceChart();\n    this.loadInstancesUsageChart();\n    this.loadProductionPerformanceChart();\n\n    if (this.isAdmin) {\n      this.loadSecurityEventsChart();\n    }\n\n    this.loadAlerts();\n    this.loadSystemStatistics();\n    this.loadDashboardSummary();\n\n    // Reinicializace popoverů po aktualizaci dat\n    setTimeout(() => {\n      this.initPopovers();\n    }, 100);\n  }\n\n  /**\n   * Zničení všech grafů\n   */\n  destroyCharts(): void {\n    if (this.apiCallsChart) {\n      this.apiCallsChart.destroy();\n      this.apiCallsChart = null;\n    }\n\n    if (this.apiPerformanceChart) {\n      this.apiPerformanceChart.destroy();\n      this.apiPerformanceChart = null;\n    }\n\n    if (this.instancesUsageChart) {\n      this.instancesUsageChart.destroy();\n      this.instancesUsageChart = null;\n    }\n\n    if (this.securityEventsChart) {\n      this.securityEventsChart.destroy();\n      this.securityEventsChart = null;\n    }\n  }\n\n  /**\n   * Načtení grafu API volání\n   */\n  loadApiCallsChart(): void {\n    this.chartService.getApiCallsChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n      next: (data) => {\n        if (this.apiCallsChart) {\n          this.apiCallsChart.data.labels = data.labels;\n          this.apiCallsChart.data.datasets[0].data = data.data;\n          this.apiCallsChart.update();\n        } else if (this.apiCallsChartRef) {\n          this.apiCallsChart = new Chart(this.apiCallsChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Počet DISApi volání',\n                data: data.data,\n                backgroundColor: 'rgba(54, 162, 235, 0.2)',\n                borderColor: 'rgba(54, 162, 235, 1)',\n                borderWidth: 2,\n                tension: 0.3,\n                fill: true\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf API volání', err);\n        this.error = 'Nepodařilo se načíst data pro graf API volání';\n      }\n    });\n  }\n\n  /**\n   * Načtení grafu výkonu API\n   */\n  loadApiPerformanceChart(): void {\n    this.chartService.getApiPerformanceChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n      next: (data) => {\n        if (this.apiPerformanceChart) {\n          this.apiPerformanceChart.data.labels = data.labels;\n          this.apiPerformanceChart.data.datasets[0].data = data.avgData;\n          this.apiPerformanceChart.data.datasets[1].data = data.maxData;\n          this.apiPerformanceChart.update();\n        } else if (this.apiPerformanceChartRef) {\n          // Uložení originálních labels pro tooltip\n          const originalLabels = [...data.labels];\n\n          // Konfigurace pro zkrácené popisky na ose X\n          const truncatedLabelsConfig = this.createTruncatedLabelsConfig(originalLabels, {\n            enabled: true,        // Zapnout zkrácení popisků\n            maxLength: 30,        // Maximální délka popisku\n            showFullInTooltip: true // Zobrazit plný název v tooltip\n          });\n\n          // Základní konfigurace grafu\n          const baseOptions = {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              title: {\n                display: true,\n                text: data.title,\n                font: {\n                  size: 16\n                }\n              },\n              legend: {\n                display: true,\n                position: 'top'\n              },\n              tooltip: {\n                mode: 'index',\n                intersect: false\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: true,\n                title: {\n                  display: true,\n                  text: 'Doba odezvy (ms)'\n                }\n              },\n              x: {\n                title: {\n                  display: true,\n                  text: 'Endpoint'\n                }\n              }\n            }\n          };\n\n          // Sloučení s konfigurací pro zkrácené popisky\n          const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n\n          this.apiPerformanceChart = new Chart(this.apiPerformanceChartRef.nativeElement, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: [\n                {\n                  label: 'Průměrná doba odezvy (ms)',\n                  data: data.avgData,\n                  backgroundColor: 'rgba(54, 162, 235, 0.5)',\n                  borderColor: 'rgba(54, 162, 235, 1)',\n                  borderWidth: 1\n                },\n                {\n                  label: 'Maximální doba odezvy (ms)',\n                  data: data.maxData,\n                  backgroundColor: 'rgba(255, 99, 132, 0.5)',\n                  borderColor: 'rgba(255, 99, 132, 1)',\n                  borderWidth: 1\n                }\n              ]\n            },\n            options: finalOptions\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf výkonu API', err);\n        this.error = 'Nepodařilo se načíst data pro graf výkonu API';\n      }\n    });\n  }\n\n  /**\n   * Načtení grafu využití instancí\n   */\n  loadInstancesUsageChart(): void {\n    this.chartService.getInstancesUsageChartData(this.selectedDays).subscribe({\n      next: (data) => {\n        if (this.instancesUsageChart) {\n          this.instancesUsageChart.data.labels = data.labels;\n          this.instancesUsageChart.data.datasets[0].data = data.data;\n          this.instancesUsageChart.update();\n        } else if (this.instancesUsageChartRef) {\n          this.instancesUsageChart = new Chart(this.instancesUsageChartRef.nativeElement, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Počet volání DIS metod',\n                data: data.data,\n                backgroundColor: [\n                  'rgba(255, 99, 132, 0.5)',\n                  'rgba(54, 162, 235, 0.5)',\n                  'rgba(255, 206, 86, 0.5)',\n                  'rgba(75, 192, 192, 0.5)',\n                  'rgba(153, 102, 255, 0.5)',\n                  'rgba(255, 159, 64, 0.5)',\n                  'rgba(199, 199, 199, 0.5)',\n                  'rgba(83, 102, 255, 0.5)',\n                  'rgba(255, 99, 255, 0.5)',\n                  'rgba(99, 255, 132, 0.5)'\n                ],\n                borderColor: [\n                  'rgba(255, 99, 132, 1)',\n                  'rgba(54, 162, 235, 1)',\n                  'rgba(255, 206, 86, 1)',\n                  'rgba(75, 192, 192, 1)',\n                  'rgba(153, 102, 255, 1)',\n                  'rgba(255, 159, 64, 1)',\n                  'rgba(199, 199, 199, 1)',\n                  'rgba(83, 102, 255, 1)',\n                  'rgba(255, 99, 255, 1)',\n                  'rgba(99, 255, 132, 1)'\n                ],\n                borderWidth: 1\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Instance'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf využití instancí', err);\n        this.error = 'Nepodařilo se načíst data pro graf využití instancí';\n      }\n    });\n  }\n\n  /**\n   * Načtení grafu výkonu produkčních instancí\n   */\n  loadProductionPerformanceChart(): void {\n    this.chartService.getProductionPerformanceChartData(this.selectedDays).subscribe({\n      next: (data) => {\n        if (this.productionPerformanceChart) {\n          this.productionPerformanceChart.data.labels = data.labels;\n          this.productionPerformanceChart.data.datasets = data.datasets;\n          this.productionPerformanceChart.update();\n        } else if (this.productionPerformanceChartRef) {\n          this.productionPerformanceChart = new Chart(this.productionPerformanceChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: data.datasets\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Doba odezvy (ms)'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf výkonu produkčních instancí', err);\n        this.error = 'Nepodařilo se načíst data pro graf výkonu produkčních instancí';\n      }\n    });\n  }\n\n  /**\n   * Načtení grafu bezpečnostních událostí\n   */\n  loadSecurityEventsChart(): void {\n    this.chartService.getSecurityEventsChartData(this.selectedDays).subscribe({\n      next: (data) => {\n        if (this.securityEventsChart) {\n          this.securityEventsChart.data.labels = data.labels;\n          this.securityEventsChart.data.datasets = data.datasets;\n          this.securityEventsChart.update();\n        } else if (this.securityEventsChartRef) {\n          this.securityEventsChart = new Chart(this.securityEventsChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: data.datasets\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet událostí'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf bezpečnostních událostí', err);\n        this.error = 'Nepodařilo se načíst data pro graf bezpečnostních událostí';\n      }\n    });\n  }\n\n  /**\n   * Změna filtru dnů\n   */\n  onDaysChange(days: number): void {\n    this.selectedDays = days;\n\n    // Uložení vybraného počtu dní do local storage\n    localStorage.setItem('monitoring_dateRange', days.toString());\n\n    this.refreshCharts();\n  }\n\n  /**\n   * Změna filtru instance\n   */\n  onInstanceChange(instanceId: number | null): void {\n    this.selectedInstanceId = instanceId;\n    this.refreshCharts();\n  }\n\n\n\n  /**\n   * Získání textu pro typ alertu\n   */\n  getAlertTypeText(alertType: string): string {\n    switch (alertType) {\n      case 'SecurityBreach':\n        return 'Bezpečnostní incident';\n      case 'CertificateExpiration':\n        return 'Expirace certifikátu';\n      case 'FailedConnectionAttempts':\n        return 'Selhané připojení';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      default:\n        return alertType;\n    }\n  }\n\n  /**\n   * Přepnutí režimu úprav dashboardu\n   */\n  toggleEditMode(): void {\n    this.editMode = !this.editMode;\n  }\n\n  /**\n   * Zpracování přetažení widgetu\n   */\n  onDrop(event: CdkDragDrop<DashboardWidget[]>): void {\n    if (event.previousIndex === event.currentIndex) {\n      return;\n    }\n\n    // Získáme všechny viditelné widgety v pořadí, jak jsou zobrazeny v DOM\n    const visibleWidgets = Array.from(document.querySelectorAll('.col-lg-6[cdkDrag]'))\n      .map(element => {\n        // Získáme ID widgetu z atributu *ngIf\n        const widget = this.widgets.find(w => {\n          const widgetId = w.id;\n          return element.innerHTML.includes(`isWidgetVisible('${widgetId}')`);\n        });\n        return widget;\n      })\n      .filter(widget => widget !== undefined) as DashboardWidget[];\n\n    // Provedeme přesun v poli viditelných widgetů\n    moveItemInArray(visibleWidgets, event.previousIndex, event.currentIndex);\n\n    // Aktualizujeme pozice všech widgetů\n    let position = 0;\n\n    // Nejprve aktualizujeme pozice viditelných widgetů\n    visibleWidgets.forEach(visibleWidget => {\n      const widget = this.widgets.find(w => w.id === visibleWidget.id);\n      if (widget) {\n        widget.position = position++;\n      }\n    });\n\n    // Poté aktualizujeme pozice skrytých widgetů\n    this.widgets\n      .filter(widget => !visibleWidgets.some(vw => vw.id === widget.id))\n      .forEach(widget => {\n        widget.position = position++;\n      });\n\n    // Seřadíme widgety podle pozice\n    this.widgets.sort((a, b) => a.position - b.position);\n\n    // Uložení konfigurace\n    this.saveDashboardConfig();\n  }\n\n  /**\n   * Změna viditelnosti widgetu\n   */\n  toggleWidgetVisibility(widgetId: string): void {\n    const widget = this.widgets.find(w => w.id === widgetId);\n    if (widget) {\n      widget.visible = !widget.visible;\n      this.saveDashboardConfig();\n    }\n  }\n\n  /**\n   * Kontrola viditelnosti widgetu\n   */\n  isWidgetVisible(widgetId: string): boolean {\n    const widget = this.widgets.find(w => w.id === widgetId);\n    return widget ? widget.visible : true;\n  }\n\n  /**\n   * Uložení konfigurace dashboardu\n   */\n  saveDashboardConfig(): void {\n    if (!this.dashboardConfig) {\n      return;\n    }\n\n    this.dashboardConfig.widgets = [...this.widgets];\n    this.dashboardConfig.lastModified = new Date();\n\n    this.dashboardConfigService.saveDashboardConfig(this.dashboardConfig).subscribe({\n      next: (config) => {\n        console.log('Konfigurace dashboardu byla úspěšně uložena');\n      },\n      error: (error) => {\n        console.error('Chyba při ukládání konfigurace dashboardu', error);\n      }\n    });\n  }\n\n  /**\n   * Reset konfigurace dashboardu na výchozí hodnoty\n   */\n  resetDashboardConfig(): void {\n    if (confirm('Opravdu chcete resetovat dashboard na výchozí nastavení?')) {\n      this.dashboardConfigService.resetDashboardConfig().subscribe({\n        next: (config) => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n          this.editMode = false;\n          this.refreshCharts();\n        },\n        error: (error) => {\n          console.error('Chyba při resetování konfigurace dashboardu', error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Otevře modální okno s grafem v režimu \"full screen\"\n   * @param chart Instance grafu nebo reference na canvas element\n   * @param title Titulek grafu\n   */\n  openFullscreenChart(chart: Chart | HTMLCanvasElement | null, title: string): void {\n    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart\n    if (chart instanceof HTMLCanvasElement) {\n      // Najdeme instanci Chart pro daný canvas element\n      const chartInstance = Chart.getChart(chart);\n      this.chartModalService.openChartModal(chartInstance || null, title);\n    } else {\n      this.chartModalService.openChartModal(chart, title);\n    }\n  }\n\n  /**\n   * Inicializace popoverů pro nápovědu\n   */\n  private initPopovers(): void {\n    // Nejprve zničíme existující popovers\n    this.destroyPopovers();\n\n    // Definice obsahu nápověd\n    const helpContent: Record<string, string> = {\n      'api-performance': 'Graf zobrazuje top 10 nejpomalejších metod seřazených podle 95. percentilu doby odezvy. ' +\n        'Pro každou metodu jsou zobrazeny dva sloupce: průměrná doba odezvy (modrý sloupec) a maximální doba odezvy (červený sloupec). ' +\n        'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',\n      'api-calls': 'Graf zobrazuje počet DISApi volání v čase pro vybrané období. ' +\n        'Zobrazuje volání z DIS aplikací na DISAdmin.DISApi službu. ' +\n        'Data jsou získávána z tabulky ActivityLogs za zvolené období.',\n      'instances-usage': 'Graf zobrazuje celkový počet volání DIS metod podle instancí. ' +\n        'Ukazuje, které instance jsou nejvíce zatížené podle skutečného počtu volání metod. ' +\n        'Data jsou získávána z tabulky PerformanceMetrics jako suma TotalCount za zvolené období.',\n      'production-performance': 'Graf zobrazuje průměrnou dobu odezvy a 95. percentil pouze pro produkční instance. ' +\n        'Produkční instance jsou kritické pro běžný provoz a jejich výkon je klíčový pro uživatelskou spokojenost. ' +\n        '95. percentil ukazuje, že 95% všech volání bylo rychlejších než zobrazená hodnota, což je lepší indikátor než prostý průměr. ' +\n        'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',\n      'security-events': 'Graf zobrazuje počet bezpečnostních událostí v čase. ' +\n        'Zahrnuje události jako neúspěšné pokusy o přihlášení, podezřelé aktivity a porušení bezpečnosti. ' +\n        'Data jsou získávána z tabulky SecurityLogs za zvolené období.',\n      'system-info': 'Tento widget zobrazuje souhrnné systémové informace včetně počtu aktivních upozornění, ' +\n        'dostupnosti API, průměrné odezvy DIS metod a počtu expirujících certifikátů. ' +\n        'Poskytuje rychlý přehled o celkovém stavu systému.',\n      // Nápovědy pro karty systémových informací\n      'alerts-count': 'Počet aktivních upozornění v systému. Upozornění jsou automaticky generována při detekci problémů s výkonem, ' +\n        'bezpečností nebo dostupností služeb. Kliknutím přejdete na modul Alerty a upozornění.',\n      'security-events-count': 'Počet nevyřešených bezpečnostních událostí včetně neúspěšných pokusů o přihlášení, ' +\n        'podezřelých aktivit a narušení bezpečnosti. Kliknutím přejdete na modul Bezpečnostní události.',\n      'api-availability': 'Procentuální dostupnost DIS API za posledních 24 hodin. Počítá se jako poměr úspěšných volání ' +\n        'k celkovému počtu volání API. Data jsou získávána z tabulky ActivityLogs. Kliknutím přejdete na modul Monitoring.',\n      'api-response-time': 'Průměrná doba odezvy DIS API metod v milisekundách za posledních 24 hodin. ' +\n        'Nižší hodnota znamená lepší výkon systému. Data jsou získávána z tabulky PerformanceMetrics. ' +\n        'Kliknutím přejdete na modul Výkon DIS.',\n      'expiring-certificates': 'Počet certifikátů, které vyprší během následujících 30 dnů. ' +\n        'Včasná obnova certifikátů je kritická pro bezpečnost a nepřerušený provoz systému. ' +\n        'Kliknutím přejdete na modul Správa certifikátů.'\n    };\n\n    // Nejprve zrušíme všechny existující popovery\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popover = bootstrap.Popover.getInstance(el);\n      if (popover) {\n        popover.dispose();\n      }\n    });\n\n    // Inicializace popoverů pomocí Bootstrap API\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const helpType = el.getAttribute('data-help-type');\n      //console.log('Initializing popover for element with help-type:', helpType);\n\n      if (helpType && helpType in helpContent) {\n        try {\n          const popover = new bootstrap.Popover(el, {\n            content: helpContent[helpType as keyof typeof helpContent],\n            html: true,\n            trigger: 'hover focus',\n            placement: 'top',\n            container: 'body',\n            sanitize: false\n          });\n\n          // Přidáme event listener pro kliknutí na klikatelné karty\n          if (el.classList.contains('clickable-card') || el.closest('.clickable-card')) {\n            el.addEventListener('click', () => {\n              popover.hide();\n            });\n          }\n        } catch (error) {\n          console.error('Error initializing popover:', error);\n        }\n      } else if (helpType) {\n        console.warn('Help content not found for type:', helpType);\n      }\n    });\n\n    // Přidáme globální event listener pro zavření popoverů při kliknutí kamkoliv\n    this.documentClickListener = (event) => {\n      const target = event.target as HTMLElement;\n      // Pokud se nekliklo na element s popoverem nebo na samotný popover, zavřeme všechny popovers\n      if (!target.closest('[data-bs-toggle=\"popover\"]') && !target.closest('.popover')) {\n        this.hideAllPopovers();\n      }\n    };\n    document.addEventListener('click', this.documentClickListener);\n  }\n\n  /**\n   * Zavře všechny aktivní popovers\n   */\n  private hideAllPopovers(): void {\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popoverInstance = bootstrap.Popover.getInstance(el);\n      if (popoverInstance) {\n        popoverInstance.hide();\n      }\n    });\n  }\n\n  /**\n   * Zničí všechny popovers\n   */\n  private destroyPopovers(): void {\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popoverInstance = bootstrap.Popover.getInstance(el);\n      if (popoverInstance) {\n        popoverInstance.dispose();\n      }\n    });\n  }\n\n  /**\n   * Navigace na modul Alerty a upozornění\n   */\n  navigateToAlerts(): void {\n    this.hideAllPopovers();\n    this.router.navigate(['/alerts']);\n  }\n\n  /**\n   * Navigace na modul Bezpečnostní události\n   */\n  navigateToSecurityEvents(): void {\n    this.hideAllPopovers();\n    this.router.navigate(['/security']);\n  }\n\n  /**\n   * Navigace na modul Certifikáty\n   */\n  navigateToCertificates(): void {\n    this.hideAllPopovers();\n    this.router.navigate(['/certificates']);\n  }\n\n  /**\n   * Navigace na modul Monitoring\n   */\n  navigateToMonitoring(): void {\n    this.hideAllPopovers();\n    this.router.navigate(['/monitoring']);\n  }\n\n  /**\n   * Navigace na modul Výkon DIS\n   */\n  navigateToPerformance(): void {\n    this.hideAllPopovers();\n    this.router.navigate(['/performance']);\n  }\n\n  /**\n   * Inteligentní zkrácení názvu metody pro zobrazení v grafu\n   * @param text Text k zkrácení\n   * @param maxLength Maximální délka (výchozí 30 znaků)\n   * @returns Zkrácený text s \"...\" pokud je potřeba\n   */\n  private truncateMethodName(text: string, maxLength: number = 30): string {\n    if (!text || text.length <= maxLength) {\n      return text;\n    }\n\n    // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n    const truncated = text.substring(0, maxLength - 3);\n    const lastDotIndex = truncated.lastIndexOf('.');\n\n    // Pokud najdeme tečku a není příliš na začátku, zkrátíme na ní\n    if (lastDotIndex > 0 && lastDotIndex > maxLength * 0.4) {\n      return text.substring(0, lastDotIndex) + '...';\n    }\n\n    // Jinak zkrátíme na pevné délce\n    return truncated + '...';\n  }\n\n  /**\n   * Vytvoří konfiguraci pro zkrácení popisků na ose X\n   * @param originalLabels Originální popisky\n   * @param options Možnosti konfigurace\n   * @returns Objekt s konfigurací pro Chart.js\n   */\n  private createTruncatedLabelsConfig(originalLabels: string[], options: {\n    enabled?: boolean;\n    maxLength?: number;\n    showFullInTooltip?: boolean;\n  } = {}) {\n    const config = {\n      enabled: options.enabled ?? true,\n      maxLength: options.maxLength ?? 30,\n      showFullInTooltip: options.showFullInTooltip ?? true\n    };\n\n    if (!config.enabled) {\n      return {};\n    }\n\n    const result: any = {\n      scales: {\n        x: {\n          ticks: {\n            callback: (value: any, index: number) => {\n              const label = originalLabels[index];\n              return this.truncateMethodName(label, config.maxLength);\n            }\n          }\n        }\n      }\n    };\n\n    if (config.showFullInTooltip) {\n      result.plugins = {\n        tooltip: {\n          callbacks: {\n            title: (context: any) => {\n              const dataIndex = context[0].dataIndex;\n              return originalLabels[dataIndex] || context[0].label;\n            }\n          }\n        }\n      };\n    }\n\n    return result;\n  }\n\n  /**\n   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu\n   * @param baseConfig Základní konfigurace grafu\n   * @param truncatedConfig Konfigurace pro zkrácené popisky\n   * @returns Sloučená konfigurace\n   */\n  private mergeChartConfig(baseConfig: any, truncatedConfig: any): any {\n    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {\n      return baseConfig;\n    }\n\n    const merged = { ...baseConfig };\n\n    // Sloučení scales\n    if (truncatedConfig.scales) {\n      merged.scales = merged.scales || {};\n      if (truncatedConfig.scales.x) {\n        merged.scales.x = merged.scales.x || {};\n        merged.scales.x.ticks = {\n          ...merged.scales.x.ticks,\n          ...truncatedConfig.scales.x.ticks\n        };\n      }\n    }\n\n    // Sloučení plugins\n    if (truncatedConfig.plugins) {\n      merged.plugins = merged.plugins || {};\n      if (truncatedConfig.plugins.tooltip) {\n        merged.plugins.tooltip = merged.plugins.tooltip || {};\n        merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};\n\n        // Sloučení tooltip callbacks - zachováme existující a přidáme nové\n        Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {\n          merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];\n        });\n      }\n    }\n\n    return merged;\n  }\n}\n", "<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2>Dashboard</h2>\n    <div class=\"d-flex gap-2 flex-wrap\">\n      <div class=\"btn-group\" role=\"group\">\n        <button type=\"button\" class=\"btn\" [class.btn-primary]=\"selectedDays === 1\" [class.btn-outline-primary]=\"selectedDays !== 1\" (click)=\"onDaysChange(1)\">1 den</button>\n        <button type=\"button\" class=\"btn\" [class.btn-primary]=\"selectedDays === 7\" [class.btn-outline-primary]=\"selectedDays !== 7\" (click)=\"onDaysChange(7)\">7 dní</button>\n        <button type=\"button\" class=\"btn\" [class.btn-primary]=\"selectedDays === 30\" [class.btn-outline-primary]=\"selectedDays !== 30\" (click)=\"onDaysChange(30)\">30 dní</button>\n        <button type=\"button\" class=\"btn\" [class.btn-primary]=\"selectedDays === 90\" [class.btn-outline-primary]=\"selectedDays !== 90\" (click)=\"onDaysChange(90)\">90 dní</button>\n      </div>\n      <div class=\"d-flex gap-2 flex-wrap\">\n        <button class=\"btn btn-outline-primary\" (click)=\"refreshCharts()\">\n          <i class=\"bi bi-arrow-clockwise me-1\"></i> <span class=\"d-none d-sm-inline\">Aktualizovat</span>\n        </button>\n        <button class=\"btn\" [class.btn-outline-warning]=\"!editMode\" [class.btn-warning]=\"editMode\" (click)=\"toggleEditMode()\">\n          <i class=\"bi\" [ngClass]=\"{'bi-pencil-square': !editMode, 'bi-check-lg': editMode}\"></i>\n          <span class=\"d-none d-sm-inline\">{{ editMode ? 'Dokončit úpravy' : 'Upravit dashboard' }}</span>\n        </button>\n        <button *ngIf=\"editMode\" class=\"btn btn-outline-danger\" (click)=\"resetDashboardConfig()\">\n          <i class=\"bi bi-arrow-counterclockwise me-1\"></i> <span class=\"d-none d-sm-inline\">Resetovat</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <div *ngIf=\"editMode\" class=\"alert alert-info mb-4\">\n    <i class=\"bi bi-info-circle-fill me-2\"></i>\n    Nyní můžete přetáhnout widgety a změnit jejich pořadí. Kliknutím na tlačítko \"Dokončit úpravy\" uložíte změny.\n  </div>\n\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center my-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Načítání...</span>\n    </div>\n  </div>\n\n  <div *ngIf=\"error\" class=\"alert alert-danger\">\n    {{ error }}\n  </div>\n\n\n\n  <!-- Grafy -->\n  <div cdkDropList class=\"row\" (cdkDropListDropped)=\"onDrop($event)\" [cdkDropListDisabled]=\"!editMode\">\n    <!-- Graf API volání -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isWidgetVisible('api-calls')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"api-calls\" style=\"cursor: help;\">DISApi volání v čase</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(apiCallsChart, 'DISApi volání v čase')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #apiCallsChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n\n    <!-- Graf výkonu API -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isWidgetVisible('api-performance')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"api-performance\" style=\"cursor: help;\">Odezva DIS metod</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(apiPerformanceChart, 'Odezva DIS metod')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #apiPerformanceChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n\n    <!-- Graf vytížení instancí -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isWidgetVisible('instances-usage')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"instances-usage\" style=\"cursor: help;\">Volání DIS metod podle instancí</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(instancesUsageChart, 'Volání DIS metod podle instancí')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #instancesUsageChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n\n    <!-- Graf výkonu produkčních instancí -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isWidgetVisible('production-performance')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"production-performance\" style=\"cursor: help;\">Průměrná odezva a 95. percentil produkčních instancí</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(productionPerformanceChart, 'Průměrná odezva a 95. percentil produkčních instancí')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #productionPerformanceChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n\n    <!-- Graf bezpečnostních událostí -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isAdmin && isWidgetVisible('security-events')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"security-events\" style=\"cursor: help;\">Bezpečnostní události</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(securityEventsChart, 'Bezpečnostní události')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #securityEventsChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Systémové informace -->\n  <div class=\"row\" *ngIf=\"isAdmin && isWidgetVisible('system-info')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n    <div class=\"col-12 mb-4\">\n      <div class=\"card\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"system-info\" style=\"cursor: help;\">Systémové informace</h5>\n          <div *ngIf=\"editMode\" class=\"widget-controls\">\n            <button class=\"btn btn-sm btn-outline-secondary\" (click)=\"toggleWidgetVisibility('system-info')\">\n              <i class=\"bi bi-eye-slash\"></i>\n            </button>\n          </div>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"row\">\n            <div class=\"col-6 col-lg-2-4 mb-3\">\n              <div class=\"card bg-primary text-white clickable-card\"\n                   (click)=\"navigateToAlerts()\"\n                   style=\"cursor: pointer;\"\n                   data-bs-toggle=\"popover\"\n                   data-help-type=\"alerts-count\">\n                <div class=\"card-body text-center compact-card\">\n                  <h3 class=\"h2 mb-1\">{{ dashboardSummary.alertsCount || 0 }}</h3>\n                  <p class=\"mb-0 card-label\">Aktivní upozornění</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-lg-2-4 mb-3\" *ngIf=\"isAdmin\">\n              <div class=\"card bg-danger text-white clickable-card\"\n                   (click)=\"navigateToSecurityEvents()\"\n                   style=\"cursor: pointer;\"\n                   data-bs-toggle=\"popover\"\n                   data-help-type=\"security-events-count\">\n                <div class=\"card-body text-center compact-card\">\n                  <h3 class=\"h2 mb-1\">{{ dashboardSummary.securityEventsCount || 0 }}</h3>\n                  <p class=\"mb-0 card-label\">Nevyřešených bezp. událostí</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-lg-2-4 mb-3\">\n              <div class=\"card bg-success text-white clickable-card\"\n                   (click)=\"navigateToMonitoring()\"\n                   style=\"cursor: pointer;\"\n                   data-bs-toggle=\"popover\"\n                   data-help-type=\"api-availability\">\n                <div class=\"card-body text-center compact-card\">\n                  <h3 class=\"h2 mb-1\">{{ systemStatistics.ApiAvailability || 100 }}%</h3>\n                  <p class=\"mb-0 card-label\">Dostupnost DIS API</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-lg-2-4 mb-3\">\n              <div class=\"card bg-info text-white clickable-card\"\n                   (click)=\"navigateToPerformance()\"\n                   style=\"cursor: pointer;\"\n                   data-bs-toggle=\"popover\"\n                   data-help-type=\"api-response-time\">\n                <div class=\"card-body text-center compact-card\">\n                  <h3 class=\"h2 mb-1\">{{ systemStatistics.AvgApiResponseTime || 0 }}ms</h3>\n                  <p class=\"mb-0 card-label\">Průměrná odezva DIS metod</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-lg-2-4 mb-3\">\n              <div class=\"card bg-warning text-dark clickable-card\"\n                   (click)=\"navigateToCertificates()\"\n                   style=\"cursor: pointer;\"\n                   data-bs-toggle=\"popover\"\n                   data-help-type=\"expiring-certificates\">\n                <div class=\"card-body text-center compact-card\">\n                  <h3 class=\"h2 mb-1\">{{ dashboardSummary.expiringCertificatesCount || 0 }}</h3>\n                  <p class=\"mb-0 card-label\">Expirující certifikáty</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}