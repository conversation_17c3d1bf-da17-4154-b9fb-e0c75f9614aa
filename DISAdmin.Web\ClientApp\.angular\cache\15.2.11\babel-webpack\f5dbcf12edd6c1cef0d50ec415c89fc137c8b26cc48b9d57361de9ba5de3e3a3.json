{"ast": null, "code": "import { Chart, registerables } from 'chart.js';\nimport { minArrayLength } from '../shared/advanced-filter/advanced-filter.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/performance.service\";\nimport * as i2 from \"../services/instance.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../services/chart-modal.service\";\nimport * as i5 from \"../services/table-sorting.service\";\nimport * as i6 from \"../services/text-utils.service\";\nimport * as i7 from \"../shared/advanced-filter/advanced-filter.component\";\nimport * as i8 from \"../shared/tab-navigation/tab-navigation.component\";\nconst _c0 = [\"aggregatedPerformanceChart\"];\nconst _c1 = [\"instancesComparisonChart\"];\nconst _c2 = [\"versionsComparisonChart\"];\nconst _c3 = [\"methodDetailChart\"];\nfunction PerformanceComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_5_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.error = null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction PerformanceComponent_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_6_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Zpracov\\u00E1n\\u00ED dat grafu...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_6_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Nejsou k dispozici \\u017E\\u00E1dn\\u00E1 data pro zobrazen\\u00ED grafu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_6_div_3_canvas_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 34, 35);\n  }\n}\nfunction PerformanceComponent_div_6_div_3_tr_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\")(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_tr_51_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const method_r16 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.showMethodDetail(method_r16));\n    });\n    i0.ɵɵelement(26, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r16.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r16.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r16.methodName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r16.totalCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, method_r16.min, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 12, method_r16.max, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 15, method_r16.avg, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, method_r16.median, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, method_r16.percentil95, \"1.0-2\"));\n  }\n}\nfunction PerformanceComponent_div_6_div_3_tr_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"\\u017D\\u00E1dn\\u00E9 metody k zobrazen\\u00ED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"h5\", 15);\n    i0.ɵɵtext(4, \"Agregovan\\u00FD v\\u00FDkon v \\u010Dase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.openFullscreenChart(ctx_r19.aggregatedPerformanceChart, \"Agregovan\\u00FD v\\u00FDkon v \\u010Dase\"));\n    });\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 19);\n    i0.ɵɵtemplate(9, PerformanceComponent_div_6_div_3_div_9_Template, 6, 0, \"div\", 20);\n    i0.ɵɵtemplate(10, PerformanceComponent_div_6_div_3_div_10_Template, 4, 0, \"div\", 21);\n    i0.ɵɵtemplate(11, PerformanceComponent_div_6_div_3_canvas_11_Template, 2, 0, \"canvas\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"div\", 14)(14, \"h5\", 23);\n    i0.ɵɵtext(15, \"Nejpomalej\\u0161\\u00ED metody\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 18)(17, \"div\", 24)(18, \"table\", 25)(19, \"thead\", 26)(20, \"tr\", 27)(21, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_21_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onSort(\"instanceName\"));\n    });\n    i0.ɵɵtext(22, \" Instance \");\n    i0.ɵɵelement(23, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_24_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onSort(\"className\"));\n    });\n    i0.ɵɵtext(25, \" T\\u0159\\u00EDda \");\n    i0.ɵɵelement(26, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_27_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onSort(\"methodName\"));\n    });\n    i0.ɵɵtext(28, \" Metoda \");\n    i0.ɵɵelement(29, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_30_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.onSort(\"totalCount\"));\n    });\n    i0.ɵɵtext(31, \" Po\\u010Det vol\\u00E1n\\u00ED \");\n    i0.ɵɵelement(32, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_33_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onSort(\"min\"));\n    });\n    i0.ɵɵtext(34, \" Min (ms) \");\n    i0.ɵɵelement(35, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_36_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onSort(\"max\"));\n    });\n    i0.ɵɵtext(37, \" Max (ms) \");\n    i0.ɵɵelement(38, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_39_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.onSort(\"avg\"));\n    });\n    i0.ɵɵtext(40, \" Pr\\u016Fm\\u011Br (ms) \");\n    i0.ɵɵelement(41, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_42_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onSort(\"median\"));\n    });\n    i0.ɵɵtext(43, \" Medi\\u00E1n (ms) \");\n    i0.ɵɵelement(44, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_45_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.onSort(\"percentil95\"));\n    });\n    i0.ɵɵtext(46, \" 95. percentil (ms) \");\n    i0.ɵɵelement(47, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"th\");\n    i0.ɵɵtext(49, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"tbody\");\n    i0.ɵɵtemplate(51, PerformanceComponent_div_6_div_3_tr_51_Template, 27, 24, \"tr\", 30);\n    i0.ɵɵtemplate(52, PerformanceComponent_div_6_div_3_tr_52_Template, 3, 0, \"tr\", 4);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.renderingAggregatedChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.hasAggregatedChartData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.hasAggregatedChartData);\n    i0.ɵɵadvance(12);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"instanceName\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"instanceName\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"instanceName\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"className\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"className\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"className\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"methodName\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"methodName\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"methodName\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"totalCount\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"totalCount\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"totalCount\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"min\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"min\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"min\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"max\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"max\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"max\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"avg\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"avg\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"avg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"median\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"median\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"median\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"percentil95\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"percentil95\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"percentil95\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.slowestMethods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.slowestMethods || ctx_r9.slowestMethods.length === 0);\n  }\n}\nfunction PerformanceComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_6_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.onAggregatedFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_6_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_6_div_3_Template, 53, 59, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_aggregated\")(\"fields\", ctx_r1.aggregatedFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction PerformanceComponent_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_7_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Zpracov\\u00E1n\\u00ED dat grafu...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_7_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Nejsou k dispozici \\u017E\\u00E1dn\\u00E1 data pro zobrazen\\u00ED grafu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_7_div_3_canvas_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 34, 41);\n  }\n}\nfunction PerformanceComponent_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"h5\", 40);\n    i0.ɵɵtext(4, \"Srovn\\u00E1n\\u00ED v\\u00FDkonu metod mezi instancemi\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_7_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.openFullscreenChart(ctx_r38.instancesComparisonChart, \"Srovn\\u00E1n\\u00ED v\\u00FDkonu metod mezi instancemi\"));\n    });\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 19);\n    i0.ɵɵtemplate(9, PerformanceComponent_div_7_div_3_div_9_Template, 6, 0, \"div\", 20);\n    i0.ɵɵtemplate(10, PerformanceComponent_div_7_div_3_div_10_Template, 4, 0, \"div\", 21);\n    i0.ɵɵtemplate(11, PerformanceComponent_div_7_div_3_canvas_11_Template, 2, 0, \"canvas\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.renderingInstancesComparisonChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r33.hasInstancesComparisonChartData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.hasInstancesComparisonChartData);\n  }\n}\nfunction PerformanceComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_7_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.onInstancesComparisonFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_7_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_7_div_3_Template, 12, 3, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_instances_comparison\")(\"fields\", ctx_r2.instancesComparisonFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading);\n  }\n}\nfunction PerformanceComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_8_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Zpracov\\u00E1n\\u00ED dat grafu...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_8_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Nejsou k dispozici \\u017E\\u00E1dn\\u00E1 data pro zobrazen\\u00ED grafu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_8_div_3_canvas_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 34, 43);\n  }\n}\nfunction PerformanceComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"h5\", 42);\n    i0.ɵɵtext(4, \"Srovn\\u00E1n\\u00ED v\\u00FDkonu metod mezi verzemi\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_8_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.openFullscreenChart(ctx_r48.versionsComparisonChart, \"Srovn\\u00E1n\\u00ED v\\u00FDkonu metod mezi verzemi\"));\n    });\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 19);\n    i0.ɵɵtemplate(9, PerformanceComponent_div_8_div_3_div_9_Template, 6, 0, \"div\", 20);\n    i0.ɵɵtemplate(10, PerformanceComponent_div_8_div_3_div_10_Template, 4, 0, \"div\", 21);\n    i0.ɵɵtemplate(11, PerformanceComponent_div_8_div_3_canvas_11_Template, 2, 0, \"canvas\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r43.renderingVersionsComparisonChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r43.hasVersionsComparisonChartData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r43.hasVersionsComparisonChartData);\n  }\n}\nfunction PerformanceComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_8_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.onVersionsComparisonFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_8_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_8_div_3_Template, 12, 3, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_versions_comparison\")(\"fields\", ctx_r3.versionsComparisonFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading);\n  }\n}\nfunction PerformanceComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵtext(1, \"Nejpomalej\\u0161\\u00ED metody\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \"Nej\\u010Dast\\u011Bji volan\\u00E9 metody\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \"V\\u0161echny metody\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_42_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\")(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_ng_container_42_tr_1_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r64);\n      const method_r62 = restoredCtx.$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r63.showMethodDetail(method_r62));\n    });\n    i0.ɵɵelement(26, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r62 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r62.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r62.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r62.methodName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r62.totalCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, method_r62.min, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 12, method_r62.max, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 15, method_r62.avg, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, method_r62.median, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, method_r62.percentil95, \"1.0-2\"));\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_42_tr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"\\u017D\\u00E1dn\\u00E9 metody k zobrazen\\u00ED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PerformanceComponent_div_9_div_3_ng_container_42_tr_1_Template, 27, 24, \"tr\", 30);\n    i0.ɵɵtemplate(2, PerformanceComponent_div_9_div_3_ng_container_42_tr_2_Template, 3, 0, \"tr\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r57.slowestMethods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.slowestMethods || ctx_r57.slowestMethods.length === 0);\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_43_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\")(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_ng_container_43_tr_1_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const method_r67 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r68.showMethodDetail(method_r67));\n    });\n    i0.ɵɵelement(26, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r67 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r67.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r67.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r67.methodName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r67.totalCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, method_r67.min, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 12, method_r67.max, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 15, method_r67.avg, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, method_r67.median, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, method_r67.percentil95, \"1.0-2\"));\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_43_tr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"\\u017D\\u00E1dn\\u00E9 metody k zobrazen\\u00ED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PerformanceComponent_div_9_div_3_ng_container_43_tr_1_Template, 27, 24, \"tr\", 30);\n    i0.ɵɵtemplate(2, PerformanceComponent_div_9_div_3_ng_container_43_tr_2_Template, 3, 0, \"tr\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r58.mostCalledMethods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r58.mostCalledMethods || ctx_r58.mostCalledMethods.length === 0);\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_44_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\")(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_ng_container_44_tr_1_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r74);\n      const method_r72 = restoredCtx.$implicit;\n      const ctx_r73 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r73.showMethodDetail(method_r72));\n    });\n    i0.ɵɵelement(26, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r72 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r72.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r72.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r72.methodName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r72.totalCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, method_r72.min, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 12, method_r72.max, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 15, method_r72.avg, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, method_r72.median, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, method_r72.percentil95, \"1.0-2\"));\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_44_tr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"\\u017D\\u00E1dn\\u00E9 metody k zobrazen\\u00ED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PerformanceComponent_div_9_div_3_ng_container_44_tr_1_Template, 27, 24, \"tr\", 30);\n    i0.ɵɵtemplate(2, PerformanceComponent_div_9_div_3_ng_container_44_tr_2_Template, 3, 0, \"tr\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r59.allMethods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r59.allMethods || ctx_r59.allMethods.length === 0);\n  }\n}\nfunction PerformanceComponent_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"h5\", 44);\n    i0.ɵɵtemplate(4, PerformanceComponent_div_9_div_3_span_4_Template, 2, 0, \"span\", 45);\n    i0.ɵɵtemplate(5, PerformanceComponent_div_9_div_3_span_5_Template, 2, 0, \"span\", 46);\n    i0.ɵɵtemplate(6, PerformanceComponent_div_9_div_3_span_6_Template, 2, 0, \"span\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 24)(9, \"table\", 25)(10, \"thead\", 26)(11, \"tr\", 27)(12, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_12_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r75.onSort(\"instanceName\"));\n    });\n    i0.ɵɵtext(13, \" Instance \");\n    i0.ɵɵelement(14, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_15_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r77 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r77.onSort(\"className\"));\n    });\n    i0.ɵɵtext(16, \" T\\u0159\\u00EDda \");\n    i0.ɵɵelement(17, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_18_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.onSort(\"methodName\"));\n    });\n    i0.ɵɵtext(19, \" Metoda \");\n    i0.ɵɵelement(20, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_21_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r79 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r79.onSort(\"totalCount\"));\n    });\n    i0.ɵɵtext(22, \" Po\\u010Det vol\\u00E1n\\u00ED \");\n    i0.ɵɵelement(23, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_24_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r80 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r80.onSort(\"min\"));\n    });\n    i0.ɵɵtext(25, \" Min (ms) \");\n    i0.ɵɵelement(26, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_27_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r81 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r81.onSort(\"max\"));\n    });\n    i0.ɵɵtext(28, \" Max (ms) \");\n    i0.ɵɵelement(29, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_30_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r82 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r82.onSort(\"avg\"));\n    });\n    i0.ɵɵtext(31, \" Pr\\u016Fm\\u011Br (ms) \");\n    i0.ɵɵelement(32, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_33_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r83 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r83.onSort(\"median\"));\n    });\n    i0.ɵɵtext(34, \" Medi\\u00E1n (ms) \");\n    i0.ɵɵelement(35, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_36_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r84 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r84.onSort(\"percentil95\"));\n    });\n    i0.ɵɵtext(37, \" 95. percentil (ms) \");\n    i0.ɵɵelement(38, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"th\");\n    i0.ɵɵtext(40, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"tbody\");\n    i0.ɵɵtemplate(42, PerformanceComponent_div_9_div_3_ng_container_42_Template, 3, 2, \"ng-container\", 4);\n    i0.ɵɵtemplate(43, PerformanceComponent_div_9_div_3_ng_container_43_Template, 3, 2, \"ng-container\", 4);\n    i0.ɵɵtemplate(44, PerformanceComponent_div_9_div_3_ng_container_44_Template, 3, 2, \"ng-container\", 4);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"slowest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"most-called\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"all\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"instanceName\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"instanceName\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"instanceName\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"className\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"className\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"className\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"methodName\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"methodName\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"methodName\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"totalCount\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"totalCount\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"totalCount\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"min\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"min\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"min\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"max\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"max\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"max\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"avg\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"avg\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"avg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"median\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"median\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"median\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"percentil95\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"percentil95\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"percentil95\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"slowest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"most-called\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"all\");\n  }\n}\nfunction PerformanceComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_9_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r86);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.onMethodsFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_9_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_9_div_3_Template, 45, 60, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_methods\")(\"fields\", ctx_r4.methodsFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.loading);\n  }\n}\nfunction PerformanceComponent_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_10_div_3_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Zpracov\\u00E1n\\u00ED dat grafu...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_10_div_3_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Nejsou k dispozici \\u017E\\u00E1dn\\u00E1 data pro zobrazen\\u00ED grafu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_10_div_3_canvas_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 34, 62);\n  }\n}\nfunction PerformanceComponent_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r94 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 51)(2, \"div\", 52)(3, \"div\", 53)(4, \"div\", 54)(5, \"h5\", 55);\n    i0.ɵɵtext(6, \"Pr\\u016Fm\\u011Brn\\u00E1 doba odezvy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 56);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 57);\n    i0.ɵɵtext(11, \"ms\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 52)(13, \"div\", 53)(14, \"div\", 54)(15, \"h5\", 58);\n    i0.ɵɵtext(16, \"Medi\\u00E1n doby odezvy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 56);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 57);\n    i0.ɵɵtext(21, \"ms\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 52)(23, \"div\", 53)(24, \"div\", 54)(25, \"h5\", 59);\n    i0.ɵɵtext(26, \"95. percentil\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 56);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 57);\n    i0.ɵɵtext(31, \"ms\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 52)(33, \"div\", 53)(34, \"div\", 54)(35, \"h5\", 60);\n    i0.ɵɵtext(36, \"Celkov\\u00FD po\\u010Det vol\\u00E1n\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\", 56);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\", 57);\n    i0.ɵɵtext(40, \"za zvolen\\u00E9 obdob\\u00ED\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(41, \"div\", 13)(42, \"div\", 14)(43, \"h5\", 61);\n    i0.ɵɵtext(44, \"V\\u00FDkon metody v \\u010Dase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_10_div_3_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r94);\n      const ctx_r93 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r93.openFullscreenChart(ctx_r93.methodDetailChart, \"V\\u00FDkon metody v \\u010Dase\"));\n    });\n    i0.ɵɵelement(46, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 18)(48, \"div\", 19);\n    i0.ɵɵtemplate(49, PerformanceComponent_div_10_div_3_div_49_Template, 6, 0, \"div\", 20);\n    i0.ɵɵtemplate(50, PerformanceComponent_div_10_div_3_div_50_Template, 4, 0, \"div\", 21);\n    i0.ɵɵtemplate(51, PerformanceComponent_div_10_div_3_canvas_51_Template, 2, 0, \"canvas\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r88 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 7, ctx_r88.methodDetailData.overallStats == null ? null : ctx_r88.methodDetailData.overallStats.avg, \"1.0-2\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 10, ctx_r88.methodDetailData.overallStats == null ? null : ctx_r88.methodDetailData.overallStats.median, \"1.0-2\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 13, ctx_r88.methodDetailData.overallStats == null ? null : ctx_r88.methodDetailData.overallStats.percentil95, \"1.0-2\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r88.methodDetailData.overallStats == null ? null : ctx_r88.methodDetailData.overallStats.totalCount);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r88.renderingMethodDetailChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r88.hasMethodDetailChartData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r88.hasMethodDetailChartData);\n  }\n}\nfunction PerformanceComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_10_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r95.onMethodDetailFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_10_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_10_div_3_Template, 52, 16, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_method_detail\")(\"fields\", ctx_r5.methodDetailFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.loading && ctx_r5.methodDetailData);\n  }\n}\n// Registrace všech komponent Chart.js\nChart.register(...registerables);\nexport let PerformanceComponent = /*#__PURE__*/(() => {\n  class PerformanceComponent {\n    constructor(performanceService, instanceService, datePipe, chartModalService, changeDetectorRef, tableSortingService, textUtils) {\n      this.performanceService = performanceService;\n      this.instanceService = instanceService;\n      this.datePipe = datePipe;\n      this.chartModalService = chartModalService;\n      this.changeDetectorRef = changeDetectorRef;\n      this.tableSortingService = tableSortingService;\n      this.textUtils = textUtils;\n      // Grafy\n      this.aggregatedPerformanceChart = null;\n      this.instancesComparisonChart = null;\n      this.versionsComparisonChart = null;\n      this.methodDetailChart = null;\n      // Stav vykreslování grafů\n      this.renderingAggregatedChart = false;\n      this.renderingInstancesComparisonChart = false;\n      this.renderingVersionsComparisonChart = false;\n      this.renderingMethodDetailChart = false;\n      // Indikátory, zda jsou k dispozici data pro grafy\n      this.hasAggregatedChartData = true;\n      this.hasInstancesComparisonChartData = true;\n      this.hasVersionsComparisonChartData = true;\n      this.hasMethodDetailChartData = true;\n      // Data\n      this.slowestMethods = [];\n      this.mostCalledMethods = [];\n      this.allMethods = [];\n      this.selectedMethod = null;\n      this.methodDetailData = null;\n      this.instances = [];\n      this.aggregatedPerformanceData = null;\n      this.instancesComparisonData = null;\n      this.versionsComparisonData = null;\n      this.methodDetailChartData = null;\n      // Typ metod pro zobrazení v záložce \"Metody\"\n      this.methodsDisplayType = 'slowest';\n      // Řazení\n      this.sortColumn = 'percentil95';\n      this.sortDirection = 'desc';\n      // Pole pro filtry\n      this.aggregatedFilterFields = [];\n      this.instancesComparisonFilterFields = [];\n      this.versionsComparisonFilterFields = [];\n      this.methodsFilterFields = [];\n      this.methodDetailFilterFields = [];\n      // Stav komponenty\n      this.loading = true;\n      this.error = null;\n      this.activeTab = 'aggregated';\n      this.subscriptions = [];\n      // Definice záložek\n      this.tabs = [{\n        id: 'aggregated',\n        label: 'Agregovaný výkon'\n      }, {\n        id: 'instances-comparison',\n        label: 'Srovnání instancí'\n      }, {\n        id: 'versions-comparison',\n        label: 'Srovnání verzí'\n      }, {\n        id: 'methods',\n        label: 'Metody'\n      }, {\n        id: 'method-detail',\n        label: 'Detail metody'\n      }];\n      // Filtry\n      this.aggregatedFilterData = {\n        period: 7\n      };\n      this.instancesComparisonFilterData = {\n        instanceIds: [],\n        period: 7,\n        className: '',\n        methodName: ''\n      };\n      this.versionsComparisonFilterData = {\n        period: 90,\n        className: '',\n        methodName: ''\n      };\n      this.methodDetailFilterData = {\n        period: 30,\n        className: '',\n        methodName: ''\n      };\n    }\n    ngOnInit() {\n      console.log('PerformanceComponent - ngOnInit');\n      // Nejprve inicializujeme prázdná pole filtrů, aby se komponenta advanced-filter správně inicializovala\n      this.initEmptyFilterFields();\n      // Načtení uloženého stavu třídění\n      this.loadSortingState();\n      // Načtení poslední aktivní záložky z localStorage\n      const lastActiveTab = this.loadActiveTab();\n      if (lastActiveTab) {\n        console.log(`ngOnInit - Načtena poslední aktivní záložka: ${lastActiveTab}`);\n        this.activeTab = lastActiveTab;\n      }\n      // Inicializace výchozích hodnot filtru\n      this.aggregatedFilterData = {\n        instanceId: undefined,\n        period: '7',\n        fromDate: undefined,\n        toDate: undefined,\n        methodsDisplayType: 'slowest'\n      };\n      // Načtení instancí - toto musí proběhnout jako první\n      this.instanceService.getAll().subscribe({\n        next: data => {\n          console.log('ngOnInit - Instance úspěšně načteny:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n          // Načtení posledního filtru pro aktuální záložku\n          this.loadLastFilter('performance_aggregated');\n          // Přímé volání načtení dat až po načtení instancí\n          console.log('ngOnInit - Volám API pro načtení dat po načtení instancí');\n          // Přímé volání API s použitím uloženého filtru\n          setTimeout(() => {\n            console.log('ngOnInit - Volám API přímo s použitím uloženého filtru:', this.aggregatedFilterData);\n            this.loading = true;\n            // Příprava parametrů pro volání API\n            let instanceId = this.aggregatedFilterData.instanceId;\n            if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n              instanceId = undefined;\n            }\n            let fromDate;\n            let toDate = new Date();\n            // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n            if (!this.aggregatedFilterData.period) {\n              fromDate = new Date();\n              fromDate.setDate(toDate.getDate() - 7); // Výchozí hodnota 7 dní\n            }\n            // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n            else if (this.aggregatedFilterData.period !== 'custom') {\n              fromDate = new Date();\n              fromDate.setDate(toDate.getDate() - Number(this.aggregatedFilterData.period));\n            }\n            // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n            else {\n              // Kontrola, zda fromDate a toDate jsou validní\n              if (this.aggregatedFilterData.fromDate) {\n                fromDate = new Date(this.aggregatedFilterData.fromDate);\n              } else {\n                // Výchozí hodnota, pokud fromDate není zadáno\n                fromDate = new Date();\n                fromDate.setDate(toDate.getDate() - 7);\n              }\n              if (this.aggregatedFilterData.toDate) {\n                toDate = new Date(this.aggregatedFilterData.toDate);\n              }\n            }\n            console.log('ngOnInit - Parametry pro volání API:', {\n              instanceId,\n              fromDate,\n              toDate\n            });\n            const subscription = this.performanceService.getAggregatedPerformance(instanceId, fromDate, toDate).subscribe({\n              next: data => {\n                console.log('ngOnInit - Data úspěšně načtena přímo:', data);\n                // Uložení dat pro pozdější vykreslení\n                this.aggregatedPerformanceData = data;\n                // Vykreslení grafu\n                if (this.aggregatedPerformanceChartRef) {\n                  this.renderAggregatedPerformanceChart(data);\n                }\n                this.loading = false;\n                // Načtení dat pro záložku \"Metody\"\n                setTimeout(() => {\n                  this.loadSlowestMethods();\n                }, 100);\n              },\n              error: error => {\n                console.error('ngOnInit - Chyba při přímém načítání dat:', error);\n                this.loading = false;\n              }\n            });\n            this.subscriptions.push(subscription);\n          }, 500);\n        },\n        error: error => {\n          console.error('ngOnInit - Chyba při načítání instancí:', error);\n        }\n      });\n    }\n    ngAfterViewInit() {\n      console.log('PerformanceComponent - ngAfterViewInit');\n      // Inicializace popoverů pro nápovědu\n      this.initPopovers();\n      // Inicializace výchozího nastavení viditelnosti metrik\n      this.initDefaultMetricsVisibility();\n      // Načtení dat pro aktivní záložku\n      setTimeout(() => {\n        // Simulujeme změnu záložky, aby se načetla data pro aktivní záložku\n        this.changeTab(this.activeTab);\n      }, 500);\n      // Pokud již máme data, zkusíme vykreslit graf\n      if (this.activeTab === 'aggregated' && this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n        console.log('PerformanceComponent - Mám data, zkusím vykreslit graf');\n        setTimeout(() => {\n          this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n        }, 500);\n      } else {\n        console.log('PerformanceComponent - Nemám data nebo canvas element, zkusím později');\n        // Přidáme event listener pro změnu záložky, abychom mohli vykreslit graf, až bude canvas element k dispozici\n        setTimeout(() => {\n          if (this.activeTab === 'aggregated' && this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n            console.log('PerformanceComponent - Mám data a canvas element, zkusím vykreslit graf po timeoutu');\n            this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n          }\n        }, 2000);\n      }\n    }\n    /**\r\n     * Inicializace výchozího nastavení viditelnosti metrik\r\n     */\n    initDefaultMetricsVisibility() {\n      // Výchozí nastavení viditelnosti metrik pro agregovaný výkon\n      if (!localStorage.getItem('metrics_visibility_aggregated')) {\n        const aggregatedVisibility = {\n          'Průměr (ms)': true,\n          'Medián (ms)': true,\n          '95. percentil (ms)': true,\n          '99. percentil (ms)': false,\n          'Minimum (ms)': false,\n          'Maximum (ms)': false,\n          'Počet volání': false\n        };\n        localStorage.setItem('metrics_visibility_aggregated', JSON.stringify(aggregatedVisibility));\n      }\n      // Výchozí nastavení viditelnosti metrik pro detail metody\n      if (!localStorage.getItem('metrics_visibility_method-detail')) {\n        const methodDetailVisibility = {\n          'Průměr (ms)': true,\n          'Medián (ms)': true,\n          '95. percentil (ms)': true,\n          'Počet volání': false\n        };\n        localStorage.setItem('metrics_visibility_method-detail', JSON.stringify(methodDetailVisibility));\n      }\n      // Pro srovnání instancí a verzí nemůžeme nastavit výchozí hodnoty,\n      // protože názvy datasetů závisí na datech z API\n    }\n    /**\r\n     * Inicializace prázdných polí filtrů\r\n     */\n    initEmptyFilterFields() {\n      // Základní pole pro všechny filtry\n      const periodField = {\n        name: 'period',\n        label: 'Časové období',\n        type: 'select',\n        options: [{\n          value: 1,\n          label: '1 den'\n        }, {\n          value: 7,\n          label: '7 dní'\n        }, {\n          value: 30,\n          label: '30 dní'\n        }, {\n          value: 90,\n          label: '90 dní'\n        }, {\n          value: 'custom',\n          label: 'Vlastní období'\n        }]\n      };\n      // Definice polí pro datum od a do\n      const fromDateField = {\n        name: 'fromDate',\n        label: 'Od',\n        type: 'date',\n        visible: values => values.period === 'custom',\n        required: values => values.period === 'custom',\n        errorMessage: 'Zadejte počáteční datum'\n      };\n      const toDateField = {\n        name: 'toDate',\n        label: 'Do',\n        type: 'date',\n        visible: values => values.period === 'custom',\n        required: values => values.period === 'custom',\n        errorMessage: 'Zadejte koncové datum'\n      };\n      // Inicializace prázdných polí filtrů\n      this.aggregatedFilterFields = [{\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [{\n          value: null,\n          label: 'Všechny instance'\n        }]\n      }, periodField, fromDateField, toDateField];\n      this.instancesComparisonFilterFields = [{\n        name: 'instanceIds',\n        label: 'Instance',\n        type: 'multiselect',\n        options: [],\n        required: true,\n        validators: [minArrayLength(2)],\n        errorMessage: 'Vyberte alespoň dvě instance pro srovnání'\n      }, {\n        name: 'className',\n        label: 'Třída',\n        type: 'text'\n      }, {\n        name: 'methodName',\n        label: 'Metoda',\n        type: 'text'\n      }, periodField, fromDateField, toDateField];\n      this.versionsComparisonFilterFields = [{\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [{\n          value: null,\n          label: 'Vyberte instanci'\n        }],\n        required: true,\n        errorMessage: 'Vyberte instanci pro srovnání verzí'\n      }, {\n        name: 'className',\n        label: 'Třída',\n        type: 'text'\n      }, {\n        name: 'methodName',\n        label: 'Metoda',\n        type: 'text'\n      }, periodField, fromDateField, toDateField];\n      this.methodsFilterFields = [{\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [{\n          value: null,\n          label: 'Všechny instance'\n        }]\n      }, {\n        name: 'methodsDisplayType',\n        label: 'Metrika',\n        type: 'select',\n        options: [{\n          value: 'slowest',\n          label: 'Nejpomalejší metody'\n        }, {\n          value: 'most-called',\n          label: 'Nejčastěji volané metody'\n        }, {\n          value: 'all',\n          label: 'Všechny metody'\n        }]\n      }, periodField, fromDateField, toDateField];\n      this.methodDetailFilterFields = [{\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [{\n          value: 0,\n          label: 'Všechny instance'\n        }],\n        required: false\n      }, {\n        name: 'className',\n        label: 'Třída',\n        type: 'text',\n        required: true,\n        errorMessage: 'Zadejte název třídy'\n      }, {\n        name: 'methodName',\n        label: 'Metoda',\n        type: 'text',\n        required: true,\n        errorMessage: 'Zadejte název metody'\n      }, periodField, fromDateField, toDateField];\n    }\n    /**\r\n     * Inicializace polí filtrů\r\n     */\n    initFilterFields() {\n      // Základní pole pro všechny filtry\n      const periodField = {\n        name: 'period',\n        label: 'Časové období',\n        type: 'select',\n        options: [{\n          value: 1,\n          label: '1 den'\n        }, {\n          value: 7,\n          label: '7 dní'\n        }, {\n          value: 30,\n          label: '30 dní'\n        }, {\n          value: 90,\n          label: '90 dní'\n        }, {\n          value: 'custom',\n          label: 'Vlastní období'\n        }]\n      };\n      // Aktualizace pole pro filtr agregovaných metrik\n      const instanceOptions = [{\n        value: null,\n        label: 'Všechny instance'\n      }, ...this.instances.sort((a, b) => {\n        // Nejprve seřadíme podle zkratky zákazníka\n        const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n        // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n        return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n      }).map(instance => ({\n        value: instance.id,\n        label: `${instance.customerAbbreviation} - ${instance.name}`\n      }))];\n      // Aktualizace pole pro filtr agregovaných metrik\n      this.updateFilterField(this.aggregatedFilterFields, 0, {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: instanceOptions\n      });\n      // Aktualizace pole pro filtr srovnání instancí\n      this.updateFilterField(this.instancesComparisonFilterFields, 0, {\n        name: 'instanceIds',\n        label: 'Instance',\n        type: 'multiselect',\n        options: this.instances.sort((a, b) => {\n          // Nejprve seřadíme podle zkratky zákazníka\n          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n          // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n        }).map(instance => ({\n          value: instance.id,\n          label: `${instance.customerAbbreviation} - ${instance.name}`\n        })),\n        required: true,\n        validators: [minArrayLength(2)],\n        errorMessage: 'Vyberte alespoň dvě instance pro srovnání'\n      });\n      // Aktualizace pole pro filtr srovnání verzí\n      this.updateFilterField(this.versionsComparisonFilterFields, 0, {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [{\n          value: null,\n          label: 'Vyberte instanci'\n        }, ...this.instances.sort((a, b) => {\n          // Nejprve seřadíme podle zkratky zákazníka\n          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n          // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n        }).map(instance => ({\n          value: instance.id,\n          label: `${instance.customerAbbreviation} - ${instance.name}`\n        }))],\n        required: true,\n        errorMessage: 'Vyberte instanci pro srovnání verzí'\n      });\n      // Aktualizace pole pro filtr metod\n      this.updateFilterField(this.methodsFilterFields, 0, {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: instanceOptions\n      });\n      // Aktualizace pole pro filtr detailu metody\n      this.updateFilterField(this.methodDetailFilterFields, 0, {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [{\n          value: 0,\n          label: 'Všechny instance'\n        }, ...this.instances.sort((a, b) => {\n          // Nejprve seřadíme podle zkratky zákazníka\n          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n          // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n        }).map(instance => ({\n          value: instance.id,\n          label: `${instance.customerAbbreviation} - ${instance.name}`\n        }))]\n      });\n      // Aktualizace pole period pro všechny filtry\n      this.updateFilterField(this.aggregatedFilterFields, 1, periodField);\n      this.updateFilterField(this.instancesComparisonFilterFields, 3, periodField);\n      this.updateFilterField(this.versionsComparisonFilterFields, 3, periodField);\n      this.updateFilterField(this.methodsFilterFields, 2, periodField); // Index 2, protože na indexu 1 je methodsDisplayType\n      this.updateFilterField(this.methodDetailFilterFields, 3, periodField);\n    }\n    /**\r\n     * Aktualizace pole filtru\r\n     */\n    updateFilterField(filterFields, index, newField) {\n      if (filterFields && filterFields.length > index) {\n        // Zachováme validátory a další vlastnosti z původního pole\n        const originalField = filterFields[index];\n        filterFields[index] = {\n          ...originalField,\n          ...newField\n        };\n        console.log(`Aktualizace pole ${newField.name}:`, filterFields[index]);\n      }\n    }\n    /**\r\n     * Načtení seznamu instancí\r\n     */\n    loadInstances() {\n      console.log('loadInstances - Načítám seznam instancí');\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('loadInstances - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      this.loading = true;\n      this.subscriptions.push(this.instanceService.getAll().subscribe({\n        next: data => {\n          console.log('loadInstances - Data úspěšně načtena:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n          // Načtení posledního filtru pro aktuální záložku\n          this.loadLastFilter('performance_aggregated');\n          // Již nebudeme načítat data zde, protože to děláme přímo v ngOnInit\n          console.log('loadInstances - Data instancí načtena, filtry inicializovány');\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Chyba při načítání instancí:', error);\n          this.loading = false;\n        }\n      }));\n    }\n    /**\r\n     * Změna časového období\r\n     */\n    onPeriodChange() {\n      // Tato metoda je volána při změně období v select boxu\n      // Není potřeba implementovat žádnou logiku, protože\n      // zobrazení/skrytí polí od-do je řešeno pomocí *ngIf v šabloně\n    }\n    ngOnDestroy() {\n      // Zrušení všech aktivních subscriptions\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\r\n     * Otevře modální okno s grafem v režimu \"full screen\"\r\n     * @param chart Instance grafu nebo reference na canvas element\r\n     * @param title Titulek grafu\r\n     */\n    openFullscreenChart(chart, title) {\n      // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart\n      if (chart instanceof HTMLCanvasElement) {\n        // Najdeme instanci Chart pro daný canvas element\n        const chartInstance = Chart.getChart(chart);\n        this.chartModalService.openChartModal(chartInstance || null, title);\n      } else {\n        this.chartModalService.openChartModal(chart, title);\n      }\n    }\n    /**\r\n     * Změna aktivní záložky\r\n     */\n    changeTab(tab) {\n      // Resetujeme chybovou hlášku při změně záložky\n      this.error = '';\n      // Spustíme detekci změn, aby se změna proměnné error projevila okamžitě\n      this.changeDetectorRef.detectChanges();\n      // Převod parametru na string, pokud není\n      const tabId = typeof tab === 'string' ? tab : String(tab);\n      this.activeTab = tabId;\n      // Uložení aktivní záložky do localStorage\n      this.saveActiveTab(tabId);\n      // Pokud je vybrána záložka \"method-detail\", přidáme ji do seznamu záložek\n      if (tabId === 'method-detail' && this.selectedMethod) {\n        // Kontrola, zda záložka již existuje\n        const tabExists = this.tabs.some(t => t.id === 'method-detail');\n        if (!tabExists) {\n          this.tabs.push({\n            id: 'method-detail',\n            label: 'Detail metody'\n          });\n        }\n      }\n      // Načtení dat pro vybranou záložku\n      switch (tabId) {\n        case 'aggregated':\n          // Načtení posledního filtru z localStorage\n          this.loadLastFilter('performance_aggregated');\n          // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n          requestAnimationFrame(() => {\n            // Ještě přidáme timeout pro jistotu\n            setTimeout(() => {\n              console.log('onTabChange - Načítám data pro záložku aggregated');\n              // Pokud již máme data, zkusíme vykreslit graf\n              if (this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n                console.log('onTabChange - Mám data, zkusím vykreslit graf');\n                this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n              } else {\n                // Jinak načteme nová data\n                this.loadAggregatedPerformance();\n              }\n            }, 500);\n          });\n          break;\n        case 'instances-comparison':\n          // Načtení posledního filtru z localStorage\n          this.loadLastFilter('performance_instances_comparison');\n          // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n          requestAnimationFrame(() => {\n            // Ještě přidáme timeout pro jistotu\n            setTimeout(() => {\n              console.log('onTabChange - Načítám data pro záložku instances-comparison');\n              // Pokud již máme data, zkusíme vykreslit graf\n              if (this.instancesComparisonData && this.instancesComparisonChartRef) {\n                console.log('onTabChange - Mám data, zkusím vykreslit graf');\n                this.renderInstancesComparisonChart(this.instancesComparisonData);\n              } else {\n                // Jinak načteme nová data\n                this.loadInstancesComparison();\n              }\n            }, 500);\n          });\n          break;\n        case 'versions-comparison':\n          // Načtení posledního filtru z localStorage\n          this.loadLastFilter('performance_versions_comparison');\n          // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n          requestAnimationFrame(() => {\n            // Ještě přidáme timeout pro jistotu\n            setTimeout(() => {\n              console.log('onTabChange - Načítám data pro záložku versions-comparison');\n              // Pokud již máme data, zkusíme vykreslit graf\n              if (this.versionsComparisonData && this.versionsComparisonChartRef) {\n                console.log('onTabChange - Mám data, zkusím vykreslit graf');\n                this.renderVersionsComparisonChart(this.versionsComparisonData);\n              } else {\n                // Jinak načteme nová data\n                this.loadVersionsComparison();\n              }\n            }, 500);\n          });\n          break;\n        case 'methods':\n          // Načtení posledního filtru z localStorage\n          const lastFilterKey = `last_filter_performance_methods`;\n          const lastFilterJson = localStorage.getItem(lastFilterKey);\n          // Pokud není uložen žádný filtr, resetujeme instanceId na undefined\n          if (!lastFilterJson) {\n            console.log('onTabChange - Není uložen žádný filtr pro záložku methods, resetuji instanceId na undefined');\n            this.aggregatedFilterData.instanceId = undefined;\n          }\n          this.loadLastFilter('performance_methods');\n          // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n          requestAnimationFrame(() => {\n            // Ještě přidáme timeout pro jistotu\n            setTimeout(() => {\n              console.log('onTabChange - Načítám data pro záložku methods, instanceId:', this.aggregatedFilterData.instanceId);\n              this.loadMethodsByType();\n            }, 100);\n          });\n          break;\n        case 'method-detail':\n          // Načtení posledního filtru z localStorage\n          this.loadLastFilter('performance_method_detail');\n          // Resetujeme indikátor, zda jsou k dispozici data pro graf\n          this.hasMethodDetailChartData = true;\n          // Pokud máme vybranou metodu, použijeme její hodnoty pro filtr\n          if (this.selectedMethod) {\n            console.log('onTabChange - Použití hodnot z vybrané metody pro filtr:', this.selectedMethod);\n            // Zjištění aktuální hodnoty instanceId\n            let instanceId = this.selectedMethod.instanceId;\n            // Pokud instanceId je null nebo undefined, použijeme 0 (všechny instance)\n            if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n              instanceId = 0;\n            }\n            // Aktualizace hodnot filtru\n            this.methodDetailFilterData = {\n              ...this.methodDetailFilterData,\n              instanceId: instanceId,\n              className: this.selectedMethod.className,\n              methodName: this.selectedMethod.methodName\n            };\n            // Uložení filtru do localStorage\n            this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n          }\n          // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n          requestAnimationFrame(() => {\n            // Ještě přidáme timeout pro jistotu\n            setTimeout(() => {\n              console.log('onTabChange - Načítám data pro záložku method-detail');\n              // Vždy načteme nová data při přepnutí na záložku\n              this.loadMethodDetail();\n            }, 500);\n          });\n          break;\n      }\n      // Reinicializace popoverů po změně záložky\n      setTimeout(() => {\n        this.initPopovers();\n      }, 500);\n    }\n    /**\r\n     * Načtení posledního filtru z localStorage\r\n     */\n    loadLastFilter(entityType) {\n      console.log(`loadLastFilter - Načítám poslední filtr pro ${entityType}`);\n      try {\n        const lastFilterKey = `last_filter_${entityType}`;\n        const lastFilterJson = localStorage.getItem(lastFilterKey);\n        if (lastFilterJson) {\n          const lastFilter = JSON.parse(lastFilterJson);\n          console.log(`Načten poslední filtr pro ${entityType} z localStorage:`, lastFilter);\n          // Aktualizace příslušného filtru podle typu entity\n          switch (entityType) {\n            case 'performance_aggregated':\n              // Kontrola, zda se filtr skutečně změnil\n              const currentAggregatedFilterStr = JSON.stringify(this.aggregatedFilterData);\n              const newAggregatedFilterStr = JSON.stringify({\n                ...this.aggregatedFilterData,\n                ...lastFilter\n              });\n              if (currentAggregatedFilterStr !== newAggregatedFilterStr) {\n                this.aggregatedFilterData = {\n                  ...this.aggregatedFilterData,\n                  ...lastFilter\n                };\n                console.log(`Aktualizován filtr pro ${entityType}:`, this.aggregatedFilterData);\n              } else {\n                console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n              }\n              break;\n            case 'performance_instances_comparison':\n              // Kontrola, zda se filtr skutečně změnil\n              const currentInstancesComparisonFilterStr = JSON.stringify(this.instancesComparisonFilterData);\n              const newInstancesComparisonFilterStr = JSON.stringify({\n                ...this.instancesComparisonFilterData,\n                ...lastFilter\n              });\n              if (currentInstancesComparisonFilterStr !== newInstancesComparisonFilterStr) {\n                this.instancesComparisonFilterData = {\n                  ...this.instancesComparisonFilterData,\n                  ...lastFilter\n                };\n                console.log(`Aktualizován filtr pro ${entityType}:`, this.instancesComparisonFilterData);\n              } else {\n                console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n              }\n              break;\n            case 'performance_versions_comparison':\n              // Kontrola, zda se filtr skutečně změnil\n              const currentVersionsComparisonFilterStr = JSON.stringify(this.versionsComparisonFilterData);\n              const newVersionsComparisonFilterStr = JSON.stringify({\n                ...this.versionsComparisonFilterData,\n                ...lastFilter\n              });\n              if (currentVersionsComparisonFilterStr !== newVersionsComparisonFilterStr) {\n                this.versionsComparisonFilterData = {\n                  ...this.versionsComparisonFilterData,\n                  ...lastFilter\n                };\n                console.log(`Aktualizován filtr pro ${entityType}:`, this.versionsComparisonFilterData);\n              } else {\n                console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n              }\n              break;\n            case 'performance_methods':\n              // Pro metody používáme stejný filtr jako pro agregované metriky\n              // Kontrola, zda se filtr skutečně změnil\n              const currentMethodsFilterStr = JSON.stringify(this.aggregatedFilterData);\n              const newMethodsFilterStr = JSON.stringify({\n                ...this.aggregatedFilterData,\n                ...lastFilter\n              });\n              if (currentMethodsFilterStr !== newMethodsFilterStr) {\n                this.aggregatedFilterData = {\n                  ...this.aggregatedFilterData,\n                  ...lastFilter\n                };\n                // Aktualizace typu zobrazovaných metod\n                if (lastFilter.methodsDisplayType) {\n                  this.methodsDisplayType = lastFilter.methodsDisplayType;\n                }\n                console.log(`Aktualizován filtr pro ${entityType}:`, this.aggregatedFilterData);\n              } else {\n                console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n              }\n              break;\n            case 'performance_method_detail':\n              // Kontrola, zda se filtr skutečně změnil\n              const currentMethodDetailFilterStr = JSON.stringify(this.methodDetailFilterData);\n              const newMethodDetailFilterStr = JSON.stringify({\n                ...this.methodDetailFilterData,\n                ...lastFilter\n              });\n              if (currentMethodDetailFilterStr !== newMethodDetailFilterStr) {\n                this.methodDetailFilterData = {\n                  ...this.methodDetailFilterData,\n                  ...lastFilter\n                };\n                console.log(`Aktualizován filtr pro ${entityType}:`, this.methodDetailFilterData);\n              } else {\n                console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n              }\n              break;\n          }\n        }\n      } catch (error) {\n        console.error(`Chyba při načítání posledního filtru pro ${entityType} z localStorage`, error);\n      }\n    }\n    /**\r\n     * Načtení agregovaných výkonnostních metrik\r\n     */\n    loadAggregatedPerformance() {\n      console.log('loadAggregatedPerformance - Začínám načítat data');\n      // Přidáme kontrolu, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('loadAggregatedPerformance - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      // Kontrola, zda je reference na canvas element k dispozici\n      if (!this.aggregatedPerformanceChartRef) {\n        console.warn('loadAggregatedPerformance - Reference na canvas element není k dispozici, ale pokračuji v načítání dat');\n      } else {\n        console.log('loadAggregatedPerformance - Reference na canvas element je k dispozici');\n      }\n      // Kontrola, zda máme načtené instance\n      if (!this.instances || this.instances.length === 0) {\n        console.warn('loadAggregatedPerformance - Instance nejsou načteny, načítám je');\n        // Načtení instancí\n        this.instanceService.getAll().subscribe({\n          next: data => {\n            console.log('loadAggregatedPerformance - Instance úspěšně načteny:', data);\n            this.instances = data;\n            // Po načtení instancí inicializujeme pole filtrů\n            this.initFilterFields();\n            // Pokračujeme v načítání dat\n            this.loadAggregatedPerformance();\n          },\n          error: error => {\n            console.error('loadAggregatedPerformance - Chyba při načítání instancí:', error);\n            this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n            this.loading = false;\n          }\n        });\n        return;\n      }\n      // Kontrola, zda máme inicializovaný filtr\n      if (!this.aggregatedFilterData) {\n        console.warn('loadAggregatedPerformance - Filtr není inicializován, inicializuji výchozí hodnoty');\n        this.aggregatedFilterData = {\n          instanceId: undefined,\n          period: '7',\n          fromDate: undefined,\n          toDate: undefined\n        };\n      }\n      // Příprava parametrů\n      // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme undefined\n      let instanceId = this.aggregatedFilterData.instanceId;\n      if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n        instanceId = undefined;\n      }\n      console.log('loadAggregatedPerformance - instanceId:', instanceId);\n      let fromDate;\n      let toDate;\n      // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n      if (!this.aggregatedFilterData.period) {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n        toDate = today;\n      }\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      else if (this.aggregatedFilterData.period !== 'custom') {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n        toDate = today;\n      }\n      // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n      else {\n        // Kontrola, zda fromDate a toDate jsou validní\n        if (this.aggregatedFilterData.fromDate) {\n          fromDate = new Date(this.aggregatedFilterData.fromDate);\n        } else {\n          // Výchozí hodnota, pokud fromDate není zadáno\n          fromDate = new Date();\n          fromDate.setDate(fromDate.getDate() - 7);\n        }\n        if (this.aggregatedFilterData.toDate) {\n          toDate = new Date(this.aggregatedFilterData.toDate);\n        } else {\n          // Výchozí hodnota, pokud toDate není zadáno\n          toDate = new Date();\n        }\n      }\n      console.log('loadAggregatedPerformance - Volám API pro načtení dat', {\n        instanceId,\n        fromDate,\n        toDate\n      });\n      try {\n        const subscription = this.performanceService.getAggregatedPerformance(instanceId, fromDate, toDate).subscribe({\n          next: data => {\n            console.log('loadAggregatedPerformance - Data úspěšně načtena:', data);\n            if (!data) {\n              console.error('loadAggregatedPerformance - API vrátilo prázdná data');\n              this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n              this.loading = false;\n              return;\n            }\n            // Kontrola, zda data obsahují potřebné údaje\n            if (!data.labels && !data.dailyData) {\n              console.warn('loadAggregatedPerformance - Data neobsahují labels ani dailyData, pokusím se je zpracovat');\n            }\n            // Uložení dat pro pozdější vykreslení\n            this.aggregatedPerformanceData = data;\n            // Vykreslení grafu\n            this.renderAggregatedPerformanceChart(data);\n            this.loading = false;\n          },\n          error: error => {\n            console.error('Chyba při načítání agregovaných výkonnostních metrik:', error);\n            this.error = 'Nepodařilo se načíst agregované výkonnostní metriky. ' + (error.message ? error.message : 'Zkuste to prosím znovu.');\n            this.loading = false;\n            // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n            if (error.status === 0) {\n              console.log('loadAggregatedPerformance - Server neodpovídá, zkusím to znovu za 5 sekund');\n              setTimeout(() => {\n                console.log('loadAggregatedPerformance - Opakuji volání API');\n                this.loadAggregatedPerformance();\n              }, 5000);\n            }\n          },\n          complete: () => {\n            console.log('loadAggregatedPerformance - Volání API dokončeno');\n          }\n        });\n        this.subscriptions.push(subscription);\n      } catch (error) {\n        console.error('loadAggregatedPerformance - Chyba při volání API:', error);\n        this.error = 'Nepodařilo se načíst agregované výkonnostní metriky. Zkuste to prosím znovu.';\n        this.loading = false;\n      }\n      // Načtení nejpomalejších metod - pouze pokud je aktivní záložka 'aggregated'\n      if (this.activeTab === 'aggregated') {\n        // Použijeme setTimeout, abychom zajistili, že se volání API nepřekrývají\n        setTimeout(() => {\n          this.loadSlowestMethods();\n        }, 100);\n      }\n    }\n    /**\r\n     * Zpracování změny filtru pro agregované metriky\r\n     */\n    onAggregatedFilterChange(filterData) {\n      console.log('onAggregatedFilterChange - Přijatá data filtru:', filterData);\n      // Kontrola, zda se filtr skutečně změnil\n      const currentFilterStr = JSON.stringify(this.aggregatedFilterData);\n      const newFilterStr = JSON.stringify(filterData);\n      // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n      // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n      if (currentFilterStr === newFilterStr) {\n        console.log('onAggregatedFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n      }\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('onAggregatedFilterChange - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n      if (filterData.period === 'custom') {\n        if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n          filterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (filterData.toDate && typeof filterData.toDate === 'string') {\n          filterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n      else if (filterData.fromDate && filterData.toDate) {\n        if (typeof filterData.fromDate === 'string') {\n          filterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (typeof filterData.toDate === 'string') {\n          filterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      this.aggregatedFilterData = filterData;\n      console.log('onAggregatedFilterChange - Zpracovaná data filtru:', this.aggregatedFilterData);\n      // Použijeme setTimeout, abychom zajistili, že se volání API nepřekrývají\n      setTimeout(() => {\n        this.loadAggregatedPerformance();\n      }, 0);\n    }\n    /**\r\n     * Načtení srovnání výkonu metod mezi instancemi\r\n     */\n    loadInstancesComparison() {\n      console.log('loadInstancesComparison - Začínám načítat data');\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('loadInstancesComparison - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      // Kontrola, zda máme načtené instance\n      if (!this.instances || this.instances.length === 0) {\n        console.warn('loadInstancesComparison - Instance nejsou načteny, načítám je');\n        // Načtení instancí\n        this.instanceService.getAll().subscribe({\n          next: data => {\n            console.log('loadInstancesComparison - Instance úspěšně načteny:', data);\n            this.instances = data;\n            // Po načtení instancí inicializujeme pole filtrů\n            this.initFilterFields();\n            // Pokračujeme v načítání dat\n            this.loadInstancesComparison();\n          },\n          error: error => {\n            console.error('loadInstancesComparison - Chyba při načítání instancí:', error);\n            this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n            this.loading = false;\n          }\n        });\n        return;\n      }\n      // Kontrola, zda jsou vybrány alespoň dvě instance\n      if (!this.instancesComparisonFilterData.instanceIds || this.instancesComparisonFilterData.instanceIds.length < 2) {\n        // Necháme validaci na formuláři, nezobrazujeme chybový blok\n        this.loading = false;\n        return;\n      }\n      // Příprava parametrů\n      const instanceIds = this.instancesComparisonFilterData.instanceIds;\n      const methodName = this.instancesComparisonFilterData.methodName;\n      const className = this.instancesComparisonFilterData.className;\n      let fromDate;\n      let toDate;\n      // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n      if (!this.instancesComparisonFilterData.period) {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n        toDate = today;\n      }\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      else if (this.instancesComparisonFilterData.period !== 'custom') {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - Number(this.instancesComparisonFilterData.period));\n        toDate = today;\n      }\n      // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n      else {\n        // Kontrola, zda fromDate a toDate jsou validní\n        if (this.instancesComparisonFilterData.fromDate) {\n          fromDate = new Date(this.instancesComparisonFilterData.fromDate);\n        } else {\n          // Výchozí hodnota, pokud fromDate není zadáno\n          fromDate = new Date();\n          fromDate.setDate(fromDate.getDate() - 7);\n        }\n        if (this.instancesComparisonFilterData.toDate) {\n          toDate = new Date(this.instancesComparisonFilterData.toDate);\n        } else {\n          // Výchozí hodnota, pokud toDate není zadáno\n          toDate = new Date();\n        }\n      }\n      try {\n        console.log('loadInstancesComparison - Volám API pro načtení dat', {\n          instanceIds,\n          methodName,\n          className,\n          fromDate,\n          toDate\n        });\n        const subscription = this.performanceService.getInstancesComparison(instanceIds, methodName, className, fromDate, toDate).subscribe({\n          next: data => {\n            console.log('loadInstancesComparison - Data úspěšně načtena:', data);\n            if (!data) {\n              console.error('loadInstancesComparison - API vrátilo prázdná data');\n              this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n              this.loading = false;\n              return;\n            }\n            // Uložení dat pro pozdější vykreslení\n            this.instancesComparisonData = data;\n            // Vykreslení grafu\n            this.renderInstancesComparisonChart(data);\n            this.loading = false;\n          },\n          error: error => {\n            console.error('Chyba při načítání srovnání instancí:', error);\n            this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi instancemi. ' + (error.message ? error.message : 'Zkuste to prosím znovu.');\n            this.loading = false;\n            // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n            if (error.status === 0) {\n              console.log('loadInstancesComparison - Server neodpovídá, zkusím to znovu za 5 sekund');\n              setTimeout(() => {\n                console.log('loadInstancesComparison - Opakuji volání API');\n                this.loadInstancesComparison();\n              }, 5000);\n            }\n          },\n          complete: () => {\n            console.log('loadInstancesComparison - Volání API dokončeno');\n          }\n        });\n        this.subscriptions.push(subscription);\n      } catch (error) {\n        console.error('loadInstancesComparison - Chyba při volání API:', error);\n        this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi instancemi. Zkuste to prosím znovu.';\n        this.loading = false;\n      }\n    }\n    /**\r\n     * Zpracování změny filtru pro srovnání instancí\r\n     */\n    onInstancesComparisonFilterChange(filterData) {\n      console.log('onInstancesComparisonFilterChange - Přijatá data filtru:', filterData);\n      // Kontrola, zda se filtr skutečně změnil\n      const currentFilterStr = JSON.stringify(this.instancesComparisonFilterData);\n      const newFilterStr = JSON.stringify(filterData);\n      // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n      // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n      if (currentFilterStr === newFilterStr) {\n        console.log('onInstancesComparisonFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n      }\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('onInstancesComparisonFilterChange - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n      if (filterData.period === 'custom') {\n        if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n          filterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (filterData.toDate && typeof filterData.toDate === 'string') {\n          filterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n      else if (filterData.fromDate && filterData.toDate) {\n        if (typeof filterData.fromDate === 'string') {\n          filterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (typeof filterData.toDate === 'string') {\n          filterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      this.instancesComparisonFilterData = filterData;\n      console.log('onInstancesComparisonFilterChange - Zpracovaná data filtru:', this.instancesComparisonFilterData);\n      this.loadInstancesComparison();\n    }\n    /**\r\n     * Načtení srovnání výkonu metod mezi verzemi\r\n     */\n    loadVersionsComparison() {\n      console.log('loadVersionsComparison - Začínám načítat data');\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('loadVersionsComparison - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      // Kontrola, zda máme načtené instance\n      if (!this.instances || this.instances.length === 0) {\n        console.warn('loadVersionsComparison - Instance nejsou načteny, načítám je');\n        // Načtení instancí\n        this.instanceService.getAll().subscribe({\n          next: data => {\n            console.log('loadVersionsComparison - Instance úspěšně načteny:', data);\n            this.instances = data;\n            // Po načtení instancí inicializujeme pole filtrů\n            this.initFilterFields();\n            // Pokračujeme v načítání dat\n            this.loadVersionsComparison();\n          },\n          error: error => {\n            console.error('loadVersionsComparison - Chyba při načítání instancí:', error);\n            this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n            this.loading = false;\n          }\n        });\n        return;\n      }\n      // Kontrola, zda je vybrána instance\n      if (!this.versionsComparisonFilterData.instanceId || this.versionsComparisonFilterData.instanceId === null || String(this.versionsComparisonFilterData.instanceId) === \"null\") {\n        console.warn('loadVersionsComparison - Není vybrána instance, přeskakuji načítání dat');\n        // Necháme validaci na formuláři, nezobrazujeme chybový blok\n        this.loading = false;\n        return;\n      }\n      // Příprava parametrů\n      const instanceId = this.versionsComparisonFilterData.instanceId;\n      const methodName = this.versionsComparisonFilterData.methodName;\n      const className = this.versionsComparisonFilterData.className;\n      let fromDate;\n      let toDate;\n      // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n      if (!this.versionsComparisonFilterData.period) {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n        toDate = today;\n      }\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      else if (this.versionsComparisonFilterData.period !== 'custom') {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - Number(this.versionsComparisonFilterData.period));\n        toDate = today;\n      }\n      // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n      else {\n        // Kontrola, zda fromDate a toDate jsou validní\n        if (this.versionsComparisonFilterData.fromDate) {\n          fromDate = new Date(this.versionsComparisonFilterData.fromDate);\n        } else {\n          // Výchozí hodnota, pokud fromDate není zadáno\n          fromDate = new Date();\n          fromDate.setDate(fromDate.getDate() - 7);\n        }\n        if (this.versionsComparisonFilterData.toDate) {\n          toDate = new Date(this.versionsComparisonFilterData.toDate);\n        } else {\n          // Výchozí hodnota, pokud toDate není zadáno\n          toDate = new Date();\n        }\n      }\n      try {\n        console.log('loadVersionsComparison - Volám API pro načtení dat', {\n          instanceId,\n          methodName,\n          className,\n          fromDate,\n          toDate\n        });\n        const subscription = this.performanceService.getVersionsComparison(instanceId, methodName, className, fromDate, toDate).subscribe({\n          next: data => {\n            console.log('loadVersionsComparison - Data úspěšně načtena:', data);\n            if (!data) {\n              console.error('loadVersionsComparison - API vrátilo prázdná data');\n              this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n              this.loading = false;\n              return;\n            }\n            // Uložení dat pro pozdější vykreslení\n            this.versionsComparisonData = data;\n            // Vykreslení grafu\n            this.renderVersionsComparisonChart(data);\n            this.loading = false;\n          },\n          error: error => {\n            console.error('Chyba při načítání srovnání verzí:', error);\n            this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi verzemi. ' + (error.message ? error.message : 'Zkuste to prosím znovu.');\n            this.loading = false;\n            // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n            if (error.status === 0) {\n              console.log('loadVersionsComparison - Server neodpovídá, zkusím to znovu za 5 sekund');\n              setTimeout(() => {\n                console.log('loadVersionsComparison - Opakuji volání API');\n                this.loadVersionsComparison();\n              }, 5000);\n            }\n          },\n          complete: () => {\n            console.log('loadVersionsComparison - Volání API dokončeno');\n          }\n        });\n        this.subscriptions.push(subscription);\n      } catch (error) {\n        console.error('loadVersionsComparison - Chyba při volání API:', error);\n        this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi verzemi. Zkuste to prosím znovu.';\n        this.loading = false;\n      }\n    }\n    /**\r\n     * Zpracování změny filtru pro srovnání verzí\r\n     */\n    onVersionsComparisonFilterChange(filterData) {\n      console.log('onVersionsComparisonFilterChange - Přijatá data filtru:', filterData);\n      // Kontrola, zda se filtr skutečně změnil\n      const currentFilterStr = JSON.stringify(this.versionsComparisonFilterData);\n      const newFilterStr = JSON.stringify(filterData);\n      // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n      // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n      if (currentFilterStr === newFilterStr) {\n        console.log('onVersionsComparisonFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n      }\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('onVersionsComparisonFilterChange - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      // Kontrola, zda je vybrána instance\n      if (filterData.instanceId === null || filterData.instanceId === undefined || String(filterData.instanceId) === \"null\") {\n        console.warn('onVersionsComparisonFilterChange - Není vybrána instance, přeskakuji načítání dat');\n        // Aktualizujeme data filtru, ale nevoláme načítání dat\n        this.versionsComparisonFilterData = filterData;\n        return;\n      }\n      // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n      if (filterData.period === 'custom') {\n        if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n          filterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (filterData.toDate && typeof filterData.toDate === 'string') {\n          filterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n      else if (filterData.fromDate && filterData.toDate) {\n        if (typeof filterData.fromDate === 'string') {\n          filterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (typeof filterData.toDate === 'string') {\n          filterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      this.versionsComparisonFilterData = filterData;\n      console.log('onVersionsComparisonFilterChange - Zpracovaná data filtru:', this.versionsComparisonFilterData);\n      this.loadVersionsComparison();\n    }\n    /**\r\n     * Načtení nejčastěji volaných metod\r\n     */\n    loadMostCalledMethods() {\n      console.log('loadMostCalledMethods - Začínám načítat data');\n      // Přidáme kontrolu, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('loadMostCalledMethods - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      // Nastavíme příznak načítání\n      this.loading = true;\n      // Kontrola, zda máme načtené instance\n      if (!this.instances || this.instances.length === 0) {\n        console.warn('loadMostCalledMethods - Instance nejsou načteny, načítám je');\n        // Načtení instancí\n        this.instanceService.getAll().subscribe({\n          next: data => {\n            console.log('loadMostCalledMethods - Instance úspěšně načteny:', data);\n            this.instances = data;\n            // Po načtení instancí inicializujeme pole filtrů\n            this.initFilterFields();\n            // Pokračujeme v načítání dat\n            this.loadMostCalledMethods();\n          },\n          error: error => {\n            console.error('loadMostCalledMethods - Chyba při načítání instancí:', error);\n            this.mostCalledMethods = [];\n            this.loading = false;\n          }\n        });\n        return;\n      }\n      // Příprava parametrů\n      // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n      let instanceId = this.aggregatedFilterData.instanceId;\n      if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n        instanceId = undefined;\n        console.log('loadMostCalledMethods - Použití undefined pro instanceId (všechny instance)');\n      }\n      let fromDate;\n      let toDate;\n      // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n      if (!this.aggregatedFilterData.period) {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n        toDate = today;\n      }\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      else if (this.aggregatedFilterData.period !== 'custom') {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n        toDate = today;\n      }\n      // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n      else {\n        // Kontrola, zda fromDate a toDate jsou validní\n        if (this.aggregatedFilterData.fromDate) {\n          fromDate = new Date(this.aggregatedFilterData.fromDate);\n        } else {\n          // Výchozí hodnota, pokud fromDate není zadáno\n          fromDate = new Date();\n          fromDate.setDate(fromDate.getDate() - 7);\n        }\n        if (this.aggregatedFilterData.toDate) {\n          toDate = new Date(this.aggregatedFilterData.toDate);\n        } else {\n          // Výchozí hodnota, pokud toDate není zadáno\n          toDate = new Date();\n        }\n      }\n      try {\n        console.log('loadMostCalledMethods - Volám API pro načtení dat', {\n          instanceId,\n          fromDate,\n          toDate\n        });\n        const subscription = this.performanceService.getMostCalledMethods(instanceId, fromDate, toDate, 10).subscribe({\n          next: data => {\n            console.log('loadMostCalledMethods - Data úspěšně načtena:', data);\n            if (!data) {\n              console.error('loadMostCalledMethods - API vrátilo prázdná data');\n              this.mostCalledMethods = [];\n              this.loading = false;\n              return;\n            }\n            this.mostCalledMethods = data;\n            // Aplikujeme řazení na načtená data\n            this.applySortToMethods();\n            this.loading = false;\n          },\n          error: error => {\n            console.error('Chyba při načítání nejčastěji volaných metod:', error);\n            this.mostCalledMethods = [];\n            this.loading = false;\n            // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n            if (error.status === 0) {\n              console.log('loadMostCalledMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n              setTimeout(() => {\n                console.log('loadMostCalledMethods - Opakuji volání API');\n                this.loadMostCalledMethods();\n              }, 5000);\n            }\n          },\n          complete: () => {\n            console.log('loadMostCalledMethods - Volání API dokončeno');\n          }\n        });\n        this.subscriptions.push(subscription);\n      } catch (error) {\n        console.error('loadMostCalledMethods - Chyba při volání API:', error);\n        this.mostCalledMethods = [];\n        this.loading = false;\n      }\n    }\n    /**\r\n     * Načtení nejpomalejších metod\r\n     */\n    loadSlowestMethods() {\n      console.log('loadSlowestMethods - Začínám načítat data');\n      // Přidáme kontrolu, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('loadSlowestMethods - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      // Nastavíme příznak načítání\n      this.loading = true;\n      // Kontrola, zda máme načtené instance\n      if (!this.instances || this.instances.length === 0) {\n        console.warn('loadSlowestMethods - Instance nejsou načteny, načítám je');\n        // Načtení instancí\n        this.instanceService.getAll().subscribe({\n          next: data => {\n            console.log('loadSlowestMethods - Instance úspěšně načteny:', data);\n            this.instances = data;\n            // Po načtení instancí inicializujeme pole filtrů\n            this.initFilterFields();\n            // Pokračujeme v načítání dat\n            this.loadSlowestMethods();\n          },\n          error: error => {\n            console.error('loadSlowestMethods - Chyba při načítání instancí:', error);\n            this.slowestMethods = [];\n          }\n        });\n        return;\n      }\n      // Příprava parametrů\n      // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n      let instanceId = this.aggregatedFilterData.instanceId;\n      if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n        instanceId = undefined;\n        console.log('loadSlowestMethods - Použití undefined pro instanceId (všechny instance)');\n      }\n      let fromDate;\n      let toDate;\n      // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n      if (!this.aggregatedFilterData.period) {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n        toDate = today;\n      }\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      else if (this.aggregatedFilterData.period !== 'custom') {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n        toDate = today;\n      }\n      // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n      else {\n        // Kontrola, zda fromDate a toDate jsou validní\n        if (this.aggregatedFilterData.fromDate) {\n          fromDate = new Date(this.aggregatedFilterData.fromDate);\n        } else {\n          // Výchozí hodnota, pokud fromDate není zadáno\n          fromDate = new Date();\n          fromDate.setDate(fromDate.getDate() - 7);\n        }\n        if (this.aggregatedFilterData.toDate) {\n          toDate = new Date(this.aggregatedFilterData.toDate);\n        } else {\n          // Výchozí hodnota, pokud toDate není zadáno\n          toDate = new Date();\n        }\n      }\n      try {\n        console.log('loadSlowestMethods - Volám API pro načtení dat', {\n          instanceId,\n          fromDate,\n          toDate\n        });\n        const subscription = this.performanceService.getSlowestMethods(instanceId, fromDate, toDate, 10).subscribe({\n          next: data => {\n            console.log('loadSlowestMethods - Data úspěšně načtena:', data);\n            if (!data) {\n              console.error('loadSlowestMethods - API vrátilo prázdná data');\n              this.slowestMethods = [];\n              this.loading = false;\n              return;\n            }\n            this.slowestMethods = data;\n            // Aplikujeme řazení na načtená data\n            this.applySortToMethods();\n            this.loading = false;\n          },\n          error: error => {\n            console.error('Chyba při načítání nejpomalejších metod:', error);\n            this.slowestMethods = [];\n            this.loading = false;\n            // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n            if (error.status === 0) {\n              console.log('loadSlowestMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n              setTimeout(() => {\n                console.log('loadSlowestMethods - Opakuji volání API');\n                this.loadSlowestMethods();\n              }, 5000);\n            }\n          },\n          complete: () => {\n            console.log('loadSlowestMethods - Volání API dokončeno');\n          }\n        });\n        this.subscriptions.push(subscription);\n      } catch (error) {\n        console.error('loadSlowestMethods - Chyba při volání API:', error);\n        this.slowestMethods = [];\n        this.loading = false;\n      }\n    }\n    /**\r\n     * Načtení všech metod\r\n     */\n    loadAllMethods() {\n      // Příprava parametrů\n      // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n      let instanceId = this.aggregatedFilterData.instanceId;\n      if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n        instanceId = undefined;\n        console.log('loadAllMethods - Použití undefined pro instanceId (všechny instance)');\n      }\n      let fromDate;\n      let toDate;\n      // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n      if (!this.aggregatedFilterData.period) {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n        toDate = today;\n      }\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      else if (this.aggregatedFilterData.period !== 'custom') {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n        toDate = today;\n      }\n      // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n      else {\n        // Kontrola, zda fromDate a toDate jsou validní\n        if (this.aggregatedFilterData.fromDate) {\n          fromDate = new Date(this.aggregatedFilterData.fromDate);\n        } else {\n          // Výchozí hodnota, pokud fromDate není zadáno\n          fromDate = new Date();\n          fromDate.setDate(fromDate.getDate() - 7);\n        }\n        if (this.aggregatedFilterData.toDate) {\n          toDate = new Date(this.aggregatedFilterData.toDate);\n        } else {\n          // Výchozí hodnota, pokud toDate není zadáno\n          toDate = new Date();\n        }\n      }\n      try {\n        console.log('loadAllMethods - Volám API pro načtení dat', {\n          instanceId,\n          fromDate,\n          toDate\n        });\n        const subscription = this.performanceService.getAllMethods(instanceId, fromDate, toDate).subscribe({\n          next: data => {\n            console.log('loadAllMethods - Data úspěšně načtena:', data);\n            if (!data) {\n              console.error('loadAllMethods - API vrátilo prázdná data');\n              this.allMethods = [];\n              return;\n            }\n            this.allMethods = data;\n            // Aplikujeme řazení na načtená data\n            this.applySortToMethods();\n          },\n          error: error => {\n            console.error('Chyba při načítání všech metod:', error);\n            this.allMethods = [];\n            // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n            if (error.status === 0) {\n              console.log('loadAllMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n              setTimeout(() => {\n                console.log('loadAllMethods - Opakuji volání API');\n                this.loadAllMethods();\n              }, 5000);\n            }\n          },\n          complete: () => {\n            console.log('loadAllMethods - Volání API dokončeno');\n          }\n        });\n        this.subscriptions.push(subscription);\n      } catch (error) {\n        console.error('loadAllMethods - Chyba při volání API:', error);\n        this.allMethods = [];\n      }\n    }\n    /**\r\n     * Načtení detailu metody\r\n     */\n    loadMethodDetail() {\n      console.log('loadMethodDetail - Začínám načítat data');\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('loadMethodDetail - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      // Kontrola, zda máme vybranou metodu nebo zadané povinné parametry\n      if (!this.selectedMethod && (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName)) {\n        console.warn('loadMethodDetail - Není vybrána metoda a nejsou zadány povinné parametry');\n        // Necháme validaci na formuláři, nezobrazujeme chybový blok\n        this.loading = false;\n        return;\n      }\n      // Pokud máme vybranou metodu, ale nemáme zadané parametry, použijeme parametry z vybrané metody\n      if (this.selectedMethod && (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName)) {\n        console.log('loadMethodDetail - Použití parametrů z vybrané metody');\n        this.methodDetailFilterData.className = this.selectedMethod.className;\n        this.methodDetailFilterData.methodName = this.selectedMethod.methodName;\n        // Zjištění aktuální hodnoty instanceId\n        let instanceId = this.selectedMethod.instanceId;\n        // Pokud instanceId je null nebo undefined, použijeme 0 (všechny instance)\n        if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n          instanceId = 0;\n        }\n        // Nastavení instanceId\n        this.methodDetailFilterData.instanceId = instanceId;\n        // Uložení filtru do localStorage\n        this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n      }\n      // Kontrola, zda máme načtené instance\n      if (!this.instances || this.instances.length === 0) {\n        console.warn('loadMethodDetail - Instance nejsou načteny, načítám je');\n        // Načtení instancí\n        this.instanceService.getAll().subscribe({\n          next: data => {\n            console.log('loadMethodDetail - Instance úspěšně načteny:', data);\n            this.instances = data;\n            // Po načtení instancí inicializujeme pole filtrů\n            this.initFilterFields();\n            // Pokračujeme v načítání dat\n            this.loadMethodDetail();\n          },\n          error: error => {\n            console.error('loadMethodDetail - Chyba při načítání instancí:', error);\n            this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n            this.loading = false;\n          }\n        });\n        return;\n      }\n      // Kontrola, zda jsou vyplněny všechny povinné parametry\n      if (!this.methodDetailFilterData.instanceId || !this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName) {\n        // Necháme validaci na formuláři, nezobrazujeme chybový blok\n        this.loading = false;\n        return;\n      }\n      // Příprava parametrů\n      // Pro metodu getMethodDetail potřebujeme instanceId jako number, null nebo 0\n      let instanceId = this.methodDetailFilterData.instanceId ?? null;\n      // Pokud instanceId je null, undefined nebo řetězec \"null\", použijeme 0 (všechny instance)\n      if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n        instanceId = 0; // 0 = všechny instance\n        console.log('loadMethodDetail - Použití hodnoty 0 pro instanceId (všechny instance)');\n        // Aktualizujeme hodnotu v objektu filtru\n        this.methodDetailFilterData.instanceId = 0;\n      }\n      console.log('loadMethodDetail - Finální hodnota instanceId:', instanceId);\n      const className = this.methodDetailFilterData.className;\n      const methodName = this.methodDetailFilterData.methodName;\n      let fromDate;\n      let toDate;\n      // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n      if (!this.methodDetailFilterData.period) {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n        toDate = today;\n      }\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      else if (this.methodDetailFilterData.period !== 'custom') {\n        const today = new Date();\n        fromDate = new Date();\n        fromDate.setDate(today.getDate() - Number(this.methodDetailFilterData.period));\n        toDate = today;\n      }\n      // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n      else {\n        // Kontrola, zda fromDate a toDate jsou validní\n        if (this.methodDetailFilterData.fromDate) {\n          fromDate = new Date(this.methodDetailFilterData.fromDate);\n        } else {\n          // Výchozí hodnota, pokud fromDate není zadáno\n          fromDate = new Date();\n          fromDate.setDate(fromDate.getDate() - 7);\n        }\n        if (this.methodDetailFilterData.toDate) {\n          toDate = new Date(this.methodDetailFilterData.toDate);\n        } else {\n          // Výchozí hodnota, pokud toDate není zadáno\n          toDate = new Date();\n        }\n      }\n      try {\n        console.log('loadMethodDetail - Volám API pro načtení dat', {\n          instanceId,\n          className,\n          methodName,\n          fromDate,\n          toDate\n        });\n        // Kontrola, zda jsou všechny povinné parametry nastaveny\n        if (!className || !methodName) {\n          console.error('loadMethodDetail - Nejsou zadány povinné parametry (třída nebo metoda)');\n          this.error = 'Pro zobrazení detailu metody je nutné zadat název třídy a metody.';\n          this.loading = false;\n          return;\n        }\n        // Pokud instanceId je 0, použijeme null, aby se parametr instanceId vůbec neodeslal\n        const finalInstanceId = instanceId === 0 ? null : instanceId;\n        const subscription = this.performanceService.getMethodDetail(finalInstanceId, className, methodName, fromDate, toDate).subscribe({\n          next: data => {\n            console.log('loadMethodDetail - Data úspěšně načtena:', data);\n            if (!data) {\n              console.error('loadMethodDetail - API vrátilo prázdná data');\n              this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n              this.loading = false;\n              return;\n            }\n            // Aktualizace hodnot filtru podle načtených dat\n            // Tím zajistíme, že hodnoty ve filtru budou odpovídat načteným datům\n            if (data.className && data.methodName) {\n              console.log('loadMethodDetail - Aktualizuji hodnoty filtru podle načtených dat:', {\n                className: data.className,\n                methodName: data.methodName\n              });\n              this.methodDetailFilterData.className = data.className;\n              this.methodDetailFilterData.methodName = data.methodName;\n              // Uložení filtru do localStorage\n              this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n            }\n            this.methodDetailData = data;\n            this.methodDetailChartData = data;\n            // Kontrola, zda jsou data prázdná\n            const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0;\n            this.hasMethodDetailChartData = hasData;\n            // Vykreslení grafu\n            this.renderMethodDetailChart(data);\n            this.loading = false;\n          },\n          error: error => {\n            console.error('Chyba při načítání detailu metody:', error);\n            this.error = 'Nepodařilo se načíst detail metody. ' + (error.message ? error.message : 'Zkuste to prosím znovu.');\n            this.loading = false;\n            // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n            if (error.status === 0) {\n              console.log('loadMethodDetail - Server neodpovídá, zkusím to znovu za 5 sekund');\n              setTimeout(() => {\n                console.log('loadMethodDetail - Opakuji volání API');\n                this.loadMethodDetail();\n              }, 5000);\n            }\n          },\n          complete: () => {\n            console.log('loadMethodDetail - Volání API dokončeno');\n          }\n        });\n        this.subscriptions.push(subscription);\n      } catch (error) {\n        console.error('loadMethodDetail - Chyba při volání API:', error);\n        this.error = 'Nepodařilo se načíst detail metody. Zkuste to prosím znovu.';\n        this.loading = false;\n      }\n    }\n    /**\r\n     * Načtení dat podle vybraného typu metod\r\n     */\n    loadMethodsByType() {\n      console.log('loadMethodsByType - Načítám data podle typu:', this.methodsDisplayType);\n      switch (this.methodsDisplayType) {\n        case 'slowest':\n          this.loadSlowestMethods();\n          break;\n        case 'most-called':\n          this.loadMostCalledMethods();\n          break;\n        case 'all':\n          this.loadAllMethods();\n          break;\n        default:\n          this.loadSlowestMethods();\n          break;\n      }\n    }\n    /**\r\n     * Zpracování změny filtru pro metody\r\n     */\n    onMethodsFilterChange(filterData) {\n      console.log('onMethodsFilterChange - Přijatá data filtru:', filterData);\n      // Kontrola, zda se filtr skutečně změnil\n      const currentFilterStr = JSON.stringify(this.aggregatedFilterData);\n      const newFilterStr = JSON.stringify(filterData);\n      // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n      // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n      if (currentFilterStr === newFilterStr) {\n        console.log('onMethodsFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n      }\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('onMethodsFilterChange - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n      if (filterData.period === 'custom') {\n        if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n          filterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (filterData.toDate && typeof filterData.toDate === 'string') {\n          filterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n      else if (filterData.fromDate && filterData.toDate) {\n        if (typeof filterData.fromDate === 'string') {\n          filterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (typeof filterData.toDate === 'string') {\n          filterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      // Aktualizace typu zobrazovaných metod\n      if (filterData.methodsDisplayType) {\n        this.methodsDisplayType = filterData.methodsDisplayType;\n      } else {\n        // Výchozí hodnota, pokud není zadána\n        this.methodsDisplayType = 'slowest';\n        filterData.methodsDisplayType = 'slowest';\n      }\n      this.aggregatedFilterData = filterData;\n      console.log('onMethodsFilterChange - Zpracovaná data filtru:', this.aggregatedFilterData);\n      this.loadMethodsByType();\n      // Reinicializace popoverů po změně typu zobrazovaných metod\n      setTimeout(() => {\n        this.initPopovers();\n      }, 500);\n    }\n    /**\r\n     * Zpracování změny filtru pro detail metody\r\n     */\n    onMethodDetailFilterChange(filterData) {\n      console.log('onMethodDetailFilterChange - Přijatá data filtru:', filterData);\n      // Kontrola, zda již neprobíhá načítání dat\n      if (this.loading) {\n        console.warn('onMethodDetailFilterChange - Načítání dat již probíhá, přeskakuji');\n        return;\n      }\n      // Aktualizujeme data filtru\n      this.methodDetailFilterData = {\n        ...filterData\n      };\n      // Kontrola, zda je hodnota instanceId nastavena\n      if (filterData.instanceId === null || filterData.instanceId === undefined || String(filterData.instanceId) === \"null\") {\n        console.log('onMethodDetailFilterChange - Hodnota instanceId není nastavena, použijeme 0 (všechny instance)');\n        this.methodDetailFilterData.instanceId = 0;\n      } else {\n        console.log('onMethodDetailFilterChange - Hodnota instanceId je nastavena:', filterData.instanceId);\n      }\n      // Pokud se změnil filtr, resetujeme vybranou metodu, aby se použily hodnoty z filtru\n      if (this.selectedMethod) {\n        if (this.selectedMethod.className !== this.methodDetailFilterData.className || this.selectedMethod.methodName !== this.methodDetailFilterData.methodName || this.selectedMethod.instanceId !== this.methodDetailFilterData.instanceId) {\n          console.log('onMethodDetailFilterChange - Resetuji vybranou metodu, protože se změnil filtr');\n          this.selectedMethod = null;\n        }\n      }\n      // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n      if (filterData.period === 'custom') {\n        if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n          this.methodDetailFilterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (filterData.toDate && typeof filterData.toDate === 'string') {\n          this.methodDetailFilterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n      else if (filterData.fromDate && filterData.toDate) {\n        if (typeof filterData.fromDate === 'string') {\n          this.methodDetailFilterData.fromDate = new Date(filterData.fromDate);\n        }\n        if (typeof filterData.toDate === 'string') {\n          this.methodDetailFilterData.toDate = new Date(filterData.toDate);\n        }\n      }\n      console.log('onMethodDetailFilterChange - Zpracovaná data filtru:', this.methodDetailFilterData);\n      // Kontrola, zda jsou vyplněny povinné parametry\n      if (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName) {\n        console.warn('onMethodDetailFilterChange - Nejsou vyplněny povinné parametry (třída nebo metoda), přeskakuji načítání dat');\n        return;\n      }\n      // Kontrola, zda se filtr skutečně změnil\n      const currentFilterStr = JSON.stringify(this.methodDetailFilterData);\n      const newFilterStr = JSON.stringify(filterData);\n      // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n      // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n      if (currentFilterStr === newFilterStr) {\n        console.log('onMethodDetailFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n      } else {\n        // Pokud se filtr změnil, uložíme ho do localStorage\n        this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n      }\n      // Resetujeme indikátor, zda jsou k dispozici data pro graf\n      this.hasMethodDetailChartData = true;\n      // Vždy načteme data a překreslíme graf\n      this.loadMethodDetail();\n    }\n    /**\r\n     * Zobrazení detailu metody\r\n     */\n    showMethodDetail(method) {\n      console.log('showMethodDetail - Zobrazuji detail metody:', method);\n      this.selectedMethod = method;\n      // Zjištění aktuální hodnoty instanceId z aktivního filtru podle záložky\n      let instanceId = method.instanceId;\n      // Pro záložky \"aggregated\" a \"methods\" použij hodnotu z this.aggregatedFilterData.instanceId\n      if (this.activeTab === 'aggregated' || this.activeTab === 'methods') {\n        if (this.aggregatedFilterData && this.aggregatedFilterData.instanceId !== undefined) {\n          instanceId = this.aggregatedFilterData.instanceId;\n          // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme 0 (všechny instance)\n          if (instanceId === null || String(instanceId) === \"null\") {\n            instanceId = 0;\n          }\n        }\n      }\n      // Pro záložku \"versions-comparison\" použij hodnotu z this.versionsComparisonFilterData.instanceId\n      else if (this.activeTab === 'versions-comparison') {\n        if (this.versionsComparisonFilterData && this.versionsComparisonFilterData.instanceId !== undefined) {\n          instanceId = this.versionsComparisonFilterData.instanceId;\n          // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme undefined\n          if (instanceId === null || String(instanceId) === \"null\") {\n            instanceId = undefined;\n          }\n        }\n      }\n      // Pro záložku \"instances-comparison\" a ostatní záložky použij hodnotu z method.instanceId\n      // Toto je již nastaveno výchozí hodnotou instanceId = method.instanceId\n      // Nastavení hodnot filtru\n      // Pro metodu getMethodDetail potřebujeme instanceId jako number nebo null\n      // Pokud instanceId je undefined, použijeme hodnotu z metody\n      if (instanceId === undefined) {\n        instanceId = method.instanceId;\n      }\n      // Pokud instanceId je null, nastavíme ho na 0 (všechny instance)\n      if (instanceId === null) {\n        instanceId = 0;\n      }\n      console.log('showMethodDetail - Nastavuji hodnoty filtru:', {\n        instanceId: instanceId,\n        className: method.className,\n        methodName: method.methodName\n      });\n      // Aktualizace hodnot filtru\n      this.methodDetailFilterData = {\n        ...this.methodDetailFilterData,\n        instanceId: instanceId,\n        className: method.className,\n        methodName: method.methodName\n      };\n      // Uložení filtru do localStorage, aby byl dostupný i po obnovení stránky\n      this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n      // Resetujeme indikátor, zda jsou k dispozici data pro graf\n      this.hasMethodDetailChartData = true;\n      // Přepnutí na záložku s detailem metody\n      this.activeTab = 'method-detail';\n      // Uložení aktivní záložky do localStorage\n      this.saveActiveTab('method-detail');\n      // Načtení detailu metody\n      this.loadMethodDetail();\n    }\n    /**\r\n     * Uložení nastavení viditelnosti metrik do localStorage\r\n     */\n    saveMetricsVisibility(chartType, datasets) {\n      try {\n        const visibility = {};\n        // Procházíme všechny datasety a ukládáme jejich viditelnost\n        datasets.forEach(dataset => {\n          if (dataset.label) {\n            // Použijeme metodu isDatasetVisible z Chart.js, pokud je k dispozici\n            if (this.aggregatedPerformanceChart && chartType === 'aggregated') {\n              const index = datasets.indexOf(dataset);\n              visibility[dataset.label] = this.aggregatedPerformanceChart.isDatasetVisible(index);\n            } else if (this.instancesComparisonChart && chartType === 'instances-comparison') {\n              const index = datasets.indexOf(dataset);\n              visibility[dataset.label] = this.instancesComparisonChart.isDatasetVisible(index);\n            } else if (this.versionsComparisonChart && chartType === 'versions-comparison') {\n              const index = datasets.indexOf(dataset);\n              visibility[dataset.label] = this.versionsComparisonChart.isDatasetVisible(index);\n            } else if (this.methodDetailChart && chartType === 'method-detail') {\n              const index = datasets.indexOf(dataset);\n              visibility[dataset.label] = this.methodDetailChart.isDatasetVisible(index);\n            } else {\n              // Fallback na původní logiku\n              visibility[dataset.label] = !dataset.hidden;\n            }\n          }\n        });\n        // Uložení do localStorage\n        localStorage.setItem(`metrics_visibility_${chartType}`, JSON.stringify(visibility));\n        console.log(`Nastavení viditelnosti metrik pro ${chartType} uloženo:`, visibility);\n      } catch (error) {\n        console.error(`Chyba při ukládání nastavení viditelnosti metrik pro ${chartType}:`, error);\n      }\n    }\n    /**\r\n     * Uložení aktivní záložky do localStorage\r\n     */\n    saveActiveTab(tabId) {\n      try {\n        localStorage.setItem('performance_active_tab', tabId);\n        console.log(`Uložena aktivní záložka: ${tabId}`);\n      } catch (error) {\n        console.error(`Chyba při ukládání aktivní záložky:`, error);\n      }\n    }\n    /**\r\n     * Načtení aktivní záložky z localStorage\r\n     */\n    loadActiveTab() {\n      try {\n        const activeTab = localStorage.getItem('performance_active_tab');\n        console.log(`Načtena aktivní záložka z localStorage: ${activeTab}`);\n        return activeTab;\n      } catch (error) {\n        console.error(`Chyba při načítání aktivní záložky:`, error);\n        return null;\n      }\n    }\n    /**\r\n     * Uložení filtru do localStorage\r\n     * @param entityType Typ entity, pro kterou se ukládá filtr\r\n     * @param filterData Data filtru\r\n     */\n    saveFilter(entityType, filterData) {\n      try {\n        const lastFilterKey = `last_filter_${entityType}`;\n        localStorage.setItem(lastFilterKey, JSON.stringify(filterData));\n        console.log(`Uložen filtr pro ${entityType} do localStorage:`, filterData);\n      } catch (error) {\n        console.error(`Chyba při ukládání filtru pro ${entityType} do localStorage:`, error);\n      }\n    }\n    /**\r\n     * Zpracování kliknutí na záhlaví sloupce pro řazení\r\n     * @param column Název sloupce, podle kterého se má řadit\r\n     */\n    onSort(column) {\n      console.log(`onSort - Řazení podle sloupce: ${column}`);\n      const newSortingState = this.tableSortingService.createNewSortingStateWithDefaults(this.sortColumn, this.sortDirection, column, ['totalCount', 'min', 'max', 'avg', 'median', 'percentil95'] // číselné sloupce\n      );\n      // Aktualizace lokálního stavu\n      this.sortColumn = newSortingState.column;\n      this.sortDirection = newSortingState.direction;\n      // Uložení do localStorage podle aktivní záložky\n      const tableName = this.activeTab === 'methods' ? 'methods' : 'methods';\n      this.tableSortingService.saveSortingState('performance', tableName, newSortingState);\n      console.log(`onSort - Nové řazení: ${this.sortColumn} ${this.sortDirection}`);\n      // Aplikujeme řazení na data\n      this.applySortToMethods();\n    }\n    /**\r\n     * Aplikace řazení na data metod\r\n     */\n    applySortToMethods() {\n      console.log(`applySortToMethods - Aplikuji řazení: ${this.sortColumn} ${this.sortDirection}`);\n      // Řazení nejpomalejších metod\n      if (this.slowestMethods && this.slowestMethods.length > 0) {\n        this.slowestMethods = this.sortMethods([...this.slowestMethods]);\n      }\n      // Řazení nejčastěji volaných metod\n      if (this.mostCalledMethods && this.mostCalledMethods.length > 0) {\n        this.mostCalledMethods = this.sortMethods([...this.mostCalledMethods]);\n      }\n      // Řazení všech metod\n      if (this.allMethods && this.allMethods.length > 0) {\n        this.allMethods = this.sortMethods([...this.allMethods]);\n      }\n    }\n    /**\r\n     * Řazení pole metod podle aktuálního sloupce a směru\r\n     * @param methods Pole metod k seřazení\r\n     * @returns Seřazené pole metod\r\n     */\n    sortMethods(methods) {\n      return methods.sort((a, b) => {\n        let valueA;\n        let valueB;\n        // Získání hodnot pro porovnání podle názvu sloupce\n        switch (this.sortColumn) {\n          case 'instanceName':\n            valueA = a.instanceName?.toLowerCase() || '';\n            valueB = b.instanceName?.toLowerCase() || '';\n            break;\n          case 'className':\n            valueA = a.className?.toLowerCase() || '';\n            valueB = b.className?.toLowerCase() || '';\n            break;\n          case 'methodName':\n            valueA = a.methodName?.toLowerCase() || '';\n            valueB = b.methodName?.toLowerCase() || '';\n            break;\n          case 'totalCount':\n            valueA = a.totalCount || 0;\n            valueB = b.totalCount || 0;\n            break;\n          case 'min':\n            valueA = a.min || 0;\n            valueB = b.min || 0;\n            break;\n          case 'max':\n            valueA = a.max || 0;\n            valueB = b.max || 0;\n            break;\n          case 'avg':\n            valueA = a.avg || 0;\n            valueB = b.avg || 0;\n            break;\n          case 'median':\n            valueA = a.median || 0;\n            valueB = b.median || 0;\n            break;\n          case 'percentil95':\n            valueA = a.percentil95 || 0;\n            valueB = b.percentil95 || 0;\n            break;\n          default:\n            valueA = 0;\n            valueB = 0;\n        }\n        // Porovnání hodnot podle směru řazení\n        if (this.sortDirection === 'asc') {\n          if (typeof valueA === 'string') {\n            return valueA.localeCompare(valueB);\n          } else {\n            return valueA - valueB;\n          }\n        } else {\n          if (typeof valueA === 'string') {\n            return valueB.localeCompare(valueA);\n          } else {\n            return valueB - valueA;\n          }\n        }\n      });\n    }\n    /**\r\n     * Načtení stavu třídění z localStorage\r\n     */\n    loadSortingState() {\n      const defaultSort = {\n        column: 'avg',\n        direction: 'desc'\n      };\n      const savedSortingState = this.tableSortingService.loadSortingState('performance', 'methods', defaultSort);\n      if (savedSortingState) {\n        this.sortColumn = savedSortingState.column;\n        this.sortDirection = savedSortingState.direction;\n      }\n    }\n    /**\r\n     * Načtení nastavení viditelnosti metrik z localStorage\r\n     */\n    loadMetricsVisibility(chartType) {\n      try {\n        const visibilityJson = localStorage.getItem(`metrics_visibility_${chartType}`);\n        if (visibilityJson) {\n          const visibility = JSON.parse(visibilityJson);\n          console.log(`Načteno nastavení viditelnosti metrik pro ${chartType}:`, visibility);\n          return visibility;\n        }\n      } catch (error) {\n        console.error(`Chyba při načítání nastavení viditelnosti metrik pro ${chartType}:`, error);\n      }\n      return null;\n    }\n    /**\r\n     * Vykreslení grafu agregovaných výkonnostních metrik\r\n     */\n    renderAggregatedPerformanceChart(data) {\n      console.log('renderAggregatedPerformanceChart - data:', data);\n      // Nastavíme příznak, že se graf vykresluje\n      this.renderingAggregatedChart = true;\n      // Uložíme data pro pozdější vykreslení\n      this.aggregatedPerformanceData = data;\n      // Příprava dat pro graf\n      const chartData = this.prepareAggregatedPerformanceChartData(data);\n      console.log('renderAggregatedPerformanceChart - chartData:', chartData);\n      // Kontrola, zda jsou data prázdná\n      const hasData = chartData.labels && Array.isArray(chartData.labels) && chartData.labels.length > 0;\n      console.log('renderAggregatedPerformanceChart - hasData:', hasData, 'labels:', chartData.labels);\n      if (!hasData) {\n        // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n        this.hasAggregatedChartData = false;\n        this.renderingAggregatedChart = false;\n        // Zničíme existující graf, pokud existuje\n        if (this.aggregatedPerformanceChart) {\n          this.aggregatedPerformanceChart.destroy();\n          this.aggregatedPerformanceChart = null;\n        }\n        return;\n      }\n      // Pokud máme data, nastavíme indikátor\n      this.hasAggregatedChartData = true;\n      // Kontrola, zda je reference na canvas element k dispozici\n      if (!this.aggregatedPerformanceChartRef) {\n        console.warn('renderAggregatedPerformanceChart - Reference na canvas element není k dispozici, zkusím později');\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderAggregatedPerformanceChart - Zkouším znovu po delším timeoutu (2000ms)');\n          if (this.aggregatedPerformanceChartRef) {\n            this.renderAggregatedPerformanceChart(data);\n          } else {\n            console.error('renderAggregatedPerformanceChart - Canvas element stále není k dispozici po 2000ms!');\n            this.renderingAggregatedChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n          }\n        }, 2000);\n        return;\n      }\n      console.log('renderAggregatedPerformanceChart - Reference na canvas element je k dispozici');\n      try {\n        // Kontrola, zda je canvas element v DOM\n        if (!document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n          console.warn('renderAggregatedPerformanceChart - Canvas element není v DOM, zkusím později');\n          // Zkusíme počkat a zkusit to znovu po delším timeoutu\n          setTimeout(() => {\n            console.log('renderAggregatedPerformanceChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n            if (this.aggregatedPerformanceChartRef && document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n              this.renderAggregatedPerformanceChart(data);\n            } else {\n              console.error('renderAggregatedPerformanceChart - Canvas element stále není v DOM po 2000ms!');\n            }\n          }, 2000);\n          return;\n        }\n        // Získání kontextu canvas elementu\n        const ctx = this.aggregatedPerformanceChartRef.nativeElement.getContext('2d');\n        if (!ctx) {\n          console.error('renderAggregatedPerformanceChart - Nepodařilo se získat kontext canvas elementu!');\n          return;\n        }\n        console.log('renderAggregatedPerformanceChart - Kontext canvas elementu je k dispozici');\n        if (this.aggregatedPerformanceChart) {\n          console.log('renderAggregatedPerformanceChart - Ničím existující graf');\n          this.aggregatedPerformanceChart.destroy();\n        }\n        // Kontrola, zda jsou všechna pole dat stejné délky\n        const labelsLength = chartData.labels.length;\n        const avgDataLength = chartData.avgData.length;\n        const medianDataLength = chartData.medianData.length;\n        const p95DataLength = chartData.p95Data.length;\n        const p99DataLength = chartData.p99Data.length;\n        const minDataLength = chartData.minData.length;\n        const maxDataLength = chartData.maxData.length;\n        const totalCountDataLength = chartData.totalCountData.length;\n        console.log('renderAggregatedPerformanceChart - Délky polí:', {\n          labelsLength,\n          avgDataLength,\n          medianDataLength,\n          p95DataLength,\n          p99DataLength,\n          minDataLength,\n          maxDataLength,\n          totalCountDataLength\n        });\n        // Pokud některé pole má jinou délku než labels, upravíme ho\n        if (avgDataLength !== labelsLength) {\n          console.warn('renderAggregatedPerformanceChart - avgData má jinou délku než labels, upravuji');\n          chartData.avgData = this.adjustArrayLength(chartData.avgData, labelsLength);\n        }\n        if (medianDataLength !== labelsLength) {\n          console.warn('renderAggregatedPerformanceChart - medianData má jinou délku než labels, upravuji');\n          chartData.medianData = this.adjustArrayLength(chartData.medianData, labelsLength);\n        }\n        if (p95DataLength !== labelsLength) {\n          console.warn('renderAggregatedPerformanceChart - p95Data má jinou délku než labels, upravuji');\n          chartData.p95Data = this.adjustArrayLength(chartData.p95Data, labelsLength);\n        }\n        if (p99DataLength !== labelsLength) {\n          console.warn('renderAggregatedPerformanceChart - p99Data má jinou délku než labels, upravuji');\n          chartData.p99Data = this.adjustArrayLength(chartData.p99Data, labelsLength);\n        }\n        if (minDataLength !== labelsLength) {\n          console.warn('renderAggregatedPerformanceChart - minData má jinou délku než labels, upravuji');\n          chartData.minData = this.adjustArrayLength(chartData.minData, labelsLength);\n        }\n        if (maxDataLength !== labelsLength) {\n          console.warn('renderAggregatedPerformanceChart - maxData má jinou délku než labels, upravuji');\n          chartData.maxData = this.adjustArrayLength(chartData.maxData, labelsLength);\n        }\n        if (totalCountDataLength !== labelsLength) {\n          console.warn('renderAggregatedPerformanceChart - totalCountData má jinou délku než labels, upravuji');\n          chartData.totalCountData = this.adjustArrayLength(chartData.totalCountData, labelsLength);\n        }\n        // Pokud jsou data k dispozici, vykreslíme graf\n        try {\n          console.log('renderAggregatedPerformanceChart - Vytvářím nový graf');\n          // Kontrola, zda je canvas element stále platný\n          if (!this.aggregatedPerformanceChartRef || !document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n            console.error('renderAggregatedPerformanceChart - Canvas element již není platný!');\n            return;\n          }\n          // Kontrola, zda má canvas element rozměry\n          const width = this.aggregatedPerformanceChartRef.nativeElement.width;\n          const height = this.aggregatedPerformanceChartRef.nativeElement.height;\n          console.log('renderAggregatedPerformanceChart - Canvas rozměry:', {\n            width,\n            height\n          });\n          // Načtení nastavení viditelnosti metrik z localStorage\n          const visibility = this.loadMetricsVisibility('aggregated');\n          // Vytvoření datasetů s nastavením viditelnosti\n          const datasets = [{\n            label: 'Průměr (ms)',\n            data: chartData.avgData,\n            borderColor: 'rgba(54, 162, 235, 1)',\n            backgroundColor: 'rgba(54, 162, 235, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['Průměr (ms)'] : false\n          }, {\n            label: 'Medián (ms)',\n            data: chartData.medianData,\n            borderColor: 'rgba(75, 192, 192, 1)',\n            backgroundColor: 'rgba(75, 192, 192, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['Medián (ms)'] : false\n          }, {\n            label: '95. percentil (ms)',\n            data: chartData.p95Data,\n            borderColor: 'rgba(255, 99, 132, 1)',\n            backgroundColor: 'rgba(255, 99, 132, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['95. percentil (ms)'] : false\n          }, {\n            label: '99. percentil (ms)',\n            data: chartData.p99Data,\n            borderColor: 'rgba(255, 159, 64, 1)',\n            backgroundColor: 'rgba(255, 159, 64, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['99. percentil (ms)'] : true\n          }, {\n            label: 'Minimum (ms)',\n            data: chartData.minData,\n            borderColor: 'rgba(153, 102, 255, 1)',\n            backgroundColor: 'rgba(153, 102, 255, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['Minimum (ms)'] : true\n          }, {\n            label: 'Maximum (ms)',\n            data: chartData.maxData,\n            borderColor: 'rgba(255, 205, 86, 1)',\n            backgroundColor: 'rgba(255, 205, 86, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['Maximum (ms)'] : true\n          }, {\n            label: 'Počet volání',\n            data: chartData.totalCountData,\n            borderColor: 'rgba(201, 203, 207, 1)',\n            backgroundColor: 'rgba(201, 203, 207, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['Počet volání'] : true,\n            yAxisID: 'y1'\n          }];\n          // Určení, které osy Y mají být zobrazené na základě viditelných metrik\n          const visibleAxes = this.getVisibleAxes(datasets);\n          // Vytvoření nového grafu\n          this.aggregatedPerformanceChart = new Chart(ctx, {\n            type: 'line',\n            data: {\n              labels: chartData.labels,\n              datasets: datasets\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: chartData.title || 'Výkon v čase'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                },\n                legend: {\n                  position: 'top',\n                  labels: {\n                    boxWidth: 12\n                  },\n                  onClick: (_e, legendItem, legend) => {\n                    // Standardní chování - přepnutí viditelnosti datasetu\n                    const index = legendItem.datasetIndex;\n                    const ci = legend.chart;\n                    if (index !== undefined) {\n                      if (ci.isDatasetVisible(index)) {\n                        ci.hide(index);\n                        legendItem.hidden = true;\n                      } else {\n                        ci.show(index);\n                        legendItem.hidden = false;\n                      }\n                      // Aktualizace viditelnosti os Y na základě nově viditelných metrik\n                      this.updateAxisVisibility(ci);\n                      // Počkáme, až se graf aktualizuje\n                      setTimeout(() => {\n                        // Uložení nastavení viditelnosti do localStorage\n                        this.saveMetricsVisibility('aggregated', ci.data.datasets);\n                      }, 0);\n                    }\n                  }\n                }\n              },\n              scales: {\n                x: {\n                  display: true,\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                },\n                y: {\n                  display: visibleAxes.showLeftAxis,\n                  title: {\n                    display: visibleAxes.showLeftAxis,\n                    text: 'Doba odezvy (ms)'\n                  },\n                  beginAtZero: true\n                },\n                y1: {\n                  display: visibleAxes.showRightAxis,\n                  position: 'right',\n                  title: {\n                    display: visibleAxes.showRightAxis,\n                    text: 'Počet volání'\n                  },\n                  beginAtZero: true,\n                  grid: {\n                    drawOnChartArea: !visibleAxes.showLeftAxis // Zobrazit mřížku pouze když levá osa není viditelná\n                  }\n                }\n              }\n            }\n          });\n\n          console.log('renderAggregatedPerformanceChart - Graf úspěšně vytvořen');\n          // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n          setTimeout(() => {\n            if (this.aggregatedPerformanceChart) {\n              this.saveMetricsVisibility('aggregated', this.aggregatedPerformanceChart.data.datasets);\n            }\n            // Ukončíme vykreslování\n            this.renderingAggregatedChart = false;\n          }, 100);\n        } catch (error) {\n          console.error('renderAggregatedPerformanceChart - Chyba při vytváření grafu:', error);\n          this.renderingAggregatedChart = false; // Ukončíme vykreslování v případě chyby\n        }\n      } catch (error) {\n        console.error('renderAggregatedPerformanceChart - Chyba při získávání kontextu canvas elementu:', error);\n        this.renderingAggregatedChart = false; // Ukončíme vykreslování v případě chyby\n      }\n    }\n    /**\r\n     * Vykreslení grafu srovnání výkonu metod mezi instancemi\r\n     */\n    renderInstancesComparisonChart(data) {\n      console.log('renderInstancesComparisonChart - data:', data);\n      // Nastavíme příznak, že se graf vykresluje\n      this.renderingInstancesComparisonChart = true;\n      // Uložíme data pro pozdější vykreslení\n      this.instancesComparisonData = data;\n      // Kontrola, zda jsou data prázdná\n      const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0 && data.datasets && Array.isArray(data.datasets) && data.datasets.length > 0;\n      if (!hasData) {\n        // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n        this.hasInstancesComparisonChartData = false;\n        this.renderingInstancesComparisonChart = false;\n        // Zničíme existující graf, pokud existuje\n        if (this.instancesComparisonChart) {\n          this.instancesComparisonChart.destroy();\n          this.instancesComparisonChart = null;\n        }\n        return;\n      }\n      // Pokud máme data, nastavíme indikátor\n      this.hasInstancesComparisonChartData = true;\n      // Kontrola, zda je reference na canvas element k dispozici\n      if (!this.instancesComparisonChartRef) {\n        console.warn('renderInstancesComparisonChart - Reference na canvas element není k dispozici, zkusím později');\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderInstancesComparisonChart - Zkouším znovu po delším timeoutu (2000ms)');\n          if (this.instancesComparisonChartRef) {\n            this.renderInstancesComparisonChart(data);\n          } else {\n            console.error('renderInstancesComparisonChart - Canvas element stále není k dispozici po 2000ms!');\n            this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n          }\n        }, 2000);\n        return;\n      }\n      console.log('renderInstancesComparisonChart - Reference na canvas element je k dispozici');\n      try {\n        // Kontrola, zda je canvas element v DOM\n        if (!document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n          console.warn('renderInstancesComparisonChart - Canvas element není v DOM, zkusím později');\n          // Zkusíme počkat a zkusit to znovu po delším timeoutu\n          setTimeout(() => {\n            console.log('renderInstancesComparisonChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n            if (this.instancesComparisonChartRef && document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n              this.renderInstancesComparisonChart(data);\n            } else {\n              console.error('renderInstancesComparisonChart - Canvas element stále není v DOM po 2000ms!');\n            }\n          }, 2000);\n          return;\n        }\n        // Získání kontextu canvas elementu\n        const ctx = this.instancesComparisonChartRef.nativeElement.getContext('2d');\n        if (!ctx) {\n          console.error('renderInstancesComparisonChart - Nepodařilo se získat kontext canvas elementu!');\n          return;\n        }\n        console.log('renderInstancesComparisonChart - Kontext canvas elementu je k dispozici');\n        if (this.instancesComparisonChart) {\n          console.log('renderInstancesComparisonChart - Ničím existující graf');\n          this.instancesComparisonChart.destroy();\n        }\n        try {\n          console.log('renderInstancesComparisonChart - Vytvářím nový graf');\n          // Kontrola, zda je canvas element stále platný\n          if (!this.instancesComparisonChartRef || !document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n            console.error('renderInstancesComparisonChart - Canvas element již není platný!');\n            return;\n          }\n          // Kontrola, zda má canvas element rozměry\n          const width = this.instancesComparisonChartRef.nativeElement.width;\n          const height = this.instancesComparisonChartRef.nativeElement.height;\n          console.log('renderInstancesComparisonChart - Canvas rozměry:', {\n            width,\n            height\n          });\n          // Načtení nastavení viditelnosti metrik z localStorage\n          const visibility = this.loadMetricsVisibility('instances-comparison');\n          // Aplikace nastavení viditelnosti na datasety\n          if (visibility && data.datasets) {\n            data.datasets.forEach(dataset => {\n              if (dataset.label && visibility[dataset.label] !== undefined) {\n                dataset.hidden = !visibility[dataset.label];\n              }\n            });\n          }\n          // Uložení originálních labels pro tooltip\n          const originalLabels = [...data.labels];\n          // Konfigurace pro zkrácené popisky na ose X\n          const truncatedLabelsConfig = this.textUtils.createTruncatedLabelsConfig(originalLabels, {\n            enabled: true,\n            maxLength: 30,\n            showFullInTooltip: true // Zobrazit plný název v tooltip\n          });\n          // Základní konfigurace grafu\n          const baseOptions = {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              title: {\n                display: true,\n                text: data.title || 'Srovnání výkonu metod mezi instancemi'\n              },\n              tooltip: {\n                mode: 'index',\n                intersect: false\n              },\n              legend: {\n                position: 'top',\n                labels: {\n                  boxWidth: 12\n                },\n                onClick: (_e, legendItem, legend) => {\n                  // Standardní chování - přepnutí viditelnosti datasetu\n                  const index = legendItem.datasetIndex;\n                  const ci = legend.chart;\n                  if (index !== undefined) {\n                    if (ci.isDatasetVisible(index)) {\n                      ci.hide(index);\n                      legendItem.hidden = true;\n                    } else {\n                      ci.show(index);\n                      legendItem.hidden = false;\n                    }\n                    // Počkáme, až se graf aktualizuje\n                    setTimeout(() => {\n                      // Uložení nastavení viditelnosti do localStorage\n                      this.saveMetricsVisibility('instances-comparison', ci.data.datasets);\n                    }, 0);\n                    // Překreslení grafu\n                    ci.update();\n                  }\n                }\n              }\n            },\n            scales: {\n              x: {\n                display: true,\n                title: {\n                  display: true,\n                  text: 'Metoda'\n                }\n              },\n              y: {\n                display: true,\n                title: {\n                  display: true,\n                  text: 'Doba odezvy (ms)'\n                },\n                beginAtZero: true\n              }\n            }\n          };\n          // Sloučení s konfigurací pro zkrácené popisky\n          const finalOptions = this.textUtils.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n          // Vytvoření nového grafu\n          this.instancesComparisonChart = new Chart(ctx, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: data.datasets\n            },\n            options: finalOptions\n          });\n          console.log('renderInstancesComparisonChart - Graf úspěšně vytvořen');\n          // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n          setTimeout(() => {\n            if (this.instancesComparisonChart) {\n              this.saveMetricsVisibility('instances-comparison', this.instancesComparisonChart.data.datasets);\n            }\n            // Ukončíme vykreslování\n            this.renderingInstancesComparisonChart = false;\n          }, 100);\n        } catch (error) {\n          console.error('renderInstancesComparisonChart - Chyba při vytváření grafu:', error);\n          this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování v případě chyby\n        }\n      } catch (error) {\n        console.error('renderInstancesComparisonChart - Chyba při získávání kontextu canvas elementu:', error);\n        this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování v případě chyby\n      }\n    }\n    /**\r\n     * Vykreslení grafu srovnání výkonu metod mezi verzemi\r\n     */\n    renderVersionsComparisonChart(data) {\n      console.log('renderVersionsComparisonChart - data:', data);\n      // Nastavíme příznak, že se graf vykresluje\n      this.renderingVersionsComparisonChart = true;\n      // Uložíme data pro pozdější vykreslení\n      this.versionsComparisonData = data;\n      // Kontrola, zda jsou data prázdná\n      const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0 && data.datasets && Array.isArray(data.datasets) && data.datasets.length > 0;\n      if (!hasData) {\n        // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n        this.hasVersionsComparisonChartData = false;\n        this.renderingVersionsComparisonChart = false;\n        // Zničíme existující graf, pokud existuje\n        if (this.versionsComparisonChart) {\n          this.versionsComparisonChart.destroy();\n          this.versionsComparisonChart = null;\n        }\n        return;\n      }\n      // Pokud máme data, nastavíme indikátor\n      this.hasVersionsComparisonChartData = true;\n      // Kontrola, zda je reference na canvas element k dispozici\n      if (!this.versionsComparisonChartRef) {\n        console.warn('renderVersionsComparisonChart - Reference na canvas element není k dispozici, zkusím později');\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderVersionsComparisonChart - Zkouším znovu po delším timeoutu (2000ms)');\n          if (this.versionsComparisonChartRef) {\n            this.renderVersionsComparisonChart(data);\n          } else {\n            console.error('renderVersionsComparisonChart - Canvas element stále není k dispozici po 2000ms!');\n            this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n          }\n        }, 2000);\n        return;\n      }\n      console.log('renderVersionsComparisonChart - Reference na canvas element je k dispozici');\n      try {\n        // Kontrola, zda je canvas element v DOM\n        if (!document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n          console.warn('renderVersionsComparisonChart - Canvas element není v DOM, zkusím později');\n          // Zkusíme počkat a zkusit to znovu po delším timeoutu\n          setTimeout(() => {\n            console.log('renderVersionsComparisonChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n            if (this.versionsComparisonChartRef && document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n              this.renderVersionsComparisonChart(data);\n            } else {\n              console.error('renderVersionsComparisonChart - Canvas element stále není v DOM po 2000ms!');\n            }\n          }, 2000);\n          return;\n        }\n        // Získání kontextu canvas elementu\n        const ctx = this.versionsComparisonChartRef.nativeElement.getContext('2d');\n        if (!ctx) {\n          console.error('renderVersionsComparisonChart - Nepodařilo se získat kontext canvas elementu!');\n          return;\n        }\n        console.log('renderVersionsComparisonChart - Kontext canvas elementu je k dispozici');\n        if (this.versionsComparisonChart) {\n          console.log('renderVersionsComparisonChart - Ničím existující graf');\n          this.versionsComparisonChart.destroy();\n        }\n        try {\n          console.log('renderVersionsComparisonChart - Vytvářím nový graf');\n          // Kontrola, zda je canvas element stále platný\n          if (!this.versionsComparisonChartRef || !document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n            console.error('renderVersionsComparisonChart - Canvas element již není platný!');\n            return;\n          }\n          // Kontrola, zda má canvas element rozměry\n          const width = this.versionsComparisonChartRef.nativeElement.width;\n          const height = this.versionsComparisonChartRef.nativeElement.height;\n          console.log('renderVersionsComparisonChart - Canvas rozměry:', {\n            width,\n            height\n          });\n          // Načtení nastavení viditelnosti metrik z localStorage\n          const visibility = this.loadMetricsVisibility('versions-comparison');\n          // Aplikace nastavení viditelnosti na datasety\n          if (visibility && data.datasets) {\n            data.datasets.forEach(dataset => {\n              if (dataset.label && visibility[dataset.label] !== undefined) {\n                dataset.hidden = !visibility[dataset.label];\n              }\n            });\n          }\n          // Uložení originálních labels pro tooltip\n          const originalLabels = [...data.labels];\n          // Konfigurace pro zkrácené popisky na ose X\n          const truncatedLabelsConfig = this.textUtils.createTruncatedLabelsConfig(originalLabels, {\n            enabled: true,\n            maxLength: 30,\n            showFullInTooltip: true // Zobrazit plný název v tooltip\n          });\n          // Základní konfigurace grafu\n          const baseOptions = {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              title: {\n                display: true,\n                text: data.title || 'Srovnání výkonu metod mezi verzemi'\n              },\n              tooltip: {\n                mode: 'index',\n                intersect: false\n              },\n              legend: {\n                position: 'top',\n                labels: {\n                  boxWidth: 12\n                },\n                onClick: (_e, legendItem, legend) => {\n                  // Standardní chování - přepnutí viditelnosti datasetu\n                  const index = legendItem.datasetIndex;\n                  const ci = legend.chart;\n                  if (index !== undefined) {\n                    if (ci.isDatasetVisible(index)) {\n                      ci.hide(index);\n                      legendItem.hidden = true;\n                    } else {\n                      ci.show(index);\n                      legendItem.hidden = false;\n                    }\n                    // Počkáme, až se graf aktualizuje\n                    setTimeout(() => {\n                      // Uložení nastavení viditelnosti do localStorage\n                      this.saveMetricsVisibility('versions-comparison', ci.data.datasets);\n                    }, 0);\n                    // Překreslení grafu\n                    ci.update();\n                  }\n                }\n              }\n            },\n            scales: {\n              x: {\n                display: true,\n                title: {\n                  display: true,\n                  text: 'Metoda'\n                }\n              },\n              y: {\n                display: true,\n                title: {\n                  display: true,\n                  text: 'Doba odezvy (ms)'\n                },\n                beginAtZero: true\n              }\n            }\n          };\n          // Sloučení s konfigurací pro zkrácené popisky\n          const finalOptions = this.textUtils.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n          // Vytvoření nového grafu\n          this.versionsComparisonChart = new Chart(ctx, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: data.datasets\n            },\n            options: finalOptions\n          });\n          console.log('renderVersionsComparisonChart - Graf úspěšně vytvořen');\n          // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n          setTimeout(() => {\n            if (this.versionsComparisonChart) {\n              this.saveMetricsVisibility('versions-comparison', this.versionsComparisonChart.data.datasets);\n            }\n            // Ukončíme vykreslování\n            this.renderingVersionsComparisonChart = false;\n          }, 100);\n        } catch (error) {\n          console.error('renderVersionsComparisonChart - Chyba při vytváření grafu:', error);\n          this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování v případě chyby\n        }\n      } catch (error) {\n        console.error('renderVersionsComparisonChart - Chyba při získávání kontextu canvas elementu:', error);\n        this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování v případě chyby\n      }\n    }\n    /**\r\n     * Vykreslení grafu detailu metody\r\n     */\n    renderMethodDetailChart(data) {\n      console.log('renderMethodDetailChart - data:', data);\n      // Nastavíme příznak, že se graf vykresluje\n      this.renderingMethodDetailChart = true;\n      // Uložíme data pro pozdější vykreslení\n      this.methodDetailChartData = data;\n      // Kontrola, zda jsou data prázdná\n      const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0;\n      if (!hasData) {\n        // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n        this.hasMethodDetailChartData = false;\n        this.renderingMethodDetailChart = false;\n        // Zničíme existující graf, pokud existuje\n        if (this.methodDetailChart) {\n          this.methodDetailChart.destroy();\n          this.methodDetailChart = null;\n        }\n        return;\n      }\n      // Aktualizace hodnot filtru podle dat grafu, pokud jsou k dispozici\n      if (data.className && data.methodName) {\n        console.log('renderMethodDetailChart - Aktualizuji hodnoty filtru podle dat grafu:', {\n          className: data.className,\n          methodName: data.methodName\n        });\n        // Aktualizace pouze pokud se hodnoty liší\n        if (this.methodDetailFilterData.className !== data.className || this.methodDetailFilterData.methodName !== data.methodName) {\n          this.methodDetailFilterData.className = data.className;\n          this.methodDetailFilterData.methodName = data.methodName;\n          // Uložení filtru do localStorage\n          this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n        }\n      }\n      // Pokud máme data, nastavíme indikátor\n      this.hasMethodDetailChartData = true;\n      // Kontrola, zda je reference na canvas element k dispozici\n      if (!this.methodDetailChartRef) {\n        console.warn('renderMethodDetailChart - Reference na canvas element není k dispozici, zkusím později');\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderMethodDetailChart - Zkouším znovu po delším timeoutu (2000ms)');\n          if (this.methodDetailChartRef) {\n            this.renderMethodDetailChart(data);\n          } else {\n            console.error('renderMethodDetailChart - Canvas element stále není k dispozici po 2000ms!');\n            this.renderingMethodDetailChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n          }\n        }, 2000);\n        return;\n      }\n      console.log('renderMethodDetailChart - Reference na canvas element je k dispozici');\n      try {\n        // Kontrola, zda je canvas element v DOM\n        if (!document.body.contains(this.methodDetailChartRef.nativeElement)) {\n          console.warn('renderMethodDetailChart - Canvas element není v DOM, zkusím později');\n          // Zkusíme počkat a zkusit to znovu po delším timeoutu\n          setTimeout(() => {\n            console.log('renderMethodDetailChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n            if (this.methodDetailChartRef && document.body.contains(this.methodDetailChartRef.nativeElement)) {\n              this.renderMethodDetailChart(data);\n            } else {\n              console.error('renderMethodDetailChart - Canvas element stále není v DOM po 2000ms!');\n            }\n          }, 2000);\n          return;\n        }\n        // Získání kontextu canvas elementu\n        const ctx = this.methodDetailChartRef.nativeElement.getContext('2d');\n        if (!ctx) {\n          console.error('renderMethodDetailChart - Nepodařilo se získat kontext canvas elementu!');\n          return;\n        }\n        console.log('renderMethodDetailChart - Kontext canvas elementu je k dispozici');\n        if (this.methodDetailChart) {\n          console.log('renderMethodDetailChart - Ničím existující graf');\n          this.methodDetailChart.destroy();\n        }\n        try {\n          console.log('renderMethodDetailChart - Vytvářím nový graf');\n          // Kontrola, zda je canvas element stále platný\n          if (!this.methodDetailChartRef || !document.body.contains(this.methodDetailChartRef.nativeElement)) {\n            console.error('renderMethodDetailChart - Canvas element již není platný!');\n            return;\n          }\n          // Kontrola, zda má canvas element rozměry\n          const width = this.methodDetailChartRef.nativeElement.width;\n          const height = this.methodDetailChartRef.nativeElement.height;\n          console.log('renderMethodDetailChart - Canvas rozměry:', {\n            width,\n            height\n          });\n          // Kontrola, zda jsou všechna pole dat stejné délky\n          if (hasData) {\n            const labelsLength = data.labels.length;\n            // Kontrola existence a délky polí dat\n            if (!data.avgData || data.avgData.length !== labelsLength) {\n              console.warn('renderMethodDetailChart - avgData má jinou délku než labels, upravuji');\n              data.avgData = this.adjustArrayLength(data.avgData || [], labelsLength);\n            }\n            if (!data.medianData || data.medianData.length !== labelsLength) {\n              console.warn('renderMethodDetailChart - medianData má jinou délku než labels, upravuji');\n              data.medianData = this.adjustArrayLength(data.medianData || [], labelsLength);\n            }\n            if (!data.p95Data || data.p95Data.length !== labelsLength) {\n              console.warn('renderMethodDetailChart - p95Data má jinou délku než labels, upravuji');\n              data.p95Data = this.adjustArrayLength(data.p95Data || [], labelsLength);\n            }\n            if (!data.totalCountData || data.totalCountData.length !== labelsLength) {\n              console.warn('renderMethodDetailChart - totalCountData má jinou délku než labels, upravuji');\n              data.totalCountData = this.adjustArrayLength(data.totalCountData || [], labelsLength);\n            }\n          }\n          // Načtení nastavení viditelnosti metrik z localStorage\n          const visibility = this.loadMetricsVisibility('method-detail');\n          // Vytvoření datasetů s nastavením viditelnosti\n          const datasets = [{\n            label: 'Průměr (ms)',\n            data: data.avgData,\n            borderColor: 'rgba(54, 162, 235, 1)',\n            backgroundColor: 'rgba(54, 162, 235, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['Průměr (ms)'] : false\n          }, {\n            label: 'Medián (ms)',\n            data: data.medianData,\n            borderColor: 'rgba(75, 192, 192, 1)',\n            backgroundColor: 'rgba(75, 192, 192, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['Medián (ms)'] : false\n          }, {\n            label: '95. percentil (ms)',\n            data: data.p95Data,\n            borderColor: 'rgba(255, 99, 132, 1)',\n            backgroundColor: 'rgba(255, 99, 132, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['95. percentil (ms)'] : false\n          }, {\n            label: 'Počet volání',\n            data: data.totalCountData,\n            borderColor: 'rgba(201, 203, 207, 1)',\n            backgroundColor: 'rgba(201, 203, 207, 0.2)',\n            borderWidth: 2,\n            tension: 0.3,\n            fill: false,\n            hidden: visibility ? !visibility['Počet volání'] : false,\n            yAxisID: 'y1'\n          }];\n          // Vytvoření nového grafu\n          this.methodDetailChart = new Chart(ctx, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: datasets\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title || 'Detail metody'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                },\n                legend: {\n                  position: 'top',\n                  labels: {\n                    boxWidth: 12\n                  },\n                  onClick: (_e, legendItem, legend) => {\n                    // Standardní chování - přepnutí viditelnosti datasetu\n                    const index = legendItem.datasetIndex;\n                    const ci = legend.chart;\n                    if (index !== undefined) {\n                      if (ci.isDatasetVisible(index)) {\n                        ci.hide(index);\n                        legendItem.hidden = true;\n                      } else {\n                        ci.show(index);\n                        legendItem.hidden = false;\n                      }\n                      // Počkáme, až se graf aktualizuje\n                      setTimeout(() => {\n                        // Uložení nastavení viditelnosti do localStorage\n                        this.saveMetricsVisibility('method-detail', ci.data.datasets);\n                      }, 0);\n                      // Překreslení grafu\n                      ci.update();\n                    }\n                  }\n                }\n              },\n              scales: {\n                x: {\n                  display: true,\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                },\n                y: {\n                  display: true,\n                  title: {\n                    display: true,\n                    text: 'Doba odezvy (ms)'\n                  },\n                  beginAtZero: true\n                },\n                y1: {\n                  display: true,\n                  position: 'right',\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  },\n                  beginAtZero: true,\n                  grid: {\n                    drawOnChartArea: false\n                  }\n                }\n              }\n            }\n          });\n          console.log('renderMethodDetailChart - Graf úspěšně vytvořen');\n          // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n          setTimeout(() => {\n            if (this.methodDetailChart) {\n              this.saveMetricsVisibility('method-detail', this.methodDetailChart.data.datasets);\n            }\n            // Ukončíme vykreslování\n            this.renderingMethodDetailChart = false;\n          }, 100);\n        } catch (error) {\n          console.error('renderMethodDetailChart - Chyba při vytváření grafu:', error);\n          this.renderingMethodDetailChart = false; // Ukončíme vykreslování v případě chyby\n        }\n      } catch (error) {\n        console.error('renderMethodDetailChart - Chyba při získávání kontextu canvas elementu:', error);\n        this.renderingMethodDetailChart = false; // Ukončíme vykreslování v případě chyby\n      }\n    }\n    /**\r\n     * Příprava dat pro graf agregovaných výkonnostních metrik\r\n     */\n    prepareAggregatedPerformanceChartData(data) {\n      console.log('prepareAggregatedPerformanceChartData - vstupní data:', data);\n      // Výchozí prázdná struktura pro data grafu\n      const chartData = {\n        labels: [],\n        avgData: [],\n        medianData: [],\n        p95Data: [],\n        p99Data: [],\n        minData: [],\n        maxData: [],\n        totalCountData: [],\n        title: 'Výkon v čase'\n      };\n      // Kontrola, zda data obsahují potřebné údaje\n      if (!data) {\n        console.warn('prepareAggregatedPerformanceChartData - Chybí data pro graf');\n        return chartData;\n      }\n      try {\n        // Pokud jsou data již připravena v požadovaném formátu z API\n        if (data.labels && Array.isArray(data.labels) && data.labels.length > 0) {\n          console.log('prepareAggregatedPerformanceChartData - Používám předpřipravená data z API');\n          chartData.labels = data.labels;\n          chartData.avgData = data.avgData || [];\n          chartData.medianData = data.medianData || [];\n          chartData.p95Data = data.p95Data || [];\n          chartData.p99Data = data.p99Data || [];\n          chartData.minData = data.minData || [];\n          chartData.maxData = data.maxData || [];\n          chartData.totalCountData = data.totalCountData || [];\n          // Nastavení titulku grafu\n          if (data.title) {\n            chartData.title = data.title;\n          }\n        }\n        // Pokud jsou data ve formátu denních dat\n        else if (data.dailyData && Array.isArray(data.dailyData) && data.dailyData.length > 0) {\n          console.log('prepareAggregatedPerformanceChartData - Zpracovávám denní data');\n          const dailyData = data.dailyData;\n          // Seřazení dat podle data\n          dailyData.sort((a, b) => {\n            const dateA = a.date ? new Date(a.date).getTime() : 0;\n            const dateB = b.date ? new Date(b.date).getTime() : 0;\n            return dateA - dateB;\n          });\n          // Naplnění polí pro graf\n          chartData.labels = dailyData.map(item => {\n            if (item.date) {\n              const date = new Date(item.date);\n              return this.formatDate(date);\n            }\n            return '';\n          }).filter(label => label !== '');\n          // Mapování dat s kontrolou existence vlastností\n          chartData.avgData = dailyData.map(item => item.avg !== undefined ? item.avg : 0);\n          chartData.medianData = dailyData.map(item => item.median !== undefined ? item.median : 0);\n          chartData.p95Data = dailyData.map(item => item.percentil95 !== undefined ? item.percentil95 : 0);\n          chartData.p99Data = dailyData.map(item => item.percentil99 !== undefined ? item.percentil99 : 0);\n          chartData.minData = dailyData.map(item => item.min !== undefined ? item.min : 0);\n          chartData.maxData = dailyData.map(item => item.max !== undefined ? item.max : 0);\n          chartData.totalCountData = dailyData.map(item => item.totalCount !== undefined ? item.totalCount : 0);\n          // Nastavení titulku grafu\n          if (data.title) {\n            chartData.title = data.title;\n          }\n        } else {\n          console.warn('prepareAggregatedPerformanceChartData - Neznámý formát dat');\n          // Pokus o extrakci dat z neznámého formátu\n          if (typeof data === 'object') {\n            // Pokus o nalezení polí s daty\n            for (const key in data) {\n              if (key === 'labels' && Array.isArray(data[key])) {\n                chartData.labels = data[key].map(item => String(item));\n              } else if (key === 'avgData' && Array.isArray(data[key])) {\n                chartData.avgData = data[key].map(item => Number(item) || 0);\n              } else if (key === 'medianData' && Array.isArray(data[key])) {\n                chartData.medianData = data[key].map(item => Number(item) || 0);\n              } else if (key === 'p95Data' && Array.isArray(data[key])) {\n                chartData.p95Data = data[key].map(item => Number(item) || 0);\n              } else if (key === 'p99Data' && Array.isArray(data[key])) {\n                chartData.p99Data = data[key].map(item => Number(item) || 0);\n              } else if (key === 'minData' && Array.isArray(data[key])) {\n                chartData.minData = data[key].map(item => Number(item) || 0);\n              } else if (key === 'maxData' && Array.isArray(data[key])) {\n                chartData.maxData = data[key].map(item => Number(item) || 0);\n              } else if (key === 'totalCountData' && Array.isArray(data[key])) {\n                chartData.totalCountData = data[key].map(item => Number(item) || 0);\n              } else if (key === 'title' && typeof data[key] === 'string') {\n                chartData.title = data[key];\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('prepareAggregatedPerformanceChartData - Chyba při zpracování dat:', error);\n      }\n      console.log('prepareAggregatedPerformanceChartData - výstupní data:', chartData);\n      return chartData;\n    }\n    /**\r\n     * Inicializace popoverů pro nápovědu\r\n     */\n    initPopovers() {\n      console.log('initPopovers - Inicializuji popovery');\n      // Definice obsahu nápověd\n      const helpContent = {\n        'aggregated-performance': 'Graf zobrazuje agregované výkonnostní metriky DIS metod v čase. ' + 'Pro každý den jsou zobrazeny hodnoty jako průměr, medián, 95. percentil, 99. percentil, minimum, maximum a počet volání. ' + 'Kliknutím na legendu můžete zobrazit nebo skrýt jednotlivé metriky. ' + 'Výchozí zobrazení obsahuje průměr, medián a 95. percentil, ostatní metriky jsou skryté.',\n        'instances-comparison': 'Graf umožňuje srovnání výkonu stejných DIS metod mezi různými instancemi. ' + 'Vyberte alespoň dvě instance, které chcete porovnat, a volitelně filtrujte podle konkrétní třídy nebo metody. ' + 'Bez filtrace se zobrazují pouze metody s významnou odezvou (alespoň u jedné instance větší než průměrná odezva DIS metod). ' + 'Graf zobrazuje průměrnou dobu odezvy metod pro každou vybranou instanci.',\n        'versions-comparison': 'Graf umožňuje srovnání výkonu DIS metod mezi různými verzemi jedné instance. ' + 'Vyberte instanci a volitelně filtrujte podle konkrétní třídy nebo metody. ' + 'Bez filtrace se zobrazují pouze metody s významnou odezvou (alespoň u jedné verze větší než průměrná odezva DIS metod). ' + 'Graf zobrazuje průměrnou dobu odezvy metod pro každou verzi aplikace v rámci vybrané instance.',\n        'slowest-methods': 'Tabulka zobrazuje nejpomalejší DIS metody seřazené podle 95. percentilu doby odezvy. ' + 'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' + 'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n        'methods': 'Tabulka zobrazuje DIS metody podle zvoleného filtru (nejpomalejší, nejčastěji volané nebo všechny). ' + 'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' + 'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n        'most-called-methods': 'Tabulka zobrazuje nejčastěji volané DIS metody seřazené podle počtu volání. ' + 'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' + 'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n        'all-methods': 'Tabulka zobrazuje všechny DIS metody. ' + 'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' + 'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n        'method-detail': 'Graf zobrazuje výkon konkrétní DIS metody v čase. ' + 'Pro každý den jsou zobrazeny hodnoty jako průměr, medián, 95. percentil, 99. percentil, minimum, maximum a počet volání. ' + 'Kliknutím na legendu můžete zobrazit nebo skrýt jednotlivé metriky. ' + 'Nad grafem jsou zobrazeny souhrnné statistiky metody za celé zvolené období.',\n        'avg-response-time': 'Průměrná doba odezvy metody v milisekundách za celé zvolené období. ' + 'Průměr je vypočítán jako aritmetický průměr všech naměřených hodnot. ' + 'Může být ovlivněn extrémními hodnotami, proto je vhodné sledovat také medián.',\n        'median-response-time': 'Medián doby odezvy metody v milisekundách za celé zvolené období. ' + 'Medián představuje prostřední hodnotu ze všech naměřených časů (50. percentil). ' + 'Polovina volání byla rychlejší a polovina pomalejší než tato hodnota. ' + 'Medián není ovlivněn extrémními hodnotami, proto lépe reprezentuje typickou dobu odezvy než průměr.',\n        'p95-response-time': '95. percentil doby odezvy metody v milisekundách za celé zvolené období. ' + 'Tato hodnota znamená, že 95% všech volání metody bylo rychlejších než tato hodnota. ' + 'Zbývajících 5% volání bylo pomalejších. ' + 'Tento ukazatel je důležitý pro identifikaci problémů s výkonem, které ovlivňují menší část uživatelů.',\n        'total-calls': 'Celkový počet volání metody za zvolené časové období. ' + 'Tento údaj ukazuje, jak často je metoda používána. ' + 'Vysoký počet volání v kombinaci s delší dobou odezvy může indikovat potenciální problém s výkonem aplikace.'\n      };\n      try {\n        // Inicializace popoverů pomocí Bootstrap API\n        setTimeout(() => {\n          try {\n            // Kontrola, zda je bootstrap k dispozici\n            if (typeof bootstrap === 'undefined') {\n              console.error('initPopovers - Bootstrap není k dispozici!');\n              return;\n            }\n            // Kontrola, zda je Popover k dispozici\n            if (typeof bootstrap.Popover === 'undefined') {\n              console.error('initPopovers - Bootstrap.Popover není k dispozici!');\n              return;\n            }\n            // Nejprve zrušíme všechny existující popovery\n            const existingPopovers = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\n            existingPopovers.forEach(el => {\n              try {\n                // @ts-ignore\n                const popover = bootstrap.Popover.getInstance(el);\n                if (popover) {\n                  popover.dispose();\n                }\n              } catch (error) {\n                console.warn('initPopovers - Chyba při rušení existujícího popoveru:', error);\n              }\n            });\n            // Nyní inicializujeme nové popovery\n            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\n            console.log('initPopovers - Nalezeno', popoverTriggerList.length, 'elementů s popovery');\n            popoverTriggerList.forEach(popoverTriggerEl => {\n              try {\n                const helpType = popoverTriggerEl.getAttribute('data-help-type');\n                const content = helpContent[helpType] || 'Nápověda není k dispozici.';\n                // Přidáme titulek popoveru\n                let title = '';\n                switch (helpType) {\n                  case 'aggregated-performance':\n                    title = 'Agregovaný výkon v čase';\n                    break;\n                  case 'instances-comparison':\n                    title = 'Srovnání výkonu metod mezi instancemi';\n                    break;\n                  case 'versions-comparison':\n                    title = 'Srovnání výkonu metod mezi verzemi';\n                    break;\n                  case 'slowest-methods':\n                    title = 'Nejpomalejší metody';\n                    break;\n                  case 'most-called-methods':\n                    title = 'Nejčastěji volané metody';\n                    break;\n                  case 'all-methods':\n                    title = 'Všechny metody';\n                    break;\n                  case 'method-detail':\n                    title = 'Detail metody';\n                    break;\n                  case 'avg-response-time':\n                    title = 'Průměrná doba odezvy';\n                    break;\n                  case 'median-response-time':\n                    title = 'Medián doby odezvy';\n                    break;\n                  case 'p95-response-time':\n                    title = '95. percentil';\n                    break;\n                  case 'total-calls':\n                    title = 'Celkový počet volání';\n                    break;\n                  default:\n                    title = 'Nápověda';\n                }\n                // @ts-ignore\n                new bootstrap.Popover(popoverTriggerEl, {\n                  container: 'body',\n                  trigger: 'hover',\n                  placement: 'top',\n                  title: title,\n                  content: content,\n                  html: true\n                });\n              } catch (error) {\n                console.error('initPopovers - Chyba při inicializaci popoveru:', error);\n              }\n            });\n            console.log('initPopovers - Popovery úspěšně inicializovány');\n          } catch (error) {\n            console.error('initPopovers - Chyba při inicializaci popoverů:', error);\n          }\n        }, 500);\n      } catch (error) {\n        console.error('initPopovers - Chyba při inicializaci popoverů:', error);\n      }\n    }\n    /**\r\n     * Formátování data\r\n     */\n    formatDate(date) {\n      return this.datePipe.transform(date, 'dd.MM.yyyy') || '';\n    }\n    /**\r\n     * Úprava délky pole na požadovanou délku\r\n     * @param array Pole, které chceme upravit\r\n     * @param length Požadovaná délka pole\r\n     * @returns Upravené pole s požadovanou délkou\r\n     */\n    adjustArrayLength(array, length) {\n      if (!array) {\n        return new Array(length).fill(0);\n      }\n      if (array.length === length) {\n        return array;\n      }\n      if (array.length > length) {\n        return array.slice(0, length);\n      }\n      // Pokud je pole kratší než požadovaná délka, doplníme ho nulami\n      const result = [...array];\n      while (result.length < length) {\n        result.push(0);\n      }\n      return result;\n    }\n    /**\r\n     * Určí, které osy Y mají být zobrazené na základě viditelných metrik\r\n     */\n    getVisibleAxes(datasets, chart) {\n      let showLeftAxis = false;\n      let showRightAxis = false;\n      datasets.forEach((dataset, index) => {\n        // Pokud máme referenci na graf, použijeme Chart.js API pro zjištění viditelnosti\n        const isVisible = chart ? chart.isDatasetVisible(index) : !dataset.hidden;\n        if (isVisible) {\n          if (dataset.yAxisID === 'y1') {\n            // Metrika \"Počet volání\" používá pravou osu\n            showRightAxis = true;\n          } else {\n            // Všechny ostatní metriky (doba odezvy) používají levou osu\n            showLeftAxis = true;\n          }\n        }\n      });\n      return {\n        showLeftAxis,\n        showRightAxis\n      };\n    }\n    /**\r\n     * Aktualizuje viditelnost os Y na základě aktuálně viditelných metrik\r\n     */\n    updateAxisVisibility(chart) {\n      if (!chart || !chart.data || !chart.data.datasets) {\n        return;\n      }\n      const visibleAxes = this.getVisibleAxes(chart.data.datasets, chart);\n      // Aktualizace konfigurace os\n      if (chart.options && chart.options.scales) {\n        if (chart.options.scales.y) {\n          chart.options.scales.y.display = visibleAxes.showLeftAxis;\n          chart.options.scales.y.title.display = visibleAxes.showLeftAxis;\n        }\n        if (chart.options.scales.y1) {\n          chart.options.scales.y1.display = visibleAxes.showRightAxis;\n          chart.options.scales.y1.title.display = visibleAxes.showRightAxis;\n          // Zobrazit mřížku pro pravou osu pouze když levá osa není viditelná\n          chart.options.scales.y1.grid.drawOnChartArea = !visibleAxes.showLeftAxis;\n        }\n      }\n      // Překreslení grafu s novými nastaveními\n      chart.update();\n    }\n    static {\n      this.ɵfac = function PerformanceComponent_Factory(t) {\n        return new (t || PerformanceComponent)(i0.ɵɵdirectiveInject(i1.PerformanceService), i0.ɵɵdirectiveInject(i2.InstanceService), i0.ɵɵdirectiveInject(i3.DatePipe), i0.ɵɵdirectiveInject(i4.ChartModalService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.TableSortingService), i0.ɵɵdirectiveInject(i6.TextUtilsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PerformanceComponent,\n        selectors: [[\"app-performance\"]],\n        viewQuery: function PerformanceComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(_c2, 5);\n            i0.ɵɵviewQuery(_c3, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.aggregatedPerformanceChartRef = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.instancesComparisonChartRef = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.versionsComparisonChartRef = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.methodDetailChartRef = _t.first);\n          }\n        },\n        decls: 11,\n        vars: 8,\n        consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [3, \"tabs\", \"activeTabId\", \"tabChange\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [3, \"entityType\", \"fields\", \"filterChange\"], [\"class\", \"text-center my-5\", 4, \"ngIf\"], [1, \"text-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"aggregated-performance\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"title\", \"Zobrazit graf na celou obrazovku\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-arrows-fullscreen\"], [1, \"card-body\"], [1, \"chart-container\", 2, \"position\", \"relative\", \"height\", \"400px\", \"width\", \"100%\"], [\"class\", \"chart-loading-overlay\", 4, \"ngIf\"], [\"class\", \"no-data-message\", 4, \"ngIf\"], [\"width\", \"800\", \"height\", \"400\", \"style\", \"display: block;\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"slowest-methods\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"dark-header\", \"table-header-override\"], [1, \"dark-header-row\"], [1, \"sortable-header\", 3, \"click\"], [1, \"bi\"], [\"style\", \"cursor: pointer;\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-loading-overlay\"], [1, \"no-data-message\"], [1, \"bi\", \"bi-bar-chart-line\"], [\"width\", \"800\", \"height\", \"400\", 2, \"display\", \"block\"], [\"aggregatedPerformanceChart\", \"\"], [2, \"cursor\", \"pointer\"], [\"title\", \"Zobrazit detail\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-info-circle\"], [\"colspan\", \"10\", 1, \"text-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"instances-comparison\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"instancesComparisonChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"versions-comparison\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"versionsComparisonChart\", \"\"], [1, \"mb-0\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"slowest-methods\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"most-called-methods\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"all-methods\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"slowest-methods\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"most-called-methods\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"all-methods\"], [1, \"row\", \"mb-4\"], [1, \"col-md-3\", \"mb-3\"], [1, \"card\", \"h-100\"], [1, \"card-body\", \"text-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"avg-response-time\", 1, \"card-title\", 2, \"cursor\", \"help\"], [1, \"display-4\", \"mb-0\"], [1, \"text-muted\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"median-response-time\", 1, \"card-title\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"p95-response-time\", 1, \"card-title\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"total-calls\", 1, \"card-title\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"method-detail\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"methodDetailChart\", \"\"]],\n        template: function PerformanceComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"V\\u00FDkon DIS\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"app-tab-navigation\", 2);\n            i0.ɵɵlistener(\"tabChange\", function PerformanceComponent_Template_app_tab_navigation_tabChange_4_listener($event) {\n              return ctx.changeTab($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, PerformanceComponent_div_5_Template, 3, 1, \"div\", 3);\n            i0.ɵɵtemplate(6, PerformanceComponent_div_6_Template, 4, 4, \"div\", 4);\n            i0.ɵɵtemplate(7, PerformanceComponent_div_7_Template, 4, 4, \"div\", 4);\n            i0.ɵɵtemplate(8, PerformanceComponent_div_8_Template, 4, 4, \"div\", 4);\n            i0.ɵɵtemplate(9, PerformanceComponent_div_9_Template, 4, 4, \"div\", 4);\n            i0.ɵɵtemplate(10, PerformanceComponent_div_10_Template, 4, 4, \"div\", 4);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"tabs\", ctx.tabs)(\"activeTabId\", ctx.activeTab);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"aggregated\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"instances-comparison\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"versions-comparison\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"methods\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"method-detail\");\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i7.AdvancedFilterComponent, i8.TabNavigationComponent, i3.DecimalPipe],\n        styles: [\".chart-container[_ngcontent-%COMP%]{position:relative;height:400px;width:100%}.chart-loading-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background-color:#fffc;display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:10}.no-data-message[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;z-index:5}.no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:1rem;color:#6c757d}.no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;color:#6c757d}@media (prefers-color-scheme: dark){.chart-loading-overlay[_ngcontent-%COMP%]{background-color:#212529cc}.no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#adb5bd}}.table-responsive[_ngcontent-%COMP%]{overflow-x:auto}.form-label[_ngcontent-%COMP%]{font-weight:500}.card-title[_ngcontent-%COMP%]{cursor:help}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{white-space:nowrap}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{vertical-align:middle}.btn-outline-info[_ngcontent-%COMP%]{color:#0dcaf0;border-color:#0dcaf0}.btn-outline-info[_ngcontent-%COMP%]:hover{color:#fff;background-color:#0dcaf0;border-color:#0dcaf0}.sortable-header[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;position:relative}.sortable-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem;margin-left:5px;opacity:.7;display:inline-block;vertical-align:middle}.sortable-header[_ngcontent-%COMP%]   i.bi-sort-up[_ngcontent-%COMP%], .sortable-header[_ngcontent-%COMP%]   i.bi-sort-down[_ngcontent-%COMP%]{opacity:1;color:#fff;font-size:1rem}\"]\n      });\n    }\n  }\n  return PerformanceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}