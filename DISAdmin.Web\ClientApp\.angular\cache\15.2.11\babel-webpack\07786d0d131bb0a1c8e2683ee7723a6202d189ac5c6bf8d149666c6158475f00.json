{"ast": null, "code": "import { Chart, registerables } from 'chart.js';\nimport { minArrayLength } from '../shared/advanced-filter/advanced-filter.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/performance.service\";\nimport * as i2 from \"../services/instance.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../services/chart-modal.service\";\nimport * as i5 from \"../services/table-sorting.service\";\nimport * as i6 from \"../services/text-utils.service\";\nimport * as i7 from \"../shared/advanced-filter/advanced-filter.component\";\nimport * as i8 from \"../shared/tab-navigation/tab-navigation.component\";\nconst _c0 = [\"aggregatedPerformanceChart\"];\nconst _c1 = [\"instancesComparisonChart\"];\nconst _c2 = [\"versionsComparisonChart\"];\nconst _c3 = [\"methodDetailChart\"];\nfunction PerformanceComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_5_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.error = null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction PerformanceComponent_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_6_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Zpracov\\u00E1n\\u00ED dat grafu...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_6_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Nejsou k dispozici \\u017E\\u00E1dn\\u00E1 data pro zobrazen\\u00ED grafu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_6_div_3_canvas_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 34, 35);\n  }\n}\nfunction PerformanceComponent_div_6_div_3_tr_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\")(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_tr_51_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const method_r16 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.showMethodDetail(method_r16));\n    });\n    i0.ɵɵelement(26, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r16.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r16.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r16.methodName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r16.totalCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, method_r16.min, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 12, method_r16.max, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 15, method_r16.avg, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, method_r16.median, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, method_r16.percentil95, \"1.0-2\"));\n  }\n}\nfunction PerformanceComponent_div_6_div_3_tr_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"\\u017D\\u00E1dn\\u00E9 metody k zobrazen\\u00ED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"h5\", 15);\n    i0.ɵɵtext(4, \"Agregovan\\u00FD v\\u00FDkon v \\u010Dase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.openFullscreenChart(ctx_r19.aggregatedPerformanceChart, \"Agregovan\\u00FD v\\u00FDkon v \\u010Dase\"));\n    });\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 19);\n    i0.ɵɵtemplate(9, PerformanceComponent_div_6_div_3_div_9_Template, 6, 0, \"div\", 20);\n    i0.ɵɵtemplate(10, PerformanceComponent_div_6_div_3_div_10_Template, 4, 0, \"div\", 21);\n    i0.ɵɵtemplate(11, PerformanceComponent_div_6_div_3_canvas_11_Template, 2, 0, \"canvas\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"div\", 14)(14, \"h5\", 23);\n    i0.ɵɵtext(15, \"Nejpomalej\\u0161\\u00ED metody\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 18)(17, \"div\", 24)(18, \"table\", 25)(19, \"thead\", 26)(20, \"tr\", 27)(21, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_21_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onSort(\"instanceName\"));\n    });\n    i0.ɵɵtext(22, \" Instance \");\n    i0.ɵɵelement(23, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_24_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onSort(\"className\"));\n    });\n    i0.ɵɵtext(25, \" T\\u0159\\u00EDda \");\n    i0.ɵɵelement(26, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_27_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onSort(\"methodName\"));\n    });\n    i0.ɵɵtext(28, \" Metoda \");\n    i0.ɵɵelement(29, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_30_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.onSort(\"totalCount\"));\n    });\n    i0.ɵɵtext(31, \" Po\\u010Det vol\\u00E1n\\u00ED \");\n    i0.ɵɵelement(32, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_33_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onSort(\"min\"));\n    });\n    i0.ɵɵtext(34, \" Min (ms) \");\n    i0.ɵɵelement(35, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_36_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onSort(\"max\"));\n    });\n    i0.ɵɵtext(37, \" Max (ms) \");\n    i0.ɵɵelement(38, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_39_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.onSort(\"avg\"));\n    });\n    i0.ɵɵtext(40, \" Pr\\u016Fm\\u011Br (ms) \");\n    i0.ɵɵelement(41, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_42_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onSort(\"median\"));\n    });\n    i0.ɵɵtext(43, \" Medi\\u00E1n (ms) \");\n    i0.ɵɵelement(44, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_6_div_3_Template_th_click_45_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.onSort(\"percentil95\"));\n    });\n    i0.ɵɵtext(46, \" 95. percentil (ms) \");\n    i0.ɵɵelement(47, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"th\");\n    i0.ɵɵtext(49, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"tbody\");\n    i0.ɵɵtemplate(51, PerformanceComponent_div_6_div_3_tr_51_Template, 27, 24, \"tr\", 30);\n    i0.ɵɵtemplate(52, PerformanceComponent_div_6_div_3_tr_52_Template, 3, 0, \"tr\", 4);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.renderingAggregatedChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.hasAggregatedChartData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.hasAggregatedChartData);\n    i0.ɵɵadvance(12);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"instanceName\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"instanceName\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"instanceName\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"className\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"className\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"className\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"methodName\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"methodName\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"methodName\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"totalCount\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"totalCount\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"totalCount\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"min\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"min\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"min\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"max\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"max\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"max\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"avg\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"avg\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"avg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"median\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"median\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"median\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r9.sortColumn === \"percentil95\" && ctx_r9.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r9.sortColumn === \"percentil95\" && ctx_r9.sortDirection === \"desc\")(\"bi-sort\", ctx_r9.sortColumn !== \"percentil95\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.slowestMethods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.slowestMethods || ctx_r9.slowestMethods.length === 0);\n  }\n}\nfunction PerformanceComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_6_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.onAggregatedFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_6_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_6_div_3_Template, 53, 59, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_aggregated\")(\"fields\", ctx_r1.aggregatedFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction PerformanceComponent_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_7_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Zpracov\\u00E1n\\u00ED dat grafu...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_7_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Nejsou k dispozici \\u017E\\u00E1dn\\u00E1 data pro zobrazen\\u00ED grafu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_7_div_3_canvas_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 34, 41);\n  }\n}\nfunction PerformanceComponent_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"h5\", 40);\n    i0.ɵɵtext(4, \"Srovn\\u00E1n\\u00ED v\\u00FDkonu metod mezi instancemi\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_7_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.openFullscreenChart(ctx_r38.instancesComparisonChart, \"Srovn\\u00E1n\\u00ED v\\u00FDkonu metod mezi instancemi\"));\n    });\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 19);\n    i0.ɵɵtemplate(9, PerformanceComponent_div_7_div_3_div_9_Template, 6, 0, \"div\", 20);\n    i0.ɵɵtemplate(10, PerformanceComponent_div_7_div_3_div_10_Template, 4, 0, \"div\", 21);\n    i0.ɵɵtemplate(11, PerformanceComponent_div_7_div_3_canvas_11_Template, 2, 0, \"canvas\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.renderingInstancesComparisonChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r33.hasInstancesComparisonChartData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.hasInstancesComparisonChartData);\n  }\n}\nfunction PerformanceComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_7_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.onInstancesComparisonFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_7_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_7_div_3_Template, 12, 3, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_instances_comparison\")(\"fields\", ctx_r2.instancesComparisonFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading);\n  }\n}\nfunction PerformanceComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_8_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Zpracov\\u00E1n\\u00ED dat grafu...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_8_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Nejsou k dispozici \\u017E\\u00E1dn\\u00E1 data pro zobrazen\\u00ED grafu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_8_div_3_canvas_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 34, 43);\n  }\n}\nfunction PerformanceComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"h5\", 42);\n    i0.ɵɵtext(4, \"Srovn\\u00E1n\\u00ED v\\u00FDkonu metod mezi verzemi\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_8_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.openFullscreenChart(ctx_r48.versionsComparisonChart, \"Srovn\\u00E1n\\u00ED v\\u00FDkonu metod mezi verzemi\"));\n    });\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 19);\n    i0.ɵɵtemplate(9, PerformanceComponent_div_8_div_3_div_9_Template, 6, 0, \"div\", 20);\n    i0.ɵɵtemplate(10, PerformanceComponent_div_8_div_3_div_10_Template, 4, 0, \"div\", 21);\n    i0.ɵɵtemplate(11, PerformanceComponent_div_8_div_3_canvas_11_Template, 2, 0, \"canvas\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r43.renderingVersionsComparisonChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r43.hasVersionsComparisonChartData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r43.hasVersionsComparisonChartData);\n  }\n}\nfunction PerformanceComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_8_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.onVersionsComparisonFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_8_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_8_div_3_Template, 12, 3, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_versions_comparison\")(\"fields\", ctx_r3.versionsComparisonFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading);\n  }\n}\nfunction PerformanceComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵtext(1, \"Nejpomalej\\u0161\\u00ED metody\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \"Nej\\u010Dast\\u011Bji volan\\u00E9 metody\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \"V\\u0161echny metody\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_42_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\")(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_ng_container_42_tr_1_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r64);\n      const method_r62 = restoredCtx.$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r63.showMethodDetail(method_r62));\n    });\n    i0.ɵɵelement(26, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r62 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r62.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r62.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r62.methodName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r62.totalCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, method_r62.min, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 12, method_r62.max, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 15, method_r62.avg, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, method_r62.median, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, method_r62.percentil95, \"1.0-2\"));\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_42_tr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"\\u017D\\u00E1dn\\u00E9 metody k zobrazen\\u00ED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PerformanceComponent_div_9_div_3_ng_container_42_tr_1_Template, 27, 24, \"tr\", 30);\n    i0.ɵɵtemplate(2, PerformanceComponent_div_9_div_3_ng_container_42_tr_2_Template, 3, 0, \"tr\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r57.slowestMethods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.slowestMethods || ctx_r57.slowestMethods.length === 0);\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_43_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\")(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_ng_container_43_tr_1_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const method_r67 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r68.showMethodDetail(method_r67));\n    });\n    i0.ɵɵelement(26, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r67 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r67.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r67.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r67.methodName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r67.totalCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, method_r67.min, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 12, method_r67.max, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 15, method_r67.avg, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, method_r67.median, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, method_r67.percentil95, \"1.0-2\"));\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_43_tr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"\\u017D\\u00E1dn\\u00E9 metody k zobrazen\\u00ED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PerformanceComponent_div_9_div_3_ng_container_43_tr_1_Template, 27, 24, \"tr\", 30);\n    i0.ɵɵtemplate(2, PerformanceComponent_div_9_div_3_ng_container_43_tr_2_Template, 3, 0, \"tr\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r58.mostCalledMethods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r58.mostCalledMethods || ctx_r58.mostCalledMethods.length === 0);\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_44_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 36)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\")(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_ng_container_44_tr_1_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r74);\n      const method_r72 = restoredCtx.$implicit;\n      const ctx_r73 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r73.showMethodDetail(method_r72));\n    });\n    i0.ɵɵelement(26, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r72 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r72.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r72.className);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r72.methodName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r72.totalCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, method_r72.min, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 12, method_r72.max, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 15, method_r72.avg, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, method_r72.median, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 21, method_r72.percentil95, \"1.0-2\"));\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_44_tr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"\\u017D\\u00E1dn\\u00E9 metody k zobrazen\\u00ED\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_9_div_3_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PerformanceComponent_div_9_div_3_ng_container_44_tr_1_Template, 27, 24, \"tr\", 30);\n    i0.ɵɵtemplate(2, PerformanceComponent_div_9_div_3_ng_container_44_tr_2_Template, 3, 0, \"tr\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r59.allMethods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r59.allMethods || ctx_r59.allMethods.length === 0);\n  }\n}\nfunction PerformanceComponent_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"div\", 14)(3, \"h5\", 44);\n    i0.ɵɵtemplate(4, PerformanceComponent_div_9_div_3_span_4_Template, 2, 0, \"span\", 45);\n    i0.ɵɵtemplate(5, PerformanceComponent_div_9_div_3_span_5_Template, 2, 0, \"span\", 46);\n    i0.ɵɵtemplate(6, PerformanceComponent_div_9_div_3_span_6_Template, 2, 0, \"span\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 24)(9, \"table\", 25)(10, \"thead\", 26)(11, \"tr\", 27)(12, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_12_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r75.onSort(\"instanceName\"));\n    });\n    i0.ɵɵtext(13, \" Instance \");\n    i0.ɵɵelement(14, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_15_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r77 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r77.onSort(\"className\"));\n    });\n    i0.ɵɵtext(16, \" T\\u0159\\u00EDda \");\n    i0.ɵɵelement(17, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_18_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.onSort(\"methodName\"));\n    });\n    i0.ɵɵtext(19, \" Metoda \");\n    i0.ɵɵelement(20, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_21_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r79 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r79.onSort(\"totalCount\"));\n    });\n    i0.ɵɵtext(22, \" Po\\u010Det vol\\u00E1n\\u00ED \");\n    i0.ɵɵelement(23, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_24_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r80 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r80.onSort(\"min\"));\n    });\n    i0.ɵɵtext(25, \" Min (ms) \");\n    i0.ɵɵelement(26, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_27_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r81 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r81.onSort(\"max\"));\n    });\n    i0.ɵɵtext(28, \" Max (ms) \");\n    i0.ɵɵelement(29, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_30_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r82 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r82.onSort(\"avg\"));\n    });\n    i0.ɵɵtext(31, \" Pr\\u016Fm\\u011Br (ms) \");\n    i0.ɵɵelement(32, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_33_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r83 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r83.onSort(\"median\"));\n    });\n    i0.ɵɵtext(34, \" Medi\\u00E1n (ms) \");\n    i0.ɵɵelement(35, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\", 28);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_9_div_3_Template_th_click_36_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r84 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r84.onSort(\"percentil95\"));\n    });\n    i0.ɵɵtext(37, \" 95. percentil (ms) \");\n    i0.ɵɵelement(38, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"th\");\n    i0.ɵɵtext(40, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"tbody\");\n    i0.ɵɵtemplate(42, PerformanceComponent_div_9_div_3_ng_container_42_Template, 3, 2, \"ng-container\", 4);\n    i0.ɵɵtemplate(43, PerformanceComponent_div_9_div_3_ng_container_43_Template, 3, 2, \"ng-container\", 4);\n    i0.ɵɵtemplate(44, PerformanceComponent_div_9_div_3_ng_container_44_Template, 3, 2, \"ng-container\", 4);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"slowest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"most-called\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"all\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"instanceName\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"instanceName\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"instanceName\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"className\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"className\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"className\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"methodName\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"methodName\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"methodName\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"totalCount\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"totalCount\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"totalCount\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"min\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"min\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"min\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"max\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"max\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"max\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"avg\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"avg\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"avg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"median\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"median\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"median\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r53.sortColumn === \"percentil95\" && ctx_r53.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r53.sortColumn === \"percentil95\" && ctx_r53.sortDirection === \"desc\")(\"bi-sort\", ctx_r53.sortColumn !== \"percentil95\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"slowest\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"most-called\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.methodsDisplayType === \"all\");\n  }\n}\nfunction PerformanceComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_9_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r86);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.onMethodsFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_9_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_9_div_3_Template, 45, 60, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_methods\")(\"fields\", ctx_r4.methodsFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.loading);\n  }\n}\nfunction PerformanceComponent_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED dat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_10_div_3_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 12);\n    i0.ɵɵtext(5, \"Zpracov\\u00E1n\\u00ED dat grafu...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_10_div_3_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Nejsou k dispozici \\u017E\\u00E1dn\\u00E1 data pro zobrazen\\u00ED grafu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PerformanceComponent_div_10_div_3_canvas_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 34, 62);\n  }\n}\nfunction PerformanceComponent_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r94 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 51)(2, \"div\", 52)(3, \"div\", 53)(4, \"div\", 54)(5, \"h5\", 55);\n    i0.ɵɵtext(6, \"Pr\\u016Fm\\u011Brn\\u00E1 doba odezvy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 56);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 57);\n    i0.ɵɵtext(11, \"ms\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 52)(13, \"div\", 53)(14, \"div\", 54)(15, \"h5\", 58);\n    i0.ɵɵtext(16, \"Medi\\u00E1n doby odezvy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 56);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 57);\n    i0.ɵɵtext(21, \"ms\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 52)(23, \"div\", 53)(24, \"div\", 54)(25, \"h5\", 59);\n    i0.ɵɵtext(26, \"95. percentil\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 56);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 57);\n    i0.ɵɵtext(31, \"ms\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 52)(33, \"div\", 53)(34, \"div\", 54)(35, \"h5\", 60);\n    i0.ɵɵtext(36, \"Celkov\\u00FD po\\u010Det vol\\u00E1n\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\", 56);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\", 57);\n    i0.ɵɵtext(40, \"za zvolen\\u00E9 obdob\\u00ED\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(41, \"div\", 13)(42, \"div\", 14)(43, \"h5\", 61);\n    i0.ɵɵtext(44, \"V\\u00FDkon metody v \\u010Dase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function PerformanceComponent_div_10_div_3_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r94);\n      const ctx_r93 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r93.openFullscreenChart(ctx_r93.methodDetailChart, \"V\\u00FDkon metody v \\u010Dase\"));\n    });\n    i0.ɵɵelement(46, \"i\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 18)(48, \"div\", 19);\n    i0.ɵɵtemplate(49, PerformanceComponent_div_10_div_3_div_49_Template, 6, 0, \"div\", 20);\n    i0.ɵɵtemplate(50, PerformanceComponent_div_10_div_3_div_50_Template, 4, 0, \"div\", 21);\n    i0.ɵɵtemplate(51, PerformanceComponent_div_10_div_3_canvas_51_Template, 2, 0, \"canvas\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r88 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 7, ctx_r88.methodDetailData.overallStats == null ? null : ctx_r88.methodDetailData.overallStats.avg, \"1.0-2\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 10, ctx_r88.methodDetailData.overallStats == null ? null : ctx_r88.methodDetailData.overallStats.median, \"1.0-2\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 13, ctx_r88.methodDetailData.overallStats == null ? null : ctx_r88.methodDetailData.overallStats.percentil95, \"1.0-2\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r88.methodDetailData.overallStats == null ? null : ctx_r88.methodDetailData.overallStats.totalCount);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r88.renderingMethodDetailChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r88.hasMethodDetailChartData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r88.hasMethodDetailChartData);\n  }\n}\nfunction PerformanceComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-advanced-filter\", 7);\n    i0.ɵɵlistener(\"filterChange\", function PerformanceComponent_div_10_Template_app_advanced_filter_filterChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r95.onMethodDetailFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, PerformanceComponent_div_10_div_2_Template, 6, 0, \"div\", 8);\n    i0.ɵɵtemplate(3, PerformanceComponent_div_10_div_3_Template, 52, 16, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"entityType\", \"performance_method_detail\")(\"fields\", ctx_r5.methodDetailFilterFields);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.loading && ctx_r5.methodDetailData);\n  }\n}\n// Registrace všech komponent Chart.js\nChart.register(...registerables);\nexport class PerformanceComponent {\n  constructor(performanceService, instanceService, datePipe, chartModalService, changeDetectorRef, tableSortingService, textUtils) {\n    this.performanceService = performanceService;\n    this.instanceService = instanceService;\n    this.datePipe = datePipe;\n    this.chartModalService = chartModalService;\n    this.changeDetectorRef = changeDetectorRef;\n    this.tableSortingService = tableSortingService;\n    this.textUtils = textUtils;\n    // Grafy\n    this.aggregatedPerformanceChart = null;\n    this.instancesComparisonChart = null;\n    this.versionsComparisonChart = null;\n    this.methodDetailChart = null;\n    // Stav vykreslování grafů\n    this.renderingAggregatedChart = false;\n    this.renderingInstancesComparisonChart = false;\n    this.renderingVersionsComparisonChart = false;\n    this.renderingMethodDetailChart = false;\n    // Indikátory, zda jsou k dispozici data pro grafy\n    this.hasAggregatedChartData = true;\n    this.hasInstancesComparisonChartData = true;\n    this.hasVersionsComparisonChartData = true;\n    this.hasMethodDetailChartData = true;\n    // Data\n    this.slowestMethods = [];\n    this.mostCalledMethods = [];\n    this.allMethods = [];\n    this.selectedMethod = null;\n    this.methodDetailData = null;\n    this.instances = [];\n    this.aggregatedPerformanceData = null;\n    this.instancesComparisonData = null;\n    this.versionsComparisonData = null;\n    this.methodDetailChartData = null;\n    // Typ metod pro zobrazení v záložce \"Metody\"\n    this.methodsDisplayType = 'slowest';\n    // Řazení\n    this.sortColumn = 'percentil95';\n    this.sortDirection = 'desc';\n    // Pole pro filtry\n    this.aggregatedFilterFields = [];\n    this.instancesComparisonFilterFields = [];\n    this.versionsComparisonFilterFields = [];\n    this.methodsFilterFields = [];\n    this.methodDetailFilterFields = [];\n    // Stav komponenty\n    this.loading = true;\n    this.error = null;\n    this.activeTab = 'aggregated';\n    this.subscriptions = [];\n    // Definice záložek\n    this.tabs = [{\n      id: 'aggregated',\n      label: 'Agregovaný výkon'\n    }, {\n      id: 'instances-comparison',\n      label: 'Srovnání instancí'\n    }, {\n      id: 'versions-comparison',\n      label: 'Srovnání verzí'\n    }, {\n      id: 'methods',\n      label: 'Metody'\n    }, {\n      id: 'method-detail',\n      label: 'Detail metody'\n    }];\n    // Filtry\n    this.aggregatedFilterData = {\n      period: 7\n    };\n    this.instancesComparisonFilterData = {\n      instanceIds: [],\n      period: 7,\n      className: '',\n      methodName: ''\n    };\n    this.versionsComparisonFilterData = {\n      period: 90,\n      className: '',\n      methodName: ''\n    };\n    this.methodDetailFilterData = {\n      period: 30,\n      className: '',\n      methodName: ''\n    };\n  }\n  ngOnInit() {\n    console.log('PerformanceComponent - ngOnInit');\n    // Nejprve inicializujeme prázdná pole filtrů, aby se komponenta advanced-filter správně inicializovala\n    this.initEmptyFilterFields();\n    // Načtení uloženého stavu třídění\n    this.loadSortingState();\n    // Načtení poslední aktivní záložky z localStorage\n    const lastActiveTab = this.loadActiveTab();\n    if (lastActiveTab) {\n      console.log(`ngOnInit - Načtena poslední aktivní záložka: ${lastActiveTab}`);\n      this.activeTab = lastActiveTab;\n    }\n    // Inicializace výchozích hodnot filtru\n    this.aggregatedFilterData = {\n      instanceId: undefined,\n      period: '7',\n      fromDate: undefined,\n      toDate: undefined,\n      methodsDisplayType: 'slowest'\n    };\n    // Načtení instancí - toto musí proběhnout jako první\n    this.instanceService.getAll().subscribe({\n      next: data => {\n        console.log('ngOnInit - Instance úspěšně načteny:', data);\n        this.instances = data;\n        // Po načtení instancí inicializujeme pole filtrů\n        this.initFilterFields();\n        // Načtení posledního filtru pro aktuální záložku\n        this.loadLastFilter('performance_aggregated');\n        // Přímé volání načtení dat až po načtení instancí\n        console.log('ngOnInit - Volám API pro načtení dat po načtení instancí');\n        // Přímé volání API s použitím uloženého filtru\n        setTimeout(() => {\n          console.log('ngOnInit - Volám API přímo s použitím uloženého filtru:', this.aggregatedFilterData);\n          this.loading = true;\n          // Příprava parametrů pro volání API\n          let instanceId = this.aggregatedFilterData.instanceId;\n          if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n            instanceId = undefined;\n          }\n          let fromDate;\n          let toDate = new Date();\n          // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n          if (!this.aggregatedFilterData.period) {\n            fromDate = new Date();\n            fromDate.setDate(toDate.getDate() - 7); // Výchozí hodnota 7 dní\n          }\n          // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n          else if (this.aggregatedFilterData.period !== 'custom') {\n            fromDate = new Date();\n            fromDate.setDate(toDate.getDate() - Number(this.aggregatedFilterData.period));\n          }\n          // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n          else {\n            // Kontrola, zda fromDate a toDate jsou validní\n            if (this.aggregatedFilterData.fromDate) {\n              fromDate = new Date(this.aggregatedFilterData.fromDate);\n            } else {\n              // Výchozí hodnota, pokud fromDate není zadáno\n              fromDate = new Date();\n              fromDate.setDate(toDate.getDate() - 7);\n            }\n            if (this.aggregatedFilterData.toDate) {\n              toDate = new Date(this.aggregatedFilterData.toDate);\n            }\n          }\n          console.log('ngOnInit - Parametry pro volání API:', {\n            instanceId,\n            fromDate,\n            toDate\n          });\n          const subscription = this.performanceService.getAggregatedPerformance(instanceId, fromDate, toDate).subscribe({\n            next: data => {\n              console.log('ngOnInit - Data úspěšně načtena přímo:', data);\n              // Uložení dat pro pozdější vykreslení\n              this.aggregatedPerformanceData = data;\n              // Vykreslení grafu\n              if (this.aggregatedPerformanceChartRef) {\n                this.renderAggregatedPerformanceChart(data);\n              }\n              this.loading = false;\n              // Načtení dat pro záložku \"Metody\"\n              setTimeout(() => {\n                this.loadSlowestMethods();\n              }, 100);\n            },\n            error: error => {\n              console.error('ngOnInit - Chyba při přímém načítání dat:', error);\n              this.loading = false;\n            }\n          });\n          this.subscriptions.push(subscription);\n        }, 500);\n      },\n      error: error => {\n        console.error('ngOnInit - Chyba při načítání instancí:', error);\n      }\n    });\n  }\n  ngAfterViewInit() {\n    console.log('PerformanceComponent - ngAfterViewInit');\n    // Inicializace popoverů pro nápovědu\n    this.initPopovers();\n    // Inicializace výchozího nastavení viditelnosti metrik\n    this.initDefaultMetricsVisibility();\n    // Načtení dat pro aktivní záložku\n    setTimeout(() => {\n      // Simulujeme změnu záložky, aby se načetla data pro aktivní záložku\n      this.changeTab(this.activeTab);\n    }, 500);\n    // Pokud již máme data, zkusíme vykreslit graf\n    if (this.activeTab === 'aggregated' && this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n      console.log('PerformanceComponent - Mám data, zkusím vykreslit graf');\n      setTimeout(() => {\n        this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n      }, 500);\n    } else {\n      console.log('PerformanceComponent - Nemám data nebo canvas element, zkusím později');\n      // Přidáme event listener pro změnu záložky, abychom mohli vykreslit graf, až bude canvas element k dispozici\n      setTimeout(() => {\n        if (this.activeTab === 'aggregated' && this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n          console.log('PerformanceComponent - Mám data a canvas element, zkusím vykreslit graf po timeoutu');\n          this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n        }\n      }, 2000);\n    }\n  }\n  /**\r\n   * Inicializace výchozího nastavení viditelnosti metrik\r\n   */\n  initDefaultMetricsVisibility() {\n    // Výchozí nastavení viditelnosti metrik pro agregovaný výkon\n    if (!localStorage.getItem('metrics_visibility_aggregated')) {\n      const aggregatedVisibility = {\n        'Průměr (ms)': true,\n        'Medián (ms)': true,\n        '95. percentil (ms)': true,\n        '99. percentil (ms)': false,\n        'Minimum (ms)': false,\n        'Maximum (ms)': false,\n        'Počet volání': false\n      };\n      localStorage.setItem('metrics_visibility_aggregated', JSON.stringify(aggregatedVisibility));\n    }\n    // Výchozí nastavení viditelnosti metrik pro detail metody\n    if (!localStorage.getItem('metrics_visibility_method-detail')) {\n      const methodDetailVisibility = {\n        'Průměr (ms)': true,\n        'Medián (ms)': true,\n        '95. percentil (ms)': true,\n        'Počet volání': false\n      };\n      localStorage.setItem('metrics_visibility_method-detail', JSON.stringify(methodDetailVisibility));\n    }\n    // Pro srovnání instancí a verzí nemůžeme nastavit výchozí hodnoty,\n    // protože názvy datasetů závisí na datech z API\n  }\n  /**\r\n   * Inicializace prázdných polí filtrů\r\n   */\n  initEmptyFilterFields() {\n    // Základní pole pro všechny filtry\n    const periodField = {\n      name: 'period',\n      label: 'Časové období',\n      type: 'select',\n      options: [{\n        value: 1,\n        label: '1 den'\n      }, {\n        value: 7,\n        label: '7 dní'\n      }, {\n        value: 30,\n        label: '30 dní'\n      }, {\n        value: 90,\n        label: '90 dní'\n      }, {\n        value: 'custom',\n        label: 'Vlastní období'\n      }]\n    };\n    // Definice polí pro datum od a do\n    const fromDateField = {\n      name: 'fromDate',\n      label: 'Od',\n      type: 'date',\n      visible: values => values.period === 'custom',\n      required: values => values.period === 'custom',\n      errorMessage: 'Zadejte počáteční datum'\n    };\n    const toDateField = {\n      name: 'toDate',\n      label: 'Do',\n      type: 'date',\n      visible: values => values.period === 'custom',\n      required: values => values.period === 'custom',\n      errorMessage: 'Zadejte koncové datum'\n    };\n    // Inicializace prázdných polí filtrů\n    this.aggregatedFilterFields = [{\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [{\n        value: null,\n        label: 'Všechny instance'\n      }]\n    }, periodField, fromDateField, toDateField];\n    this.instancesComparisonFilterFields = [{\n      name: 'instanceIds',\n      label: 'Instance',\n      type: 'multiselect',\n      options: [],\n      required: true,\n      validators: [minArrayLength(2)],\n      errorMessage: 'Vyberte alespoň dvě instance pro srovnání'\n    }, {\n      name: 'className',\n      label: 'Třída',\n      type: 'text'\n    }, {\n      name: 'methodName',\n      label: 'Metoda',\n      type: 'text'\n    }, periodField, fromDateField, toDateField];\n    this.versionsComparisonFilterFields = [{\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [{\n        value: null,\n        label: 'Vyberte instanci'\n      }],\n      required: true,\n      errorMessage: 'Vyberte instanci pro srovnání verzí'\n    }, {\n      name: 'className',\n      label: 'Třída',\n      type: 'text'\n    }, {\n      name: 'methodName',\n      label: 'Metoda',\n      type: 'text'\n    }, periodField, fromDateField, toDateField];\n    this.methodsFilterFields = [{\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [{\n        value: null,\n        label: 'Všechny instance'\n      }]\n    }, {\n      name: 'methodsDisplayType',\n      label: 'Metrika',\n      type: 'select',\n      options: [{\n        value: 'slowest',\n        label: 'Nejpomalejší metody'\n      }, {\n        value: 'most-called',\n        label: 'Nejčastěji volané metody'\n      }, {\n        value: 'all',\n        label: 'Všechny metody'\n      }]\n    }, periodField, fromDateField, toDateField];\n    this.methodDetailFilterFields = [{\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [{\n        value: 0,\n        label: 'Všechny instance'\n      }],\n      required: false\n    }, {\n      name: 'className',\n      label: 'Třída',\n      type: 'text',\n      required: true,\n      errorMessage: 'Zadejte název třídy'\n    }, {\n      name: 'methodName',\n      label: 'Metoda',\n      type: 'text',\n      required: true,\n      errorMessage: 'Zadejte název metody'\n    }, periodField, fromDateField, toDateField];\n  }\n  /**\r\n   * Inicializace polí filtrů\r\n   */\n  initFilterFields() {\n    // Základní pole pro všechny filtry\n    const periodField = {\n      name: 'period',\n      label: 'Časové období',\n      type: 'select',\n      options: [{\n        value: 1,\n        label: '1 den'\n      }, {\n        value: 7,\n        label: '7 dní'\n      }, {\n        value: 30,\n        label: '30 dní'\n      }, {\n        value: 90,\n        label: '90 dní'\n      }, {\n        value: 'custom',\n        label: 'Vlastní období'\n      }]\n    };\n    // Aktualizace pole pro filtr agregovaných metrik\n    const instanceOptions = [{\n      value: null,\n      label: 'Všechny instance'\n    }, ...this.instances.sort((a, b) => {\n      // Nejprve seřadíme podle zkratky zákazníka\n      const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n      // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n      return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n    }).map(instance => ({\n      value: instance.id,\n      label: `${instance.customerAbbreviation} - ${instance.name}`\n    }))];\n    // Aktualizace pole pro filtr agregovaných metrik\n    this.updateFilterField(this.aggregatedFilterFields, 0, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: instanceOptions\n    });\n    // Aktualizace pole pro filtr srovnání instancí\n    this.updateFilterField(this.instancesComparisonFilterFields, 0, {\n      name: 'instanceIds',\n      label: 'Instance',\n      type: 'multiselect',\n      options: this.instances.sort((a, b) => {\n        // Nejprve seřadíme podle zkratky zákazníka\n        const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n        // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n        return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n      }).map(instance => ({\n        value: instance.id,\n        label: `${instance.customerAbbreviation} - ${instance.name}`\n      })),\n      required: true,\n      validators: [minArrayLength(2)],\n      errorMessage: 'Vyberte alespoň dvě instance pro srovnání'\n    });\n    // Aktualizace pole pro filtr srovnání verzí\n    this.updateFilterField(this.versionsComparisonFilterFields, 0, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [{\n        value: null,\n        label: 'Vyberte instanci'\n      }, ...this.instances.sort((a, b) => {\n        // Nejprve seřadíme podle zkratky zákazníka\n        const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n        // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n        return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n      }).map(instance => ({\n        value: instance.id,\n        label: `${instance.customerAbbreviation} - ${instance.name}`\n      }))],\n      required: true,\n      errorMessage: 'Vyberte instanci pro srovnání verzí'\n    });\n    // Aktualizace pole pro filtr metod\n    this.updateFilterField(this.methodsFilterFields, 0, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: instanceOptions\n    });\n    // Aktualizace pole pro filtr detailu metody\n    this.updateFilterField(this.methodDetailFilterFields, 0, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [{\n        value: 0,\n        label: 'Všechny instance'\n      }, ...this.instances.sort((a, b) => {\n        // Nejprve seřadíme podle zkratky zákazníka\n        const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n        // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n        return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n      }).map(instance => ({\n        value: instance.id,\n        label: `${instance.customerAbbreviation} - ${instance.name}`\n      }))]\n    });\n    // Aktualizace pole period pro všechny filtry\n    this.updateFilterField(this.aggregatedFilterFields, 1, periodField);\n    this.updateFilterField(this.instancesComparisonFilterFields, 3, periodField);\n    this.updateFilterField(this.versionsComparisonFilterFields, 3, periodField);\n    this.updateFilterField(this.methodsFilterFields, 2, periodField); // Index 2, protože na indexu 1 je methodsDisplayType\n    this.updateFilterField(this.methodDetailFilterFields, 3, periodField);\n  }\n  /**\r\n   * Aktualizace pole filtru\r\n   */\n  updateFilterField(filterFields, index, newField) {\n    if (filterFields && filterFields.length > index) {\n      // Zachováme validátory a další vlastnosti z původního pole\n      const originalField = filterFields[index];\n      filterFields[index] = {\n        ...originalField,\n        ...newField\n      };\n      console.log(`Aktualizace pole ${newField.name}:`, filterFields[index]);\n    }\n  }\n  /**\r\n   * Načtení seznamu instancí\r\n   */\n  loadInstances() {\n    console.log('loadInstances - Načítám seznam instancí');\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadInstances - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    this.loading = true;\n    this.subscriptions.push(this.instanceService.getAll().subscribe({\n      next: data => {\n        console.log('loadInstances - Data úspěšně načtena:', data);\n        this.instances = data;\n        // Po načtení instancí inicializujeme pole filtrů\n        this.initFilterFields();\n        // Načtení posledního filtru pro aktuální záložku\n        this.loadLastFilter('performance_aggregated');\n        // Již nebudeme načítat data zde, protože to děláme přímo v ngOnInit\n        console.log('loadInstances - Data instancí načtena, filtry inicializovány');\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Chyba při načítání instancí:', error);\n        this.loading = false;\n      }\n    }));\n  }\n  /**\r\n   * Změna časového období\r\n   */\n  onPeriodChange() {\n    // Tato metoda je volána při změně období v select boxu\n    // Není potřeba implementovat žádnou logiku, protože\n    // zobrazení/skrytí polí od-do je řešeno pomocí *ngIf v šabloně\n  }\n  ngOnDestroy() {\n    // Zrušení všech aktivních subscriptions\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  /**\r\n   * Otevře modální okno s grafem v režimu \"full screen\"\r\n   * @param chart Instance grafu nebo reference na canvas element\r\n   * @param title Titulek grafu\r\n   */\n  openFullscreenChart(chart, title) {\n    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart\n    if (chart instanceof HTMLCanvasElement) {\n      // Najdeme instanci Chart pro daný canvas element\n      const chartInstance = Chart.getChart(chart);\n      this.chartModalService.openChartModal(chartInstance || null, title);\n    } else {\n      this.chartModalService.openChartModal(chart, title);\n    }\n  }\n  /**\r\n   * Změna aktivní záložky\r\n   */\n  changeTab(tab) {\n    // Resetujeme chybovou hlášku při změně záložky\n    this.error = '';\n    // Spustíme detekci změn, aby se změna proměnné error projevila okamžitě\n    this.changeDetectorRef.detectChanges();\n    // Převod parametru na string, pokud není\n    const tabId = typeof tab === 'string' ? tab : String(tab);\n    this.activeTab = tabId;\n    // Uložení aktivní záložky do localStorage\n    this.saveActiveTab(tabId);\n    // Pokud je vybrána záložka \"method-detail\", přidáme ji do seznamu záložek\n    if (tabId === 'method-detail' && this.selectedMethod) {\n      // Kontrola, zda záložka již existuje\n      const tabExists = this.tabs.some(t => t.id === 'method-detail');\n      if (!tabExists) {\n        this.tabs.push({\n          id: 'method-detail',\n          label: 'Detail metody'\n        });\n      }\n    }\n    // Načtení dat pro vybranou záložku\n    switch (tabId) {\n      case 'aggregated':\n        // Načtení posledního filtru z localStorage\n        this.loadLastFilter('performance_aggregated');\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku aggregated');\n            // Pokud již máme data, zkusíme vykreslit graf\n            if (this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n              console.log('onTabChange - Mám data, zkusím vykreslit graf');\n              this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n            } else {\n              // Jinak načteme nová data\n              this.loadAggregatedPerformance();\n            }\n          }, 500);\n        });\n        break;\n      case 'instances-comparison':\n        // Načtení posledního filtru z localStorage\n        this.loadLastFilter('performance_instances_comparison');\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku instances-comparison');\n            // Pokud již máme data, zkusíme vykreslit graf\n            if (this.instancesComparisonData && this.instancesComparisonChartRef) {\n              console.log('onTabChange - Mám data, zkusím vykreslit graf');\n              this.renderInstancesComparisonChart(this.instancesComparisonData);\n            } else {\n              // Jinak načteme nová data\n              this.loadInstancesComparison();\n            }\n          }, 500);\n        });\n        break;\n      case 'versions-comparison':\n        // Načtení posledního filtru z localStorage\n        this.loadLastFilter('performance_versions_comparison');\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku versions-comparison');\n            // Pokud již máme data, zkusíme vykreslit graf\n            if (this.versionsComparisonData && this.versionsComparisonChartRef) {\n              console.log('onTabChange - Mám data, zkusím vykreslit graf');\n              this.renderVersionsComparisonChart(this.versionsComparisonData);\n            } else {\n              // Jinak načteme nová data\n              this.loadVersionsComparison();\n            }\n          }, 500);\n        });\n        break;\n      case 'methods':\n        // Načtení posledního filtru z localStorage\n        const lastFilterKey = `last_filter_performance_methods`;\n        const lastFilterJson = localStorage.getItem(lastFilterKey);\n        // Pokud není uložen žádný filtr, resetujeme instanceId na undefined\n        if (!lastFilterJson) {\n          console.log('onTabChange - Není uložen žádný filtr pro záložku methods, resetuji instanceId na undefined');\n          this.aggregatedFilterData.instanceId = undefined;\n        }\n        this.loadLastFilter('performance_methods');\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku methods, instanceId:', this.aggregatedFilterData.instanceId);\n            this.loadMethodsByType();\n          }, 100);\n        });\n        break;\n      case 'method-detail':\n        // Načtení posledního filtru z localStorage\n        this.loadLastFilter('performance_method_detail');\n        // Resetujeme indikátor, zda jsou k dispozici data pro graf\n        this.hasMethodDetailChartData = true;\n        // Pokud máme vybranou metodu, použijeme její hodnoty pro filtr\n        if (this.selectedMethod) {\n          console.log('onTabChange - Použití hodnot z vybrané metody pro filtr:', this.selectedMethod);\n          // Zjištění aktuální hodnoty instanceId\n          let instanceId = this.selectedMethod.instanceId;\n          // Pokud instanceId je null nebo undefined, použijeme 0 (všechny instance)\n          if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n            instanceId = 0;\n          }\n          // Aktualizace hodnot filtru\n          this.methodDetailFilterData = {\n            ...this.methodDetailFilterData,\n            instanceId: instanceId,\n            className: this.selectedMethod.className,\n            methodName: this.selectedMethod.methodName\n          };\n          // Uložení filtru do localStorage\n          this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n        }\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku method-detail');\n            // Vždy načteme nová data při přepnutí na záložku\n            this.loadMethodDetail();\n          }, 500);\n        });\n        break;\n    }\n    // Reinicializace popoverů po změně záložky\n    setTimeout(() => {\n      this.initPopovers();\n    }, 500);\n  }\n  /**\r\n   * Načtení posledního filtru z localStorage\r\n   */\n  loadLastFilter(entityType) {\n    console.log(`loadLastFilter - Načítám poslední filtr pro ${entityType}`);\n    try {\n      const lastFilterKey = `last_filter_${entityType}`;\n      const lastFilterJson = localStorage.getItem(lastFilterKey);\n      if (lastFilterJson) {\n        const lastFilter = JSON.parse(lastFilterJson);\n        console.log(`Načten poslední filtr pro ${entityType} z localStorage:`, lastFilter);\n        // Aktualizace příslušného filtru podle typu entity\n        switch (entityType) {\n          case 'performance_aggregated':\n            // Kontrola, zda se filtr skutečně změnil\n            const currentAggregatedFilterStr = JSON.stringify(this.aggregatedFilterData);\n            const newAggregatedFilterStr = JSON.stringify({\n              ...this.aggregatedFilterData,\n              ...lastFilter\n            });\n            if (currentAggregatedFilterStr !== newAggregatedFilterStr) {\n              this.aggregatedFilterData = {\n                ...this.aggregatedFilterData,\n                ...lastFilter\n              };\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.aggregatedFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n          case 'performance_instances_comparison':\n            // Kontrola, zda se filtr skutečně změnil\n            const currentInstancesComparisonFilterStr = JSON.stringify(this.instancesComparisonFilterData);\n            const newInstancesComparisonFilterStr = JSON.stringify({\n              ...this.instancesComparisonFilterData,\n              ...lastFilter\n            });\n            if (currentInstancesComparisonFilterStr !== newInstancesComparisonFilterStr) {\n              this.instancesComparisonFilterData = {\n                ...this.instancesComparisonFilterData,\n                ...lastFilter\n              };\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.instancesComparisonFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n          case 'performance_versions_comparison':\n            // Kontrola, zda se filtr skutečně změnil\n            const currentVersionsComparisonFilterStr = JSON.stringify(this.versionsComparisonFilterData);\n            const newVersionsComparisonFilterStr = JSON.stringify({\n              ...this.versionsComparisonFilterData,\n              ...lastFilter\n            });\n            if (currentVersionsComparisonFilterStr !== newVersionsComparisonFilterStr) {\n              this.versionsComparisonFilterData = {\n                ...this.versionsComparisonFilterData,\n                ...lastFilter\n              };\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.versionsComparisonFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n          case 'performance_methods':\n            // Pro metody používáme stejný filtr jako pro agregované metriky\n            // Kontrola, zda se filtr skutečně změnil\n            const currentMethodsFilterStr = JSON.stringify(this.aggregatedFilterData);\n            const newMethodsFilterStr = JSON.stringify({\n              ...this.aggregatedFilterData,\n              ...lastFilter\n            });\n            if (currentMethodsFilterStr !== newMethodsFilterStr) {\n              this.aggregatedFilterData = {\n                ...this.aggregatedFilterData,\n                ...lastFilter\n              };\n              // Aktualizace typu zobrazovaných metod\n              if (lastFilter.methodsDisplayType) {\n                this.methodsDisplayType = lastFilter.methodsDisplayType;\n              }\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.aggregatedFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n          case 'performance_method_detail':\n            // Kontrola, zda se filtr skutečně změnil\n            const currentMethodDetailFilterStr = JSON.stringify(this.methodDetailFilterData);\n            const newMethodDetailFilterStr = JSON.stringify({\n              ...this.methodDetailFilterData,\n              ...lastFilter\n            });\n            if (currentMethodDetailFilterStr !== newMethodDetailFilterStr) {\n              this.methodDetailFilterData = {\n                ...this.methodDetailFilterData,\n                ...lastFilter\n              };\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.methodDetailFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n        }\n      }\n    } catch (error) {\n      console.error(`Chyba při načítání posledního filtru pro ${entityType} z localStorage`, error);\n    }\n  }\n  /**\r\n   * Načtení agregovaných výkonnostních metrik\r\n   */\n  loadAggregatedPerformance() {\n    console.log('loadAggregatedPerformance - Začínám načítat data');\n    // Přidáme kontrolu, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadAggregatedPerformance - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.aggregatedPerformanceChartRef) {\n      console.warn('loadAggregatedPerformance - Reference na canvas element není k dispozici, ale pokračuji v načítání dat');\n    } else {\n      console.log('loadAggregatedPerformance - Reference na canvas element je k dispozici');\n    }\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadAggregatedPerformance - Instance nejsou načteny, načítám je');\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: data => {\n          console.log('loadAggregatedPerformance - Instance úspěšně načteny:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n          // Pokračujeme v načítání dat\n          this.loadAggregatedPerformance();\n        },\n        error: error => {\n          console.error('loadAggregatedPerformance - Chyba při načítání instancí:', error);\n          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n          this.loading = false;\n        }\n      });\n      return;\n    }\n    // Kontrola, zda máme inicializovaný filtr\n    if (!this.aggregatedFilterData) {\n      console.warn('loadAggregatedPerformance - Filtr není inicializován, inicializuji výchozí hodnoty');\n      this.aggregatedFilterData = {\n        instanceId: undefined,\n        period: '7',\n        fromDate: undefined,\n        toDate: undefined\n      };\n    }\n    // Příprava parametrů\n    // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme undefined\n    let instanceId = this.aggregatedFilterData.instanceId;\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n      instanceId = undefined;\n    }\n    console.log('loadAggregatedPerformance - instanceId:', instanceId);\n    let fromDate;\n    let toDate;\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.aggregatedFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.aggregatedFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.aggregatedFilterData.fromDate) {\n        fromDate = new Date(this.aggregatedFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n      if (this.aggregatedFilterData.toDate) {\n        toDate = new Date(this.aggregatedFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n    console.log('loadAggregatedPerformance - Volám API pro načtení dat', {\n      instanceId,\n      fromDate,\n      toDate\n    });\n    try {\n      const subscription = this.performanceService.getAggregatedPerformance(instanceId, fromDate, toDate).subscribe({\n        next: data => {\n          console.log('loadAggregatedPerformance - Data úspěšně načtena:', data);\n          if (!data) {\n            console.error('loadAggregatedPerformance - API vrátilo prázdná data');\n            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n            this.loading = false;\n            return;\n          }\n          // Kontrola, zda data obsahují potřebné údaje\n          if (!data.labels && !data.dailyData) {\n            console.warn('loadAggregatedPerformance - Data neobsahují labels ani dailyData, pokusím se je zpracovat');\n          }\n          // Uložení dat pro pozdější vykreslení\n          this.aggregatedPerformanceData = data;\n          // Vykreslení grafu\n          this.renderAggregatedPerformanceChart(data);\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Chyba při načítání agregovaných výkonnostních metrik:', error);\n          this.error = 'Nepodařilo se načíst agregované výkonnostní metriky. ' + (error.message ? error.message : 'Zkuste to prosím znovu.');\n          this.loading = false;\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadAggregatedPerformance - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadAggregatedPerformance - Opakuji volání API');\n              this.loadAggregatedPerformance();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadAggregatedPerformance - Volání API dokončeno');\n        }\n      });\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadAggregatedPerformance - Chyba při volání API:', error);\n      this.error = 'Nepodařilo se načíst agregované výkonnostní metriky. Zkuste to prosím znovu.';\n      this.loading = false;\n    }\n    // Načtení nejpomalejších metod - pouze pokud je aktivní záložka 'aggregated'\n    if (this.activeTab === 'aggregated') {\n      // Použijeme setTimeout, abychom zajistili, že se volání API nepřekrývají\n      setTimeout(() => {\n        this.loadSlowestMethods();\n      }, 100);\n    }\n  }\n  /**\r\n   * Zpracování změny filtru pro agregované metriky\r\n   */\n  onAggregatedFilterChange(filterData) {\n    console.log('onAggregatedFilterChange - Přijatá data filtru:', filterData);\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.aggregatedFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onAggregatedFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    }\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onAggregatedFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    this.aggregatedFilterData = filterData;\n    console.log('onAggregatedFilterChange - Zpracovaná data filtru:', this.aggregatedFilterData);\n    // Použijeme setTimeout, abychom zajistili, že se volání API nepřekrývají\n    setTimeout(() => {\n      this.loadAggregatedPerformance();\n    }, 0);\n  }\n  /**\r\n   * Načtení srovnání výkonu metod mezi instancemi\r\n   */\n  loadInstancesComparison() {\n    console.log('loadInstancesComparison - Začínám načítat data');\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadInstancesComparison - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadInstancesComparison - Instance nejsou načteny, načítám je');\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: data => {\n          console.log('loadInstancesComparison - Instance úspěšně načteny:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n          // Pokračujeme v načítání dat\n          this.loadInstancesComparison();\n        },\n        error: error => {\n          console.error('loadInstancesComparison - Chyba při načítání instancí:', error);\n          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n          this.loading = false;\n        }\n      });\n      return;\n    }\n    // Kontrola, zda jsou vybrány alespoň dvě instance\n    if (!this.instancesComparisonFilterData.instanceIds || this.instancesComparisonFilterData.instanceIds.length < 2) {\n      // Necháme validaci na formuláři, nezobrazujeme chybový blok\n      this.loading = false;\n      return;\n    }\n    // Příprava parametrů\n    const instanceIds = this.instancesComparisonFilterData.instanceIds;\n    const methodName = this.instancesComparisonFilterData.methodName;\n    const className = this.instancesComparisonFilterData.className;\n    let fromDate;\n    let toDate;\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.instancesComparisonFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.instancesComparisonFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.instancesComparisonFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.instancesComparisonFilterData.fromDate) {\n        fromDate = new Date(this.instancesComparisonFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n      if (this.instancesComparisonFilterData.toDate) {\n        toDate = new Date(this.instancesComparisonFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n    try {\n      console.log('loadInstancesComparison - Volám API pro načtení dat', {\n        instanceIds,\n        methodName,\n        className,\n        fromDate,\n        toDate\n      });\n      const subscription = this.performanceService.getInstancesComparison(instanceIds, methodName, className, fromDate, toDate).subscribe({\n        next: data => {\n          console.log('loadInstancesComparison - Data úspěšně načtena:', data);\n          if (!data) {\n            console.error('loadInstancesComparison - API vrátilo prázdná data');\n            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n            this.loading = false;\n            return;\n          }\n          // Uložení dat pro pozdější vykreslení\n          this.instancesComparisonData = data;\n          // Vykreslení grafu\n          this.renderInstancesComparisonChart(data);\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Chyba při načítání srovnání instancí:', error);\n          this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi instancemi. ' + (error.message ? error.message : 'Zkuste to prosím znovu.');\n          this.loading = false;\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadInstancesComparison - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadInstancesComparison - Opakuji volání API');\n              this.loadInstancesComparison();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadInstancesComparison - Volání API dokončeno');\n        }\n      });\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadInstancesComparison - Chyba při volání API:', error);\n      this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi instancemi. Zkuste to prosím znovu.';\n      this.loading = false;\n    }\n  }\n  /**\r\n   * Zpracování změny filtru pro srovnání instancí\r\n   */\n  onInstancesComparisonFilterChange(filterData) {\n    console.log('onInstancesComparisonFilterChange - Přijatá data filtru:', filterData);\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.instancesComparisonFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onInstancesComparisonFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    }\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onInstancesComparisonFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    this.instancesComparisonFilterData = filterData;\n    console.log('onInstancesComparisonFilterChange - Zpracovaná data filtru:', this.instancesComparisonFilterData);\n    this.loadInstancesComparison();\n  }\n  /**\r\n   * Načtení srovnání výkonu metod mezi verzemi\r\n   */\n  loadVersionsComparison() {\n    console.log('loadVersionsComparison - Začínám načítat data');\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadVersionsComparison - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadVersionsComparison - Instance nejsou načteny, načítám je');\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: data => {\n          console.log('loadVersionsComparison - Instance úspěšně načteny:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n          // Pokračujeme v načítání dat\n          this.loadVersionsComparison();\n        },\n        error: error => {\n          console.error('loadVersionsComparison - Chyba při načítání instancí:', error);\n          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n          this.loading = false;\n        }\n      });\n      return;\n    }\n    // Kontrola, zda je vybrána instance\n    if (!this.versionsComparisonFilterData.instanceId || this.versionsComparisonFilterData.instanceId === null || String(this.versionsComparisonFilterData.instanceId) === \"null\") {\n      console.warn('loadVersionsComparison - Není vybrána instance, přeskakuji načítání dat');\n      // Necháme validaci na formuláři, nezobrazujeme chybový blok\n      this.loading = false;\n      return;\n    }\n    // Příprava parametrů\n    const instanceId = this.versionsComparisonFilterData.instanceId;\n    const methodName = this.versionsComparisonFilterData.methodName;\n    const className = this.versionsComparisonFilterData.className;\n    let fromDate;\n    let toDate;\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.versionsComparisonFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.versionsComparisonFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.versionsComparisonFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.versionsComparisonFilterData.fromDate) {\n        fromDate = new Date(this.versionsComparisonFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n      if (this.versionsComparisonFilterData.toDate) {\n        toDate = new Date(this.versionsComparisonFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n    try {\n      console.log('loadVersionsComparison - Volám API pro načtení dat', {\n        instanceId,\n        methodName,\n        className,\n        fromDate,\n        toDate\n      });\n      const subscription = this.performanceService.getVersionsComparison(instanceId, methodName, className, fromDate, toDate).subscribe({\n        next: data => {\n          console.log('loadVersionsComparison - Data úspěšně načtena:', data);\n          if (!data) {\n            console.error('loadVersionsComparison - API vrátilo prázdná data');\n            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n            this.loading = false;\n            return;\n          }\n          // Uložení dat pro pozdější vykreslení\n          this.versionsComparisonData = data;\n          // Vykreslení grafu\n          this.renderVersionsComparisonChart(data);\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Chyba při načítání srovnání verzí:', error);\n          this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi verzemi. ' + (error.message ? error.message : 'Zkuste to prosím znovu.');\n          this.loading = false;\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadVersionsComparison - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadVersionsComparison - Opakuji volání API');\n              this.loadVersionsComparison();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadVersionsComparison - Volání API dokončeno');\n        }\n      });\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadVersionsComparison - Chyba při volání API:', error);\n      this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi verzemi. Zkuste to prosím znovu.';\n      this.loading = false;\n    }\n  }\n  /**\r\n   * Zpracování změny filtru pro srovnání verzí\r\n   */\n  onVersionsComparisonFilterChange(filterData) {\n    console.log('onVersionsComparisonFilterChange - Přijatá data filtru:', filterData);\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.versionsComparisonFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onVersionsComparisonFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    }\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onVersionsComparisonFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    // Kontrola, zda je vybrána instance\n    if (filterData.instanceId === null || filterData.instanceId === undefined || String(filterData.instanceId) === \"null\") {\n      console.warn('onVersionsComparisonFilterChange - Není vybrána instance, přeskakuji načítání dat');\n      // Aktualizujeme data filtru, ale nevoláme načítání dat\n      this.versionsComparisonFilterData = filterData;\n      return;\n    }\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    this.versionsComparisonFilterData = filterData;\n    console.log('onVersionsComparisonFilterChange - Zpracovaná data filtru:', this.versionsComparisonFilterData);\n    this.loadVersionsComparison();\n  }\n  /**\r\n   * Načtení nejčastěji volaných metod\r\n   */\n  loadMostCalledMethods() {\n    console.log('loadMostCalledMethods - Začínám načítat data');\n    // Přidáme kontrolu, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadMostCalledMethods - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    // Nastavíme příznak načítání\n    this.loading = true;\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadMostCalledMethods - Instance nejsou načteny, načítám je');\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: data => {\n          console.log('loadMostCalledMethods - Instance úspěšně načteny:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n          // Pokračujeme v načítání dat\n          this.loadMostCalledMethods();\n        },\n        error: error => {\n          console.error('loadMostCalledMethods - Chyba při načítání instancí:', error);\n          this.mostCalledMethods = [];\n          this.loading = false;\n        }\n      });\n      return;\n    }\n    // Příprava parametrů\n    // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n    let instanceId = this.aggregatedFilterData.instanceId;\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n      instanceId = undefined;\n      console.log('loadMostCalledMethods - Použití undefined pro instanceId (všechny instance)');\n    }\n    let fromDate;\n    let toDate;\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.aggregatedFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.aggregatedFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.aggregatedFilterData.fromDate) {\n        fromDate = new Date(this.aggregatedFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n      if (this.aggregatedFilterData.toDate) {\n        toDate = new Date(this.aggregatedFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n    try {\n      console.log('loadMostCalledMethods - Volám API pro načtení dat', {\n        instanceId,\n        fromDate,\n        toDate\n      });\n      const subscription = this.performanceService.getMostCalledMethods(instanceId, fromDate, toDate, 10).subscribe({\n        next: data => {\n          console.log('loadMostCalledMethods - Data úspěšně načtena:', data);\n          if (!data) {\n            console.error('loadMostCalledMethods - API vrátilo prázdná data');\n            this.mostCalledMethods = [];\n            this.loading = false;\n            return;\n          }\n          this.mostCalledMethods = data;\n          // Aplikujeme řazení na načtená data\n          this.applySortToMethods();\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Chyba při načítání nejčastěji volaných metod:', error);\n          this.mostCalledMethods = [];\n          this.loading = false;\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadMostCalledMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadMostCalledMethods - Opakuji volání API');\n              this.loadMostCalledMethods();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadMostCalledMethods - Volání API dokončeno');\n        }\n      });\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadMostCalledMethods - Chyba při volání API:', error);\n      this.mostCalledMethods = [];\n      this.loading = false;\n    }\n  }\n  /**\r\n   * Načtení nejpomalejších metod\r\n   */\n  loadSlowestMethods() {\n    console.log('loadSlowestMethods - Začínám načítat data');\n    // Přidáme kontrolu, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadSlowestMethods - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    // Nastavíme příznak načítání\n    this.loading = true;\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadSlowestMethods - Instance nejsou načteny, načítám je');\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: data => {\n          console.log('loadSlowestMethods - Instance úspěšně načteny:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n          // Pokračujeme v načítání dat\n          this.loadSlowestMethods();\n        },\n        error: error => {\n          console.error('loadSlowestMethods - Chyba při načítání instancí:', error);\n          this.slowestMethods = [];\n        }\n      });\n      return;\n    }\n    // Příprava parametrů\n    // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n    let instanceId = this.aggregatedFilterData.instanceId;\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n      instanceId = undefined;\n      console.log('loadSlowestMethods - Použití undefined pro instanceId (všechny instance)');\n    }\n    let fromDate;\n    let toDate;\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.aggregatedFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.aggregatedFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.aggregatedFilterData.fromDate) {\n        fromDate = new Date(this.aggregatedFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n      if (this.aggregatedFilterData.toDate) {\n        toDate = new Date(this.aggregatedFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n    try {\n      console.log('loadSlowestMethods - Volám API pro načtení dat', {\n        instanceId,\n        fromDate,\n        toDate\n      });\n      const subscription = this.performanceService.getSlowestMethods(instanceId, fromDate, toDate, 10).subscribe({\n        next: data => {\n          console.log('loadSlowestMethods - Data úspěšně načtena:', data);\n          if (!data) {\n            console.error('loadSlowestMethods - API vrátilo prázdná data');\n            this.slowestMethods = [];\n            this.loading = false;\n            return;\n          }\n          this.slowestMethods = data;\n          // Aplikujeme řazení na načtená data\n          this.applySortToMethods();\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Chyba při načítání nejpomalejších metod:', error);\n          this.slowestMethods = [];\n          this.loading = false;\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadSlowestMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadSlowestMethods - Opakuji volání API');\n              this.loadSlowestMethods();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadSlowestMethods - Volání API dokončeno');\n        }\n      });\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadSlowestMethods - Chyba při volání API:', error);\n      this.slowestMethods = [];\n      this.loading = false;\n    }\n  }\n  /**\r\n   * Načtení všech metod\r\n   */\n  loadAllMethods() {\n    // Příprava parametrů\n    // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n    let instanceId = this.aggregatedFilterData.instanceId;\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n      instanceId = undefined;\n      console.log('loadAllMethods - Použití undefined pro instanceId (všechny instance)');\n    }\n    let fromDate;\n    let toDate;\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.aggregatedFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.aggregatedFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.aggregatedFilterData.fromDate) {\n        fromDate = new Date(this.aggregatedFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n      if (this.aggregatedFilterData.toDate) {\n        toDate = new Date(this.aggregatedFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n    try {\n      console.log('loadAllMethods - Volám API pro načtení dat', {\n        instanceId,\n        fromDate,\n        toDate\n      });\n      const subscription = this.performanceService.getAllMethods(instanceId, fromDate, toDate).subscribe({\n        next: data => {\n          console.log('loadAllMethods - Data úspěšně načtena:', data);\n          if (!data) {\n            console.error('loadAllMethods - API vrátilo prázdná data');\n            this.allMethods = [];\n            return;\n          }\n          this.allMethods = data;\n          // Aplikujeme řazení na načtená data\n          this.applySortToMethods();\n        },\n        error: error => {\n          console.error('Chyba při načítání všech metod:', error);\n          this.allMethods = [];\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadAllMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadAllMethods - Opakuji volání API');\n              this.loadAllMethods();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadAllMethods - Volání API dokončeno');\n        }\n      });\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadAllMethods - Chyba při volání API:', error);\n      this.allMethods = [];\n    }\n  }\n  /**\r\n   * Načtení detailu metody\r\n   */\n  loadMethodDetail() {\n    console.log('loadMethodDetail - Začínám načítat data');\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadMethodDetail - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    // Kontrola, zda máme vybranou metodu nebo zadané povinné parametry\n    if (!this.selectedMethod && (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName)) {\n      console.warn('loadMethodDetail - Není vybrána metoda a nejsou zadány povinné parametry');\n      // Necháme validaci na formuláři, nezobrazujeme chybový blok\n      this.loading = false;\n      return;\n    }\n    // Pokud máme vybranou metodu, ale nemáme zadané parametry, použijeme parametry z vybrané metody\n    if (this.selectedMethod && (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName)) {\n      console.log('loadMethodDetail - Použití parametrů z vybrané metody');\n      this.methodDetailFilterData.className = this.selectedMethod.className;\n      this.methodDetailFilterData.methodName = this.selectedMethod.methodName;\n      // Zjištění aktuální hodnoty instanceId\n      let instanceId = this.selectedMethod.instanceId;\n      // Pokud instanceId je null nebo undefined, použijeme 0 (všechny instance)\n      if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n        instanceId = 0;\n      }\n      // Nastavení instanceId\n      this.methodDetailFilterData.instanceId = instanceId;\n      // Uložení filtru do localStorage\n      this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n    }\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadMethodDetail - Instance nejsou načteny, načítám je');\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: data => {\n          console.log('loadMethodDetail - Instance úspěšně načteny:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n          // Pokračujeme v načítání dat\n          this.loadMethodDetail();\n        },\n        error: error => {\n          console.error('loadMethodDetail - Chyba při načítání instancí:', error);\n          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n          this.loading = false;\n        }\n      });\n      return;\n    }\n    // Kontrola, zda jsou vyplněny všechny povinné parametry\n    if (!this.methodDetailFilterData.instanceId || !this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName) {\n      // Necháme validaci na formuláři, nezobrazujeme chybový blok\n      this.loading = false;\n      return;\n    }\n    // Příprava parametrů\n    // Pro metodu getMethodDetail potřebujeme instanceId jako number, null nebo 0\n    let instanceId = this.methodDetailFilterData.instanceId ?? null;\n    // Pokud instanceId je null, undefined nebo řetězec \"null\", použijeme 0 (všechny instance)\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n      instanceId = 0; // 0 = všechny instance\n      console.log('loadMethodDetail - Použití hodnoty 0 pro instanceId (všechny instance)');\n      // Aktualizujeme hodnotu v objektu filtru\n      this.methodDetailFilterData.instanceId = 0;\n    }\n    console.log('loadMethodDetail - Finální hodnota instanceId:', instanceId);\n    const className = this.methodDetailFilterData.className;\n    const methodName = this.methodDetailFilterData.methodName;\n    let fromDate;\n    let toDate;\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.methodDetailFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.methodDetailFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.methodDetailFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.methodDetailFilterData.fromDate) {\n        fromDate = new Date(this.methodDetailFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n      if (this.methodDetailFilterData.toDate) {\n        toDate = new Date(this.methodDetailFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n    try {\n      console.log('loadMethodDetail - Volám API pro načtení dat', {\n        instanceId,\n        className,\n        methodName,\n        fromDate,\n        toDate\n      });\n      // Kontrola, zda jsou všechny povinné parametry nastaveny\n      if (!className || !methodName) {\n        console.error('loadMethodDetail - Nejsou zadány povinné parametry (třída nebo metoda)');\n        this.error = 'Pro zobrazení detailu metody je nutné zadat název třídy a metody.';\n        this.loading = false;\n        return;\n      }\n      // Pokud instanceId je 0, použijeme null, aby se parametr instanceId vůbec neodeslal\n      const finalInstanceId = instanceId === 0 ? null : instanceId;\n      const subscription = this.performanceService.getMethodDetail(finalInstanceId, className, methodName, fromDate, toDate).subscribe({\n        next: data => {\n          console.log('loadMethodDetail - Data úspěšně načtena:', data);\n          if (!data) {\n            console.error('loadMethodDetail - API vrátilo prázdná data');\n            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n            this.loading = false;\n            return;\n          }\n          // Aktualizace hodnot filtru podle načtených dat\n          // Tím zajistíme, že hodnoty ve filtru budou odpovídat načteným datům\n          if (data.className && data.methodName) {\n            console.log('loadMethodDetail - Aktualizuji hodnoty filtru podle načtených dat:', {\n              className: data.className,\n              methodName: data.methodName\n            });\n            this.methodDetailFilterData.className = data.className;\n            this.methodDetailFilterData.methodName = data.methodName;\n            // Uložení filtru do localStorage\n            this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n          }\n          this.methodDetailData = data;\n          this.methodDetailChartData = data;\n          // Kontrola, zda jsou data prázdná\n          const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0;\n          this.hasMethodDetailChartData = hasData;\n          // Vykreslení grafu\n          this.renderMethodDetailChart(data);\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Chyba při načítání detailu metody:', error);\n          this.error = 'Nepodařilo se načíst detail metody. ' + (error.message ? error.message : 'Zkuste to prosím znovu.');\n          this.loading = false;\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadMethodDetail - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadMethodDetail - Opakuji volání API');\n              this.loadMethodDetail();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadMethodDetail - Volání API dokončeno');\n        }\n      });\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadMethodDetail - Chyba při volání API:', error);\n      this.error = 'Nepodařilo se načíst detail metody. Zkuste to prosím znovu.';\n      this.loading = false;\n    }\n  }\n  /**\r\n   * Načtení dat podle vybraného typu metod\r\n   */\n  loadMethodsByType() {\n    console.log('loadMethodsByType - Načítám data podle typu:', this.methodsDisplayType);\n    switch (this.methodsDisplayType) {\n      case 'slowest':\n        this.loadSlowestMethods();\n        break;\n      case 'most-called':\n        this.loadMostCalledMethods();\n        break;\n      case 'all':\n        this.loadAllMethods();\n        break;\n      default:\n        this.loadSlowestMethods();\n        break;\n    }\n  }\n  /**\r\n   * Zpracování změny filtru pro metody\r\n   */\n  onMethodsFilterChange(filterData) {\n    console.log('onMethodsFilterChange - Přijatá data filtru:', filterData);\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.aggregatedFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onMethodsFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    }\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onMethodsFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Aktualizace typu zobrazovaných metod\n    if (filterData.methodsDisplayType) {\n      this.methodsDisplayType = filterData.methodsDisplayType;\n    } else {\n      // Výchozí hodnota, pokud není zadána\n      this.methodsDisplayType = 'slowest';\n      filterData.methodsDisplayType = 'slowest';\n    }\n    this.aggregatedFilterData = filterData;\n    console.log('onMethodsFilterChange - Zpracovaná data filtru:', this.aggregatedFilterData);\n    this.loadMethodsByType();\n    // Reinicializace popoverů po změně typu zobrazovaných metod\n    setTimeout(() => {\n      this.initPopovers();\n    }, 500);\n  }\n  /**\r\n   * Zpracování změny filtru pro detail metody\r\n   */\n  onMethodDetailFilterChange(filterData) {\n    console.log('onMethodDetailFilterChange - Přijatá data filtru:', filterData);\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onMethodDetailFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n    // Aktualizujeme data filtru\n    this.methodDetailFilterData = {\n      ...filterData\n    };\n    // Kontrola, zda je hodnota instanceId nastavena\n    if (filterData.instanceId === null || filterData.instanceId === undefined || String(filterData.instanceId) === \"null\") {\n      console.log('onMethodDetailFilterChange - Hodnota instanceId není nastavena, použijeme 0 (všechny instance)');\n      this.methodDetailFilterData.instanceId = 0;\n    } else {\n      console.log('onMethodDetailFilterChange - Hodnota instanceId je nastavena:', filterData.instanceId);\n    }\n    // Pokud se změnil filtr, resetujeme vybranou metodu, aby se použily hodnoty z filtru\n    if (this.selectedMethod) {\n      if (this.selectedMethod.className !== this.methodDetailFilterData.className || this.selectedMethod.methodName !== this.methodDetailFilterData.methodName || this.selectedMethod.instanceId !== this.methodDetailFilterData.instanceId) {\n        console.log('onMethodDetailFilterChange - Resetuji vybranou metodu, protože se změnil filtr');\n        this.selectedMethod = null;\n      }\n    }\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        this.methodDetailFilterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        this.methodDetailFilterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        this.methodDetailFilterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        this.methodDetailFilterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    console.log('onMethodDetailFilterChange - Zpracovaná data filtru:', this.methodDetailFilterData);\n    // Kontrola, zda jsou vyplněny povinné parametry\n    if (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName) {\n      console.warn('onMethodDetailFilterChange - Nejsou vyplněny povinné parametry (třída nebo metoda), přeskakuji načítání dat');\n      return;\n    }\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.methodDetailFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onMethodDetailFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    } else {\n      // Pokud se filtr změnil, uložíme ho do localStorage\n      this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n    }\n    // Resetujeme indikátor, zda jsou k dispozici data pro graf\n    this.hasMethodDetailChartData = true;\n    // Vždy načteme data a překreslíme graf\n    this.loadMethodDetail();\n  }\n  /**\r\n   * Zobrazení detailu metody\r\n   */\n  showMethodDetail(method) {\n    console.log('showMethodDetail - Zobrazuji detail metody:', method);\n    this.selectedMethod = method;\n    // Zjištění aktuální hodnoty instanceId z aktivního filtru podle záložky\n    let instanceId = method.instanceId;\n    // Pro záložky \"aggregated\" a \"methods\" použij hodnotu z this.aggregatedFilterData.instanceId\n    if (this.activeTab === 'aggregated' || this.activeTab === 'methods') {\n      if (this.aggregatedFilterData && this.aggregatedFilterData.instanceId !== undefined) {\n        instanceId = this.aggregatedFilterData.instanceId;\n        // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme 0 (všechny instance)\n        if (instanceId === null || String(instanceId) === \"null\") {\n          instanceId = 0;\n        }\n      }\n    }\n    // Pro záložku \"versions-comparison\" použij hodnotu z this.versionsComparisonFilterData.instanceId\n    else if (this.activeTab === 'versions-comparison') {\n      if (this.versionsComparisonFilterData && this.versionsComparisonFilterData.instanceId !== undefined) {\n        instanceId = this.versionsComparisonFilterData.instanceId;\n        // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme undefined\n        if (instanceId === null || String(instanceId) === \"null\") {\n          instanceId = undefined;\n        }\n      }\n    }\n    // Pro záložku \"instances-comparison\" a ostatní záložky použij hodnotu z method.instanceId\n    // Toto je již nastaveno výchozí hodnotou instanceId = method.instanceId\n    // Nastavení hodnot filtru\n    // Pro metodu getMethodDetail potřebujeme instanceId jako number nebo null\n    // Pokud instanceId je undefined, použijeme hodnotu z metody\n    if (instanceId === undefined) {\n      instanceId = method.instanceId;\n    }\n    // Pokud instanceId je null, nastavíme ho na 0 (všechny instance)\n    if (instanceId === null) {\n      instanceId = 0;\n    }\n    console.log('showMethodDetail - Nastavuji hodnoty filtru:', {\n      instanceId: instanceId,\n      className: method.className,\n      methodName: method.methodName\n    });\n    // Aktualizace hodnot filtru\n    this.methodDetailFilterData = {\n      ...this.methodDetailFilterData,\n      instanceId: instanceId,\n      className: method.className,\n      methodName: method.methodName\n    };\n    // Uložení filtru do localStorage, aby byl dostupný i po obnovení stránky\n    this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n    // Resetujeme indikátor, zda jsou k dispozici data pro graf\n    this.hasMethodDetailChartData = true;\n    // Přepnutí na záložku s detailem metody\n    this.activeTab = 'method-detail';\n    // Uložení aktivní záložky do localStorage\n    this.saveActiveTab('method-detail');\n    // Načtení detailu metody\n    this.loadMethodDetail();\n  }\n  /**\r\n   * Uložení nastavení viditelnosti metrik do localStorage\r\n   */\n  saveMetricsVisibility(chartType, datasets) {\n    try {\n      const visibility = {};\n      // Procházíme všechny datasety a ukládáme jejich viditelnost\n      datasets.forEach(dataset => {\n        if (dataset.label) {\n          // Použijeme metodu isDatasetVisible z Chart.js, pokud je k dispozici\n          if (this.aggregatedPerformanceChart && chartType === 'aggregated') {\n            const index = datasets.indexOf(dataset);\n            visibility[dataset.label] = this.aggregatedPerformanceChart.isDatasetVisible(index);\n          } else if (this.instancesComparisonChart && chartType === 'instances-comparison') {\n            const index = datasets.indexOf(dataset);\n            visibility[dataset.label] = this.instancesComparisonChart.isDatasetVisible(index);\n          } else if (this.versionsComparisonChart && chartType === 'versions-comparison') {\n            const index = datasets.indexOf(dataset);\n            visibility[dataset.label] = this.versionsComparisonChart.isDatasetVisible(index);\n          } else if (this.methodDetailChart && chartType === 'method-detail') {\n            const index = datasets.indexOf(dataset);\n            visibility[dataset.label] = this.methodDetailChart.isDatasetVisible(index);\n          } else {\n            // Fallback na původní logiku\n            visibility[dataset.label] = !dataset.hidden;\n          }\n        }\n      });\n      // Uložení do localStorage\n      localStorage.setItem(`metrics_visibility_${chartType}`, JSON.stringify(visibility));\n      console.log(`Nastavení viditelnosti metrik pro ${chartType} uloženo:`, visibility);\n    } catch (error) {\n      console.error(`Chyba při ukládání nastavení viditelnosti metrik pro ${chartType}:`, error);\n    }\n  }\n  /**\r\n   * Uložení aktivní záložky do localStorage\r\n   */\n  saveActiveTab(tabId) {\n    try {\n      localStorage.setItem('performance_active_tab', tabId);\n      console.log(`Uložena aktivní záložka: ${tabId}`);\n    } catch (error) {\n      console.error(`Chyba při ukládání aktivní záložky:`, error);\n    }\n  }\n  /**\r\n   * Načtení aktivní záložky z localStorage\r\n   */\n  loadActiveTab() {\n    try {\n      const activeTab = localStorage.getItem('performance_active_tab');\n      console.log(`Načtena aktivní záložka z localStorage: ${activeTab}`);\n      return activeTab;\n    } catch (error) {\n      console.error(`Chyba při načítání aktivní záložky:`, error);\n      return null;\n    }\n  }\n  /**\r\n   * Uložení filtru do localStorage\r\n   * @param entityType Typ entity, pro kterou se ukládá filtr\r\n   * @param filterData Data filtru\r\n   */\n  saveFilter(entityType, filterData) {\n    try {\n      const lastFilterKey = `last_filter_${entityType}`;\n      localStorage.setItem(lastFilterKey, JSON.stringify(filterData));\n      console.log(`Uložen filtr pro ${entityType} do localStorage:`, filterData);\n    } catch (error) {\n      console.error(`Chyba při ukládání filtru pro ${entityType} do localStorage:`, error);\n    }\n  }\n  /**\r\n   * Zpracování kliknutí na záhlaví sloupce pro řazení\r\n   * @param column Název sloupce, podle kterého se má řadit\r\n   */\n  onSort(column) {\n    console.log(`onSort - Řazení podle sloupce: ${column}`);\n    const newSortingState = this.tableSortingService.createNewSortingStateWithDefaults(this.sortColumn, this.sortDirection, column, ['totalCount', 'min', 'max', 'avg', 'median', 'percentil95'] // číselné sloupce\n    );\n    // Aktualizace lokálního stavu\n    this.sortColumn = newSortingState.column;\n    this.sortDirection = newSortingState.direction;\n    // Uložení do localStorage podle aktivní záložky\n    const tableName = this.activeTab === 'methods' ? 'methods' : 'methods';\n    this.tableSortingService.saveSortingState('performance', tableName, newSortingState);\n    console.log(`onSort - Nové řazení: ${this.sortColumn} ${this.sortDirection}`);\n    // Aplikujeme řazení na data\n    this.applySortToMethods();\n  }\n  /**\r\n   * Aplikace řazení na data metod\r\n   */\n  applySortToMethods() {\n    console.log(`applySortToMethods - Aplikuji řazení: ${this.sortColumn} ${this.sortDirection}`);\n    // Řazení nejpomalejších metod\n    if (this.slowestMethods && this.slowestMethods.length > 0) {\n      this.slowestMethods = this.sortMethods([...this.slowestMethods]);\n    }\n    // Řazení nejčastěji volaných metod\n    if (this.mostCalledMethods && this.mostCalledMethods.length > 0) {\n      this.mostCalledMethods = this.sortMethods([...this.mostCalledMethods]);\n    }\n    // Řazení všech metod\n    if (this.allMethods && this.allMethods.length > 0) {\n      this.allMethods = this.sortMethods([...this.allMethods]);\n    }\n  }\n  /**\r\n   * Řazení pole metod podle aktuálního sloupce a směru\r\n   * @param methods Pole metod k seřazení\r\n   * @returns Seřazené pole metod\r\n   */\n  sortMethods(methods) {\n    return methods.sort((a, b) => {\n      let valueA;\n      let valueB;\n      // Získání hodnot pro porovnání podle názvu sloupce\n      switch (this.sortColumn) {\n        case 'instanceName':\n          valueA = a.instanceName?.toLowerCase() || '';\n          valueB = b.instanceName?.toLowerCase() || '';\n          break;\n        case 'className':\n          valueA = a.className?.toLowerCase() || '';\n          valueB = b.className?.toLowerCase() || '';\n          break;\n        case 'methodName':\n          valueA = a.methodName?.toLowerCase() || '';\n          valueB = b.methodName?.toLowerCase() || '';\n          break;\n        case 'totalCount':\n          valueA = a.totalCount || 0;\n          valueB = b.totalCount || 0;\n          break;\n        case 'min':\n          valueA = a.min || 0;\n          valueB = b.min || 0;\n          break;\n        case 'max':\n          valueA = a.max || 0;\n          valueB = b.max || 0;\n          break;\n        case 'avg':\n          valueA = a.avg || 0;\n          valueB = b.avg || 0;\n          break;\n        case 'median':\n          valueA = a.median || 0;\n          valueB = b.median || 0;\n          break;\n        case 'percentil95':\n          valueA = a.percentil95 || 0;\n          valueB = b.percentil95 || 0;\n          break;\n        default:\n          valueA = 0;\n          valueB = 0;\n      }\n      // Porovnání hodnot podle směru řazení\n      if (this.sortDirection === 'asc') {\n        if (typeof valueA === 'string') {\n          return valueA.localeCompare(valueB);\n        } else {\n          return valueA - valueB;\n        }\n      } else {\n        if (typeof valueA === 'string') {\n          return valueB.localeCompare(valueA);\n        } else {\n          return valueB - valueA;\n        }\n      }\n    });\n  }\n  /**\r\n   * Načtení stavu třídění z localStorage\r\n   */\n  loadSortingState() {\n    const defaultSort = {\n      column: 'avg',\n      direction: 'desc'\n    };\n    const savedSortingState = this.tableSortingService.loadSortingState('performance', 'methods', defaultSort);\n    if (savedSortingState) {\n      this.sortColumn = savedSortingState.column;\n      this.sortDirection = savedSortingState.direction;\n    }\n  }\n  /**\r\n   * Načtení nastavení viditelnosti metrik z localStorage\r\n   */\n  loadMetricsVisibility(chartType) {\n    try {\n      const visibilityJson = localStorage.getItem(`metrics_visibility_${chartType}`);\n      if (visibilityJson) {\n        const visibility = JSON.parse(visibilityJson);\n        console.log(`Načteno nastavení viditelnosti metrik pro ${chartType}:`, visibility);\n        return visibility;\n      }\n    } catch (error) {\n      console.error(`Chyba při načítání nastavení viditelnosti metrik pro ${chartType}:`, error);\n    }\n    return null;\n  }\n  /**\r\n   * Vykreslení grafu agregovaných výkonnostních metrik\r\n   */\n  renderAggregatedPerformanceChart(data) {\n    console.log('renderAggregatedPerformanceChart - data:', data);\n    // Nastavíme příznak, že se graf vykresluje\n    this.renderingAggregatedChart = true;\n    // Uložíme data pro pozdější vykreslení\n    this.aggregatedPerformanceData = data;\n    // Příprava dat pro graf\n    const chartData = this.prepareAggregatedPerformanceChartData(data);\n    console.log('renderAggregatedPerformanceChart - chartData:', chartData);\n    // Kontrola, zda jsou data prázdná\n    const hasData = chartData.labels && Array.isArray(chartData.labels) && chartData.labels.length > 0;\n    console.log('renderAggregatedPerformanceChart - hasData:', hasData, 'labels:', chartData.labels);\n    if (!hasData) {\n      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n      this.hasAggregatedChartData = false;\n      this.renderingAggregatedChart = false;\n      // Zničíme existující graf, pokud existuje\n      if (this.aggregatedPerformanceChart) {\n        this.aggregatedPerformanceChart.destroy();\n        this.aggregatedPerformanceChart = null;\n      }\n      return;\n    }\n    // Pokud máme data, nastavíme indikátor\n    this.hasAggregatedChartData = true;\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.aggregatedPerformanceChartRef) {\n      console.warn('renderAggregatedPerformanceChart - Reference na canvas element není k dispozici, zkusím později');\n      // Zkusíme počkat a zkusit to znovu po delším timeoutu\n      setTimeout(() => {\n        console.log('renderAggregatedPerformanceChart - Zkouším znovu po delším timeoutu (2000ms)');\n        if (this.aggregatedPerformanceChartRef) {\n          this.renderAggregatedPerformanceChart(data);\n        } else {\n          console.error('renderAggregatedPerformanceChart - Canvas element stále není k dispozici po 2000ms!');\n          this.renderingAggregatedChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n        }\n      }, 2000);\n      return;\n    }\n    console.log('renderAggregatedPerformanceChart - Reference na canvas element je k dispozici');\n    try {\n      // Kontrola, zda je canvas element v DOM\n      if (!document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n        console.warn('renderAggregatedPerformanceChart - Canvas element není v DOM, zkusím později');\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderAggregatedPerformanceChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n          if (this.aggregatedPerformanceChartRef && document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n            this.renderAggregatedPerformanceChart(data);\n          } else {\n            console.error('renderAggregatedPerformanceChart - Canvas element stále není v DOM po 2000ms!');\n          }\n        }, 2000);\n        return;\n      }\n      // Získání kontextu canvas elementu\n      const ctx = this.aggregatedPerformanceChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        console.error('renderAggregatedPerformanceChart - Nepodařilo se získat kontext canvas elementu!');\n        return;\n      }\n      console.log('renderAggregatedPerformanceChart - Kontext canvas elementu je k dispozici');\n      if (this.aggregatedPerformanceChart) {\n        console.log('renderAggregatedPerformanceChart - Ničím existující graf');\n        this.aggregatedPerformanceChart.destroy();\n      }\n      // Kontrola, zda jsou všechna pole dat stejné délky\n      const labelsLength = chartData.labels.length;\n      const avgDataLength = chartData.avgData.length;\n      const medianDataLength = chartData.medianData.length;\n      const p95DataLength = chartData.p95Data.length;\n      const p99DataLength = chartData.p99Data.length;\n      const minDataLength = chartData.minData.length;\n      const maxDataLength = chartData.maxData.length;\n      const totalCountDataLength = chartData.totalCountData.length;\n      console.log('renderAggregatedPerformanceChart - Délky polí:', {\n        labelsLength,\n        avgDataLength,\n        medianDataLength,\n        p95DataLength,\n        p99DataLength,\n        minDataLength,\n        maxDataLength,\n        totalCountDataLength\n      });\n      // Pokud některé pole má jinou délku než labels, upravíme ho\n      if (avgDataLength !== labelsLength) {\n        console.warn('renderAggregatedPerformanceChart - avgData má jinou délku než labels, upravuji');\n        chartData.avgData = this.adjustArrayLength(chartData.avgData, labelsLength);\n      }\n      if (medianDataLength !== labelsLength) {\n        console.warn('renderAggregatedPerformanceChart - medianData má jinou délku než labels, upravuji');\n        chartData.medianData = this.adjustArrayLength(chartData.medianData, labelsLength);\n      }\n      if (p95DataLength !== labelsLength) {\n        console.warn('renderAggregatedPerformanceChart - p95Data má jinou délku než labels, upravuji');\n        chartData.p95Data = this.adjustArrayLength(chartData.p95Data, labelsLength);\n      }\n      if (p99DataLength !== labelsLength) {\n        console.warn('renderAggregatedPerformanceChart - p99Data má jinou délku než labels, upravuji');\n        chartData.p99Data = this.adjustArrayLength(chartData.p99Data, labelsLength);\n      }\n      if (minDataLength !== labelsLength) {\n        console.warn('renderAggregatedPerformanceChart - minData má jinou délku než labels, upravuji');\n        chartData.minData = this.adjustArrayLength(chartData.minData, labelsLength);\n      }\n      if (maxDataLength !== labelsLength) {\n        console.warn('renderAggregatedPerformanceChart - maxData má jinou délku než labels, upravuji');\n        chartData.maxData = this.adjustArrayLength(chartData.maxData, labelsLength);\n      }\n      if (totalCountDataLength !== labelsLength) {\n        console.warn('renderAggregatedPerformanceChart - totalCountData má jinou délku než labels, upravuji');\n        chartData.totalCountData = this.adjustArrayLength(chartData.totalCountData, labelsLength);\n      }\n      // Pokud jsou data k dispozici, vykreslíme graf\n      try {\n        console.log('renderAggregatedPerformanceChart - Vytvářím nový graf');\n        // Kontrola, zda je canvas element stále platný\n        if (!this.aggregatedPerformanceChartRef || !document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n          console.error('renderAggregatedPerformanceChart - Canvas element již není platný!');\n          return;\n        }\n        // Kontrola, zda má canvas element rozměry\n        const width = this.aggregatedPerformanceChartRef.nativeElement.width;\n        const height = this.aggregatedPerformanceChartRef.nativeElement.height;\n        console.log('renderAggregatedPerformanceChart - Canvas rozměry:', {\n          width,\n          height\n        });\n        // Načtení nastavení viditelnosti metrik z localStorage\n        const visibility = this.loadMetricsVisibility('aggregated');\n        // Vytvoření datasetů s nastavením viditelnosti\n        const datasets = [{\n          label: 'Průměr (ms)',\n          data: chartData.avgData,\n          borderColor: 'rgba(54, 162, 235, 1)',\n          backgroundColor: 'rgba(54, 162, 235, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Průměr (ms)'] : false\n        }, {\n          label: 'Medián (ms)',\n          data: chartData.medianData,\n          borderColor: 'rgba(75, 192, 192, 1)',\n          backgroundColor: 'rgba(75, 192, 192, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Medián (ms)'] : false\n        }, {\n          label: '95. percentil (ms)',\n          data: chartData.p95Data,\n          borderColor: 'rgba(255, 99, 132, 1)',\n          backgroundColor: 'rgba(255, 99, 132, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['95. percentil (ms)'] : false\n        }, {\n          label: '99. percentil (ms)',\n          data: chartData.p99Data,\n          borderColor: 'rgba(255, 159, 64, 1)',\n          backgroundColor: 'rgba(255, 159, 64, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['99. percentil (ms)'] : true\n        }, {\n          label: 'Minimum (ms)',\n          data: chartData.minData,\n          borderColor: 'rgba(153, 102, 255, 1)',\n          backgroundColor: 'rgba(153, 102, 255, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Minimum (ms)'] : true\n        }, {\n          label: 'Maximum (ms)',\n          data: chartData.maxData,\n          borderColor: 'rgba(255, 205, 86, 1)',\n          backgroundColor: 'rgba(255, 205, 86, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Maximum (ms)'] : true\n        }, {\n          label: 'Počet volání',\n          data: chartData.totalCountData,\n          borderColor: 'rgba(201, 203, 207, 1)',\n          backgroundColor: 'rgba(201, 203, 207, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Počet volání'] : true,\n          yAxisID: 'y1'\n        }];\n        // Určení, které osy Y mají být zobrazené na základě viditelných metrik\n        const visibleAxes = this.getVisibleAxes(datasets);\n        // Vytvoření nového grafu\n        this.aggregatedPerformanceChart = new Chart(ctx, {\n          type: 'line',\n          data: {\n            labels: chartData.labels,\n            datasets: datasets\n          },\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              title: {\n                display: true,\n                text: chartData.title || 'Výkon v čase'\n              },\n              tooltip: {\n                mode: 'index',\n                intersect: false\n              },\n              legend: {\n                position: 'top',\n                labels: {\n                  boxWidth: 12\n                },\n                onClick: (_e, legendItem, legend) => {\n                  // Standardní chování - přepnutí viditelnosti datasetu\n                  const index = legendItem.datasetIndex;\n                  const ci = legend.chart;\n                  if (index !== undefined) {\n                    if (ci.isDatasetVisible(index)) {\n                      ci.hide(index);\n                      legendItem.hidden = true;\n                    } else {\n                      ci.show(index);\n                      legendItem.hidden = false;\n                    }\n                    // Aktualizace viditelnosti os Y na základě nově viditelných metrik\n                    this.updateAxisVisibility(ci);\n                    // Počkáme, až se graf aktualizuje\n                    setTimeout(() => {\n                      // Uložení nastavení viditelnosti do localStorage\n                      this.saveMetricsVisibility('aggregated', ci.data.datasets);\n                    }, 0);\n                  }\n                }\n              }\n            },\n            scales: {\n              x: {\n                display: true,\n                title: {\n                  display: true,\n                  text: 'Datum'\n                }\n              },\n              y: {\n                display: visibleAxes.showLeftAxis,\n                title: {\n                  display: visibleAxes.showLeftAxis,\n                  text: 'Doba odezvy (ms)'\n                },\n                beginAtZero: true\n              },\n              y1: {\n                display: visibleAxes.showRightAxis,\n                position: 'right',\n                title: {\n                  display: visibleAxes.showRightAxis,\n                  text: 'Počet volání'\n                },\n                beginAtZero: true,\n                grid: {\n                  drawOnChartArea: !visibleAxes.showLeftAxis // Zobrazit mřížku pouze když levá osa není viditelná\n                }\n              }\n            }\n          }\n        });\n\n        console.log('renderAggregatedPerformanceChart - Graf úspěšně vytvořen');\n        // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n        setTimeout(() => {\n          if (this.aggregatedPerformanceChart) {\n            this.saveMetricsVisibility('aggregated', this.aggregatedPerformanceChart.data.datasets);\n          }\n          // Ukončíme vykreslování\n          this.renderingAggregatedChart = false;\n        }, 100);\n      } catch (error) {\n        console.error('renderAggregatedPerformanceChart - Chyba při vytváření grafu:', error);\n        this.renderingAggregatedChart = false; // Ukončíme vykreslování v případě chyby\n      }\n    } catch (error) {\n      console.error('renderAggregatedPerformanceChart - Chyba při získávání kontextu canvas elementu:', error);\n      this.renderingAggregatedChart = false; // Ukončíme vykreslování v případě chyby\n    }\n  }\n  /**\r\n   * Vykreslení grafu srovnání výkonu metod mezi instancemi\r\n   */\n  renderInstancesComparisonChart(data) {\n    console.log('renderInstancesComparisonChart - data:', data);\n    // Nastavíme příznak, že se graf vykresluje\n    this.renderingInstancesComparisonChart = true;\n    // Uložíme data pro pozdější vykreslení\n    this.instancesComparisonData = data;\n    // Kontrola, zda jsou data prázdná\n    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0 && data.datasets && Array.isArray(data.datasets) && data.datasets.length > 0;\n    if (!hasData) {\n      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n      this.hasInstancesComparisonChartData = false;\n      this.renderingInstancesComparisonChart = false;\n      // Zničíme existující graf, pokud existuje\n      if (this.instancesComparisonChart) {\n        this.instancesComparisonChart.destroy();\n        this.instancesComparisonChart = null;\n      }\n      return;\n    }\n    // Pokud máme data, nastavíme indikátor\n    this.hasInstancesComparisonChartData = true;\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.instancesComparisonChartRef) {\n      console.warn('renderInstancesComparisonChart - Reference na canvas element není k dispozici, zkusím později');\n      // Zkusíme počkat a zkusit to znovu po delším timeoutu\n      setTimeout(() => {\n        console.log('renderInstancesComparisonChart - Zkouším znovu po delším timeoutu (2000ms)');\n        if (this.instancesComparisonChartRef) {\n          this.renderInstancesComparisonChart(data);\n        } else {\n          console.error('renderInstancesComparisonChart - Canvas element stále není k dispozici po 2000ms!');\n          this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n        }\n      }, 2000);\n      return;\n    }\n    console.log('renderInstancesComparisonChart - Reference na canvas element je k dispozici');\n    try {\n      // Kontrola, zda je canvas element v DOM\n      if (!document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n        console.warn('renderInstancesComparisonChart - Canvas element není v DOM, zkusím později');\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderInstancesComparisonChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n          if (this.instancesComparisonChartRef && document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n            this.renderInstancesComparisonChart(data);\n          } else {\n            console.error('renderInstancesComparisonChart - Canvas element stále není v DOM po 2000ms!');\n          }\n        }, 2000);\n        return;\n      }\n      // Získání kontextu canvas elementu\n      const ctx = this.instancesComparisonChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        console.error('renderInstancesComparisonChart - Nepodařilo se získat kontext canvas elementu!');\n        return;\n      }\n      console.log('renderInstancesComparisonChart - Kontext canvas elementu je k dispozici');\n      if (this.instancesComparisonChart) {\n        console.log('renderInstancesComparisonChart - Ničím existující graf');\n        this.instancesComparisonChart.destroy();\n      }\n      try {\n        console.log('renderInstancesComparisonChart - Vytvářím nový graf');\n        // Kontrola, zda je canvas element stále platný\n        if (!this.instancesComparisonChartRef || !document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n          console.error('renderInstancesComparisonChart - Canvas element již není platný!');\n          return;\n        }\n        // Kontrola, zda má canvas element rozměry\n        const width = this.instancesComparisonChartRef.nativeElement.width;\n        const height = this.instancesComparisonChartRef.nativeElement.height;\n        console.log('renderInstancesComparisonChart - Canvas rozměry:', {\n          width,\n          height\n        });\n        // Načtení nastavení viditelnosti metrik z localStorage\n        const visibility = this.loadMetricsVisibility('instances-comparison');\n        // Aplikace nastavení viditelnosti na datasety\n        if (visibility && data.datasets) {\n          data.datasets.forEach(dataset => {\n            if (dataset.label && visibility[dataset.label] !== undefined) {\n              dataset.hidden = !visibility[dataset.label];\n            }\n          });\n        }\n        // Uložení originálních labels pro tooltip\n        const originalLabels = [...data.labels];\n        // Konfigurace pro zkrácené popisky na ose X\n        const truncatedLabelsConfig = this.textUtils.createTruncatedLabelsConfig(originalLabels, {\n          enabled: true,\n          maxLength: 30,\n          showFullInTooltip: true // Zobrazit plný název v tooltip\n        });\n        // Základní konfigurace grafu\n        const baseOptions = {\n          responsive: true,\n          maintainAspectRatio: false,\n          plugins: {\n            title: {\n              display: true,\n              text: data.title || 'Srovnání výkonu metod mezi instancemi'\n            },\n            tooltip: {\n              mode: 'index',\n              intersect: false\n            },\n            legend: {\n              position: 'top',\n              labels: {\n                boxWidth: 12\n              },\n              onClick: (_e, legendItem, legend) => {\n                // Standardní chování - přepnutí viditelnosti datasetu\n                const index = legendItem.datasetIndex;\n                const ci = legend.chart;\n                if (index !== undefined) {\n                  if (ci.isDatasetVisible(index)) {\n                    ci.hide(index);\n                    legendItem.hidden = true;\n                  } else {\n                    ci.show(index);\n                    legendItem.hidden = false;\n                  }\n                  // Počkáme, až se graf aktualizuje\n                  setTimeout(() => {\n                    // Uložení nastavení viditelnosti do localStorage\n                    this.saveMetricsVisibility('instances-comparison', ci.data.datasets);\n                  }, 0);\n                  // Překreslení grafu\n                  ci.update();\n                }\n              }\n            }\n          },\n          scales: {\n            x: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Metoda'\n              }\n            },\n            y: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Doba odezvy (ms)'\n              },\n              beginAtZero: true\n            }\n          }\n        };\n        // Sloučení s konfigurací pro zkrácené popisky\n        const finalOptions = this.textUtils.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n        // Vytvoření nového grafu\n        this.instancesComparisonChart = new Chart(ctx, {\n          type: 'bar',\n          data: {\n            labels: data.labels,\n            datasets: data.datasets\n          },\n          options: finalOptions\n        });\n        console.log('renderInstancesComparisonChart - Graf úspěšně vytvořen');\n        // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n        setTimeout(() => {\n          if (this.instancesComparisonChart) {\n            this.saveMetricsVisibility('instances-comparison', this.instancesComparisonChart.data.datasets);\n          }\n          // Ukončíme vykreslování\n          this.renderingInstancesComparisonChart = false;\n        }, 100);\n      } catch (error) {\n        console.error('renderInstancesComparisonChart - Chyba při vytváření grafu:', error);\n        this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování v případě chyby\n      }\n    } catch (error) {\n      console.error('renderInstancesComparisonChart - Chyba při získávání kontextu canvas elementu:', error);\n      this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování v případě chyby\n    }\n  }\n  /**\r\n   * Vykreslení grafu srovnání výkonu metod mezi verzemi\r\n   */\n  renderVersionsComparisonChart(data) {\n    console.log('renderVersionsComparisonChart - data:', data);\n    // Nastavíme příznak, že se graf vykresluje\n    this.renderingVersionsComparisonChart = true;\n    // Uložíme data pro pozdější vykreslení\n    this.versionsComparisonData = data;\n    // Kontrola, zda jsou data prázdná\n    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0 && data.datasets && Array.isArray(data.datasets) && data.datasets.length > 0;\n    if (!hasData) {\n      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n      this.hasVersionsComparisonChartData = false;\n      this.renderingVersionsComparisonChart = false;\n      // Zničíme existující graf, pokud existuje\n      if (this.versionsComparisonChart) {\n        this.versionsComparisonChart.destroy();\n        this.versionsComparisonChart = null;\n      }\n      return;\n    }\n    // Pokud máme data, nastavíme indikátor\n    this.hasVersionsComparisonChartData = true;\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.versionsComparisonChartRef) {\n      console.warn('renderVersionsComparisonChart - Reference na canvas element není k dispozici, zkusím později');\n      // Zkusíme počkat a zkusit to znovu po delším timeoutu\n      setTimeout(() => {\n        console.log('renderVersionsComparisonChart - Zkouším znovu po delším timeoutu (2000ms)');\n        if (this.versionsComparisonChartRef) {\n          this.renderVersionsComparisonChart(data);\n        } else {\n          console.error('renderVersionsComparisonChart - Canvas element stále není k dispozici po 2000ms!');\n          this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n        }\n      }, 2000);\n      return;\n    }\n    console.log('renderVersionsComparisonChart - Reference na canvas element je k dispozici');\n    try {\n      // Kontrola, zda je canvas element v DOM\n      if (!document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n        console.warn('renderVersionsComparisonChart - Canvas element není v DOM, zkusím později');\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderVersionsComparisonChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n          if (this.versionsComparisonChartRef && document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n            this.renderVersionsComparisonChart(data);\n          } else {\n            console.error('renderVersionsComparisonChart - Canvas element stále není v DOM po 2000ms!');\n          }\n        }, 2000);\n        return;\n      }\n      // Získání kontextu canvas elementu\n      const ctx = this.versionsComparisonChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        console.error('renderVersionsComparisonChart - Nepodařilo se získat kontext canvas elementu!');\n        return;\n      }\n      console.log('renderVersionsComparisonChart - Kontext canvas elementu je k dispozici');\n      if (this.versionsComparisonChart) {\n        console.log('renderVersionsComparisonChart - Ničím existující graf');\n        this.versionsComparisonChart.destroy();\n      }\n      try {\n        console.log('renderVersionsComparisonChart - Vytvářím nový graf');\n        // Kontrola, zda je canvas element stále platný\n        if (!this.versionsComparisonChartRef || !document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n          console.error('renderVersionsComparisonChart - Canvas element již není platný!');\n          return;\n        }\n        // Kontrola, zda má canvas element rozměry\n        const width = this.versionsComparisonChartRef.nativeElement.width;\n        const height = this.versionsComparisonChartRef.nativeElement.height;\n        console.log('renderVersionsComparisonChart - Canvas rozměry:', {\n          width,\n          height\n        });\n        // Načtení nastavení viditelnosti metrik z localStorage\n        const visibility = this.loadMetricsVisibility('versions-comparison');\n        // Aplikace nastavení viditelnosti na datasety\n        if (visibility && data.datasets) {\n          data.datasets.forEach(dataset => {\n            if (dataset.label && visibility[dataset.label] !== undefined) {\n              dataset.hidden = !visibility[dataset.label];\n            }\n          });\n        }\n        // Uložení originálních labels pro tooltip\n        const originalLabels = [...data.labels];\n        // Konfigurace pro zkrácené popisky na ose X\n        const truncatedLabelsConfig = this.textUtils.createTruncatedLabelsConfig(originalLabels, {\n          enabled: true,\n          maxLength: 30,\n          showFullInTooltip: true // Zobrazit plný název v tooltip\n        });\n        // Základní konfigurace grafu\n        const baseOptions = {\n          responsive: true,\n          maintainAspectRatio: false,\n          plugins: {\n            title: {\n              display: true,\n              text: data.title || 'Srovnání výkonu metod mezi verzemi'\n            },\n            tooltip: {\n              mode: 'index',\n              intersect: false\n            },\n            legend: {\n              position: 'top',\n              labels: {\n                boxWidth: 12\n              },\n              onClick: (_e, legendItem, legend) => {\n                // Standardní chování - přepnutí viditelnosti datasetu\n                const index = legendItem.datasetIndex;\n                const ci = legend.chart;\n                if (index !== undefined) {\n                  if (ci.isDatasetVisible(index)) {\n                    ci.hide(index);\n                    legendItem.hidden = true;\n                  } else {\n                    ci.show(index);\n                    legendItem.hidden = false;\n                  }\n                  // Počkáme, až se graf aktualizuje\n                  setTimeout(() => {\n                    // Uložení nastavení viditelnosti do localStorage\n                    this.saveMetricsVisibility('versions-comparison', ci.data.datasets);\n                  }, 0);\n                  // Překreslení grafu\n                  ci.update();\n                }\n              }\n            }\n          },\n          scales: {\n            x: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Metoda'\n              }\n            },\n            y: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Doba odezvy (ms)'\n              },\n              beginAtZero: true\n            }\n          }\n        };\n        // Sloučení s konfigurací pro zkrácené popisky\n        const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n        // Vytvoření nového grafu\n        this.versionsComparisonChart = new Chart(ctx, {\n          type: 'bar',\n          data: {\n            labels: data.labels,\n            datasets: data.datasets\n          },\n          options: finalOptions\n        });\n        console.log('renderVersionsComparisonChart - Graf úspěšně vytvořen');\n        // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n        setTimeout(() => {\n          if (this.versionsComparisonChart) {\n            this.saveMetricsVisibility('versions-comparison', this.versionsComparisonChart.data.datasets);\n          }\n          // Ukončíme vykreslování\n          this.renderingVersionsComparisonChart = false;\n        }, 100);\n      } catch (error) {\n        console.error('renderVersionsComparisonChart - Chyba při vytváření grafu:', error);\n        this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování v případě chyby\n      }\n    } catch (error) {\n      console.error('renderVersionsComparisonChart - Chyba při získávání kontextu canvas elementu:', error);\n      this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování v případě chyby\n    }\n  }\n  /**\r\n   * Vykreslení grafu detailu metody\r\n   */\n  renderMethodDetailChart(data) {\n    console.log('renderMethodDetailChart - data:', data);\n    // Nastavíme příznak, že se graf vykresluje\n    this.renderingMethodDetailChart = true;\n    // Uložíme data pro pozdější vykreslení\n    this.methodDetailChartData = data;\n    // Kontrola, zda jsou data prázdná\n    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0;\n    if (!hasData) {\n      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n      this.hasMethodDetailChartData = false;\n      this.renderingMethodDetailChart = false;\n      // Zničíme existující graf, pokud existuje\n      if (this.methodDetailChart) {\n        this.methodDetailChart.destroy();\n        this.methodDetailChart = null;\n      }\n      return;\n    }\n    // Aktualizace hodnot filtru podle dat grafu, pokud jsou k dispozici\n    if (data.className && data.methodName) {\n      console.log('renderMethodDetailChart - Aktualizuji hodnoty filtru podle dat grafu:', {\n        className: data.className,\n        methodName: data.methodName\n      });\n      // Aktualizace pouze pokud se hodnoty liší\n      if (this.methodDetailFilterData.className !== data.className || this.methodDetailFilterData.methodName !== data.methodName) {\n        this.methodDetailFilterData.className = data.className;\n        this.methodDetailFilterData.methodName = data.methodName;\n        // Uložení filtru do localStorage\n        this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n      }\n    }\n    // Pokud máme data, nastavíme indikátor\n    this.hasMethodDetailChartData = true;\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.methodDetailChartRef) {\n      console.warn('renderMethodDetailChart - Reference na canvas element není k dispozici, zkusím později');\n      // Zkusíme počkat a zkusit to znovu po delším timeoutu\n      setTimeout(() => {\n        console.log('renderMethodDetailChart - Zkouším znovu po delším timeoutu (2000ms)');\n        if (this.methodDetailChartRef) {\n          this.renderMethodDetailChart(data);\n        } else {\n          console.error('renderMethodDetailChart - Canvas element stále není k dispozici po 2000ms!');\n          this.renderingMethodDetailChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n        }\n      }, 2000);\n      return;\n    }\n    console.log('renderMethodDetailChart - Reference na canvas element je k dispozici');\n    try {\n      // Kontrola, zda je canvas element v DOM\n      if (!document.body.contains(this.methodDetailChartRef.nativeElement)) {\n        console.warn('renderMethodDetailChart - Canvas element není v DOM, zkusím později');\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderMethodDetailChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n          if (this.methodDetailChartRef && document.body.contains(this.methodDetailChartRef.nativeElement)) {\n            this.renderMethodDetailChart(data);\n          } else {\n            console.error('renderMethodDetailChart - Canvas element stále není v DOM po 2000ms!');\n          }\n        }, 2000);\n        return;\n      }\n      // Získání kontextu canvas elementu\n      const ctx = this.methodDetailChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        console.error('renderMethodDetailChart - Nepodařilo se získat kontext canvas elementu!');\n        return;\n      }\n      console.log('renderMethodDetailChart - Kontext canvas elementu je k dispozici');\n      if (this.methodDetailChart) {\n        console.log('renderMethodDetailChart - Ničím existující graf');\n        this.methodDetailChart.destroy();\n      }\n      try {\n        console.log('renderMethodDetailChart - Vytvářím nový graf');\n        // Kontrola, zda je canvas element stále platný\n        if (!this.methodDetailChartRef || !document.body.contains(this.methodDetailChartRef.nativeElement)) {\n          console.error('renderMethodDetailChart - Canvas element již není platný!');\n          return;\n        }\n        // Kontrola, zda má canvas element rozměry\n        const width = this.methodDetailChartRef.nativeElement.width;\n        const height = this.methodDetailChartRef.nativeElement.height;\n        console.log('renderMethodDetailChart - Canvas rozměry:', {\n          width,\n          height\n        });\n        // Kontrola, zda jsou všechna pole dat stejné délky\n        if (hasData) {\n          const labelsLength = data.labels.length;\n          // Kontrola existence a délky polí dat\n          if (!data.avgData || data.avgData.length !== labelsLength) {\n            console.warn('renderMethodDetailChart - avgData má jinou délku než labels, upravuji');\n            data.avgData = this.adjustArrayLength(data.avgData || [], labelsLength);\n          }\n          if (!data.medianData || data.medianData.length !== labelsLength) {\n            console.warn('renderMethodDetailChart - medianData má jinou délku než labels, upravuji');\n            data.medianData = this.adjustArrayLength(data.medianData || [], labelsLength);\n          }\n          if (!data.p95Data || data.p95Data.length !== labelsLength) {\n            console.warn('renderMethodDetailChart - p95Data má jinou délku než labels, upravuji');\n            data.p95Data = this.adjustArrayLength(data.p95Data || [], labelsLength);\n          }\n          if (!data.totalCountData || data.totalCountData.length !== labelsLength) {\n            console.warn('renderMethodDetailChart - totalCountData má jinou délku než labels, upravuji');\n            data.totalCountData = this.adjustArrayLength(data.totalCountData || [], labelsLength);\n          }\n        }\n        // Načtení nastavení viditelnosti metrik z localStorage\n        const visibility = this.loadMetricsVisibility('method-detail');\n        // Vytvoření datasetů s nastavením viditelnosti\n        const datasets = [{\n          label: 'Průměr (ms)',\n          data: data.avgData,\n          borderColor: 'rgba(54, 162, 235, 1)',\n          backgroundColor: 'rgba(54, 162, 235, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Průměr (ms)'] : false\n        }, {\n          label: 'Medián (ms)',\n          data: data.medianData,\n          borderColor: 'rgba(75, 192, 192, 1)',\n          backgroundColor: 'rgba(75, 192, 192, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Medián (ms)'] : false\n        }, {\n          label: '95. percentil (ms)',\n          data: data.p95Data,\n          borderColor: 'rgba(255, 99, 132, 1)',\n          backgroundColor: 'rgba(255, 99, 132, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['95. percentil (ms)'] : false\n        }, {\n          label: 'Počet volání',\n          data: data.totalCountData,\n          borderColor: 'rgba(201, 203, 207, 1)',\n          backgroundColor: 'rgba(201, 203, 207, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Počet volání'] : false,\n          yAxisID: 'y1'\n        }];\n        // Vytvoření nového grafu\n        this.methodDetailChart = new Chart(ctx, {\n          type: 'line',\n          data: {\n            labels: data.labels,\n            datasets: datasets\n          },\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              title: {\n                display: true,\n                text: data.title || 'Detail metody'\n              },\n              tooltip: {\n                mode: 'index',\n                intersect: false\n              },\n              legend: {\n                position: 'top',\n                labels: {\n                  boxWidth: 12\n                },\n                onClick: (_e, legendItem, legend) => {\n                  // Standardní chování - přepnutí viditelnosti datasetu\n                  const index = legendItem.datasetIndex;\n                  const ci = legend.chart;\n                  if (index !== undefined) {\n                    if (ci.isDatasetVisible(index)) {\n                      ci.hide(index);\n                      legendItem.hidden = true;\n                    } else {\n                      ci.show(index);\n                      legendItem.hidden = false;\n                    }\n                    // Počkáme, až se graf aktualizuje\n                    setTimeout(() => {\n                      // Uložení nastavení viditelnosti do localStorage\n                      this.saveMetricsVisibility('method-detail', ci.data.datasets);\n                    }, 0);\n                    // Překreslení grafu\n                    ci.update();\n                  }\n                }\n              }\n            },\n            scales: {\n              x: {\n                display: true,\n                title: {\n                  display: true,\n                  text: 'Datum'\n                }\n              },\n              y: {\n                display: true,\n                title: {\n                  display: true,\n                  text: 'Doba odezvy (ms)'\n                },\n                beginAtZero: true\n              },\n              y1: {\n                display: true,\n                position: 'right',\n                title: {\n                  display: true,\n                  text: 'Počet volání'\n                },\n                beginAtZero: true,\n                grid: {\n                  drawOnChartArea: false\n                }\n              }\n            }\n          }\n        });\n        console.log('renderMethodDetailChart - Graf úspěšně vytvořen');\n        // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n        setTimeout(() => {\n          if (this.methodDetailChart) {\n            this.saveMetricsVisibility('method-detail', this.methodDetailChart.data.datasets);\n          }\n          // Ukončíme vykreslování\n          this.renderingMethodDetailChart = false;\n        }, 100);\n      } catch (error) {\n        console.error('renderMethodDetailChart - Chyba při vytváření grafu:', error);\n        this.renderingMethodDetailChart = false; // Ukončíme vykreslování v případě chyby\n      }\n    } catch (error) {\n      console.error('renderMethodDetailChart - Chyba při získávání kontextu canvas elementu:', error);\n      this.renderingMethodDetailChart = false; // Ukončíme vykreslování v případě chyby\n    }\n  }\n  /**\r\n   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu\r\n   * @param baseConfig Základní konfigurace grafu\r\n   * @param truncatedConfig Konfigurace pro zkrácené popisky\r\n   * @returns Sloučená konfigurace\r\n   */\n  mergeChartConfig(baseConfig, truncatedConfig) {\n    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {\n      return baseConfig;\n    }\n    const merged = {\n      ...baseConfig\n    };\n    // Sloučení scales\n    if (truncatedConfig.scales) {\n      merged.scales = merged.scales || {};\n      if (truncatedConfig.scales.x) {\n        merged.scales.x = merged.scales.x || {};\n        merged.scales.x.ticks = {\n          ...merged.scales.x.ticks,\n          ...truncatedConfig.scales.x.ticks\n        };\n      }\n    }\n    // Sloučení plugins\n    if (truncatedConfig.plugins) {\n      merged.plugins = merged.plugins || {};\n      if (truncatedConfig.plugins.tooltip) {\n        merged.plugins.tooltip = merged.plugins.tooltip || {};\n        merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};\n        // Sloučení tooltip callbacks - zachováme existující a přidáme nové\n        Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {\n          merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];\n        });\n      }\n    }\n    return merged;\n  }\n  // ========================================\n  // PŘÍKLAD POUŽITÍ ZKRÁCENÝCH POPISKŮ:\n  // ========================================\n  //\n  // // 1. Vytvořte konfiguraci pro zkrácené popisky\n  // const truncatedLabelsConfig = this.createTruncatedLabelsConfig(data.labels, {\n  //   enabled: true,           // Zapnout zkrácení\n  //   maxLength: 25,          // Max 25 znaků\n  //   showFullInTooltip: true // Plný název v tooltip\n  // });\n  //\n  // // 2. Vytvořte základní konfiguraci grafu\n  // const baseOptions = {\n  //   responsive: true,\n  //   plugins: { /* vaše plugins */ },\n  //   scales: { /* vaše scales */ }\n  // };\n  //\n  // // 3. Sloučte konfigurace\n  // const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n  //\n  // // 4. Vytvořte graf\n  // new Chart(ctx, {\n  //   type: 'bar',\n  //   data: { labels: data.labels, datasets: data.datasets },\n  //   options: finalOptions\n  // });\n  //\n  // ========================================\n  /**\r\n   * Příprava dat pro graf agregovaných výkonnostních metrik\r\n   */\n  prepareAggregatedPerformanceChartData(data) {\n    console.log('prepareAggregatedPerformanceChartData - vstupní data:', data);\n    // Výchozí prázdná struktura pro data grafu\n    const chartData = {\n      labels: [],\n      avgData: [],\n      medianData: [],\n      p95Data: [],\n      p99Data: [],\n      minData: [],\n      maxData: [],\n      totalCountData: [],\n      title: 'Výkon v čase'\n    };\n    // Kontrola, zda data obsahují potřebné údaje\n    if (!data) {\n      console.warn('prepareAggregatedPerformanceChartData - Chybí data pro graf');\n      return chartData;\n    }\n    try {\n      // Pokud jsou data již připravena v požadovaném formátu z API\n      if (data.labels && Array.isArray(data.labels) && data.labels.length > 0) {\n        console.log('prepareAggregatedPerformanceChartData - Používám předpřipravená data z API');\n        chartData.labels = data.labels;\n        chartData.avgData = data.avgData || [];\n        chartData.medianData = data.medianData || [];\n        chartData.p95Data = data.p95Data || [];\n        chartData.p99Data = data.p99Data || [];\n        chartData.minData = data.minData || [];\n        chartData.maxData = data.maxData || [];\n        chartData.totalCountData = data.totalCountData || [];\n        // Nastavení titulku grafu\n        if (data.title) {\n          chartData.title = data.title;\n        }\n      }\n      // Pokud jsou data ve formátu denních dat\n      else if (data.dailyData && Array.isArray(data.dailyData) && data.dailyData.length > 0) {\n        console.log('prepareAggregatedPerformanceChartData - Zpracovávám denní data');\n        const dailyData = data.dailyData;\n        // Seřazení dat podle data\n        dailyData.sort((a, b) => {\n          const dateA = a.date ? new Date(a.date).getTime() : 0;\n          const dateB = b.date ? new Date(b.date).getTime() : 0;\n          return dateA - dateB;\n        });\n        // Naplnění polí pro graf\n        chartData.labels = dailyData.map(item => {\n          if (item.date) {\n            const date = new Date(item.date);\n            return this.formatDate(date);\n          }\n          return '';\n        }).filter(label => label !== '');\n        // Mapování dat s kontrolou existence vlastností\n        chartData.avgData = dailyData.map(item => item.avg !== undefined ? item.avg : 0);\n        chartData.medianData = dailyData.map(item => item.median !== undefined ? item.median : 0);\n        chartData.p95Data = dailyData.map(item => item.percentil95 !== undefined ? item.percentil95 : 0);\n        chartData.p99Data = dailyData.map(item => item.percentil99 !== undefined ? item.percentil99 : 0);\n        chartData.minData = dailyData.map(item => item.min !== undefined ? item.min : 0);\n        chartData.maxData = dailyData.map(item => item.max !== undefined ? item.max : 0);\n        chartData.totalCountData = dailyData.map(item => item.totalCount !== undefined ? item.totalCount : 0);\n        // Nastavení titulku grafu\n        if (data.title) {\n          chartData.title = data.title;\n        }\n      } else {\n        console.warn('prepareAggregatedPerformanceChartData - Neznámý formát dat');\n        // Pokus o extrakci dat z neznámého formátu\n        if (typeof data === 'object') {\n          // Pokus o nalezení polí s daty\n          for (const key in data) {\n            if (key === 'labels' && Array.isArray(data[key])) {\n              chartData.labels = data[key].map(item => String(item));\n            } else if (key === 'avgData' && Array.isArray(data[key])) {\n              chartData.avgData = data[key].map(item => Number(item) || 0);\n            } else if (key === 'medianData' && Array.isArray(data[key])) {\n              chartData.medianData = data[key].map(item => Number(item) || 0);\n            } else if (key === 'p95Data' && Array.isArray(data[key])) {\n              chartData.p95Data = data[key].map(item => Number(item) || 0);\n            } else if (key === 'p99Data' && Array.isArray(data[key])) {\n              chartData.p99Data = data[key].map(item => Number(item) || 0);\n            } else if (key === 'minData' && Array.isArray(data[key])) {\n              chartData.minData = data[key].map(item => Number(item) || 0);\n            } else if (key === 'maxData' && Array.isArray(data[key])) {\n              chartData.maxData = data[key].map(item => Number(item) || 0);\n            } else if (key === 'totalCountData' && Array.isArray(data[key])) {\n              chartData.totalCountData = data[key].map(item => Number(item) || 0);\n            } else if (key === 'title' && typeof data[key] === 'string') {\n              chartData.title = data[key];\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.error('prepareAggregatedPerformanceChartData - Chyba při zpracování dat:', error);\n    }\n    console.log('prepareAggregatedPerformanceChartData - výstupní data:', chartData);\n    return chartData;\n  }\n  /**\r\n   * Inicializace popoverů pro nápovědu\r\n   */\n  initPopovers() {\n    console.log('initPopovers - Inicializuji popovery');\n    // Definice obsahu nápověd\n    const helpContent = {\n      'aggregated-performance': 'Graf zobrazuje agregované výkonnostní metriky DIS metod v čase. ' + 'Pro každý den jsou zobrazeny hodnoty jako průměr, medián, 95. percentil, 99. percentil, minimum, maximum a počet volání. ' + 'Kliknutím na legendu můžete zobrazit nebo skrýt jednotlivé metriky. ' + 'Výchozí zobrazení obsahuje průměr, medián a 95. percentil, ostatní metriky jsou skryté.',\n      'instances-comparison': 'Graf umožňuje srovnání výkonu stejných DIS metod mezi různými instancemi. ' + 'Vyberte alespoň dvě instance, které chcete porovnat, a volitelně filtrujte podle konkrétní třídy nebo metody. ' + 'Bez filtrace se zobrazují pouze metody s významnou odezvou (alespoň u jedné instance větší než průměrná odezva DIS metod). ' + 'Graf zobrazuje průměrnou dobu odezvy metod pro každou vybranou instanci.',\n      'versions-comparison': 'Graf umožňuje srovnání výkonu DIS metod mezi různými verzemi jedné instance. ' + 'Vyberte instanci a volitelně filtrujte podle konkrétní třídy nebo metody. ' + 'Bez filtrace se zobrazují pouze metody s významnou odezvou (alespoň u jedné verze větší než průměrná odezva DIS metod). ' + 'Graf zobrazuje průměrnou dobu odezvy metod pro každou verzi aplikace v rámci vybrané instance.',\n      'slowest-methods': 'Tabulka zobrazuje nejpomalejší DIS metody seřazené podle 95. percentilu doby odezvy. ' + 'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' + 'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n      'methods': 'Tabulka zobrazuje DIS metody podle zvoleného filtru (nejpomalejší, nejčastěji volané nebo všechny). ' + 'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' + 'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n      'most-called-methods': 'Tabulka zobrazuje nejčastěji volané DIS metody seřazené podle počtu volání. ' + 'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' + 'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n      'all-methods': 'Tabulka zobrazuje všechny DIS metody. ' + 'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' + 'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n      'method-detail': 'Graf zobrazuje výkon konkrétní DIS metody v čase. ' + 'Pro každý den jsou zobrazeny hodnoty jako průměr, medián, 95. percentil, 99. percentil, minimum, maximum a počet volání. ' + 'Kliknutím na legendu můžete zobrazit nebo skrýt jednotlivé metriky. ' + 'Nad grafem jsou zobrazeny souhrnné statistiky metody za celé zvolené období.',\n      'avg-response-time': 'Průměrná doba odezvy metody v milisekundách za celé zvolené období. ' + 'Průměr je vypočítán jako aritmetický průměr všech naměřených hodnot. ' + 'Může být ovlivněn extrémními hodnotami, proto je vhodné sledovat také medián.',\n      'median-response-time': 'Medián doby odezvy metody v milisekundách za celé zvolené období. ' + 'Medián představuje prostřední hodnotu ze všech naměřených časů (50. percentil). ' + 'Polovina volání byla rychlejší a polovina pomalejší než tato hodnota. ' + 'Medián není ovlivněn extrémními hodnotami, proto lépe reprezentuje typickou dobu odezvy než průměr.',\n      'p95-response-time': '95. percentil doby odezvy metody v milisekundách za celé zvolené období. ' + 'Tato hodnota znamená, že 95% všech volání metody bylo rychlejších než tato hodnota. ' + 'Zbývajících 5% volání bylo pomalejších. ' + 'Tento ukazatel je důležitý pro identifikaci problémů s výkonem, které ovlivňují menší část uživatelů.',\n      'total-calls': 'Celkový počet volání metody za zvolené časové období. ' + 'Tento údaj ukazuje, jak často je metoda používána. ' + 'Vysoký počet volání v kombinaci s delší dobou odezvy může indikovat potenciální problém s výkonem aplikace.'\n    };\n    try {\n      // Inicializace popoverů pomocí Bootstrap API\n      setTimeout(() => {\n        try {\n          // Kontrola, zda je bootstrap k dispozici\n          if (typeof bootstrap === 'undefined') {\n            console.error('initPopovers - Bootstrap není k dispozici!');\n            return;\n          }\n          // Kontrola, zda je Popover k dispozici\n          if (typeof bootstrap.Popover === 'undefined') {\n            console.error('initPopovers - Bootstrap.Popover není k dispozici!');\n            return;\n          }\n          // Nejprve zrušíme všechny existující popovery\n          const existingPopovers = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\n          existingPopovers.forEach(el => {\n            try {\n              // @ts-ignore\n              const popover = bootstrap.Popover.getInstance(el);\n              if (popover) {\n                popover.dispose();\n              }\n            } catch (error) {\n              console.warn('initPopovers - Chyba při rušení existujícího popoveru:', error);\n            }\n          });\n          // Nyní inicializujeme nové popovery\n          const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\n          console.log('initPopovers - Nalezeno', popoverTriggerList.length, 'elementů s popovery');\n          popoverTriggerList.forEach(popoverTriggerEl => {\n            try {\n              const helpType = popoverTriggerEl.getAttribute('data-help-type');\n              const content = helpContent[helpType] || 'Nápověda není k dispozici.';\n              // Přidáme titulek popoveru\n              let title = '';\n              switch (helpType) {\n                case 'aggregated-performance':\n                  title = 'Agregovaný výkon v čase';\n                  break;\n                case 'instances-comparison':\n                  title = 'Srovnání výkonu metod mezi instancemi';\n                  break;\n                case 'versions-comparison':\n                  title = 'Srovnání výkonu metod mezi verzemi';\n                  break;\n                case 'slowest-methods':\n                  title = 'Nejpomalejší metody';\n                  break;\n                case 'most-called-methods':\n                  title = 'Nejčastěji volané metody';\n                  break;\n                case 'all-methods':\n                  title = 'Všechny metody';\n                  break;\n                case 'method-detail':\n                  title = 'Detail metody';\n                  break;\n                case 'avg-response-time':\n                  title = 'Průměrná doba odezvy';\n                  break;\n                case 'median-response-time':\n                  title = 'Medián doby odezvy';\n                  break;\n                case 'p95-response-time':\n                  title = '95. percentil';\n                  break;\n                case 'total-calls':\n                  title = 'Celkový počet volání';\n                  break;\n                default:\n                  title = 'Nápověda';\n              }\n              // @ts-ignore\n              new bootstrap.Popover(popoverTriggerEl, {\n                container: 'body',\n                trigger: 'hover',\n                placement: 'top',\n                title: title,\n                content: content,\n                html: true\n              });\n            } catch (error) {\n              console.error('initPopovers - Chyba při inicializaci popoveru:', error);\n            }\n          });\n          console.log('initPopovers - Popovery úspěšně inicializovány');\n        } catch (error) {\n          console.error('initPopovers - Chyba při inicializaci popoverů:', error);\n        }\n      }, 500);\n    } catch (error) {\n      console.error('initPopovers - Chyba při inicializaci popoverů:', error);\n    }\n  }\n  /**\r\n   * Formátování data\r\n   */\n  formatDate(date) {\n    return this.datePipe.transform(date, 'dd.MM.yyyy') || '';\n  }\n  /**\r\n   * Úprava délky pole na požadovanou délku\r\n   * @param array Pole, které chceme upravit\r\n   * @param length Požadovaná délka pole\r\n   * @returns Upravené pole s požadovanou délkou\r\n   */\n  adjustArrayLength(array, length) {\n    if (!array) {\n      return new Array(length).fill(0);\n    }\n    if (array.length === length) {\n      return array;\n    }\n    if (array.length > length) {\n      return array.slice(0, length);\n    }\n    // Pokud je pole kratší než požadovaná délka, doplníme ho nulami\n    const result = [...array];\n    while (result.length < length) {\n      result.push(0);\n    }\n    return result;\n  }\n  /**\r\n   * Určí, které osy Y mají být zobrazené na základě viditelných metrik\r\n   */\n  getVisibleAxes(datasets, chart) {\n    let showLeftAxis = false;\n    let showRightAxis = false;\n    datasets.forEach((dataset, index) => {\n      // Pokud máme referenci na graf, použijeme Chart.js API pro zjištění viditelnosti\n      const isVisible = chart ? chart.isDatasetVisible(index) : !dataset.hidden;\n      if (isVisible) {\n        if (dataset.yAxisID === 'y1') {\n          // Metrika \"Počet volání\" používá pravou osu\n          showRightAxis = true;\n        } else {\n          // Všechny ostatní metriky (doba odezvy) používají levou osu\n          showLeftAxis = true;\n        }\n      }\n    });\n    return {\n      showLeftAxis,\n      showRightAxis\n    };\n  }\n  /**\r\n   * Aktualizuje viditelnost os Y na základě aktuálně viditelných metrik\r\n   */\n  updateAxisVisibility(chart) {\n    if (!chart || !chart.data || !chart.data.datasets) {\n      return;\n    }\n    const visibleAxes = this.getVisibleAxes(chart.data.datasets, chart);\n    // Aktualizace konfigurace os\n    if (chart.options && chart.options.scales) {\n      if (chart.options.scales.y) {\n        chart.options.scales.y.display = visibleAxes.showLeftAxis;\n        chart.options.scales.y.title.display = visibleAxes.showLeftAxis;\n      }\n      if (chart.options.scales.y1) {\n        chart.options.scales.y1.display = visibleAxes.showRightAxis;\n        chart.options.scales.y1.title.display = visibleAxes.showRightAxis;\n        // Zobrazit mřížku pro pravou osu pouze když levá osa není viditelná\n        chart.options.scales.y1.grid.drawOnChartArea = !visibleAxes.showLeftAxis;\n      }\n    }\n    // Překreslení grafu s novými nastaveními\n    chart.update();\n  }\n  static {\n    this.ɵfac = function PerformanceComponent_Factory(t) {\n      return new (t || PerformanceComponent)(i0.ɵɵdirectiveInject(i1.PerformanceService), i0.ɵɵdirectiveInject(i2.InstanceService), i0.ɵɵdirectiveInject(i3.DatePipe), i0.ɵɵdirectiveInject(i4.ChartModalService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.TableSortingService), i0.ɵɵdirectiveInject(i6.TextUtilsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PerformanceComponent,\n      selectors: [[\"app-performance\"]],\n      viewQuery: function PerformanceComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.aggregatedPerformanceChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.instancesComparisonChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.versionsComparisonChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.methodDetailChartRef = _t.first);\n        }\n      },\n      decls: 11,\n      vars: 8,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [3, \"tabs\", \"activeTabId\", \"tabChange\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [3, \"entityType\", \"fields\", \"filterChange\"], [\"class\", \"text-center my-5\", 4, \"ngIf\"], [1, \"text-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"aggregated-performance\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"title\", \"Zobrazit graf na celou obrazovku\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-arrows-fullscreen\"], [1, \"card-body\"], [1, \"chart-container\", 2, \"position\", \"relative\", \"height\", \"400px\", \"width\", \"100%\"], [\"class\", \"chart-loading-overlay\", 4, \"ngIf\"], [\"class\", \"no-data-message\", 4, \"ngIf\"], [\"width\", \"800\", \"height\", \"400\", \"style\", \"display: block;\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"slowest-methods\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"dark-header\", \"table-header-override\"], [1, \"dark-header-row\"], [1, \"sortable-header\", 3, \"click\"], [1, \"bi\"], [\"style\", \"cursor: pointer;\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-loading-overlay\"], [1, \"no-data-message\"], [1, \"bi\", \"bi-bar-chart-line\"], [\"width\", \"800\", \"height\", \"400\", 2, \"display\", \"block\"], [\"aggregatedPerformanceChart\", \"\"], [2, \"cursor\", \"pointer\"], [\"title\", \"Zobrazit detail\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-info-circle\"], [\"colspan\", \"10\", 1, \"text-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"instances-comparison\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"instancesComparisonChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"versions-comparison\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"versionsComparisonChart\", \"\"], [1, \"mb-0\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"slowest-methods\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"most-called-methods\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"all-methods\", 4, \"ngIf\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"slowest-methods\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"most-called-methods\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"all-methods\"], [1, \"row\", \"mb-4\"], [1, \"col-md-3\", \"mb-3\"], [1, \"card\", \"h-100\"], [1, \"card-body\", \"text-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"avg-response-time\", 1, \"card-title\", 2, \"cursor\", \"help\"], [1, \"display-4\", \"mb-0\"], [1, \"text-muted\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"median-response-time\", 1, \"card-title\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"p95-response-time\", 1, \"card-title\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"total-calls\", 1, \"card-title\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"method-detail\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"methodDetailChart\", \"\"]],\n      template: function PerformanceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"V\\u00FDkon DIS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"app-tab-navigation\", 2);\n          i0.ɵɵlistener(\"tabChange\", function PerformanceComponent_Template_app_tab_navigation_tabChange_4_listener($event) {\n            return ctx.changeTab($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, PerformanceComponent_div_5_Template, 3, 1, \"div\", 3);\n          i0.ɵɵtemplate(6, PerformanceComponent_div_6_Template, 4, 4, \"div\", 4);\n          i0.ɵɵtemplate(7, PerformanceComponent_div_7_Template, 4, 4, \"div\", 4);\n          i0.ɵɵtemplate(8, PerformanceComponent_div_8_Template, 4, 4, \"div\", 4);\n          i0.ɵɵtemplate(9, PerformanceComponent_div_9_Template, 4, 4, \"div\", 4);\n          i0.ɵɵtemplate(10, PerformanceComponent_div_10_Template, 4, 4, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"tabs\", ctx.tabs)(\"activeTabId\", ctx.activeTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"aggregated\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"instances-comparison\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"versions-comparison\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"methods\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"method-detail\");\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i7.AdvancedFilterComponent, i8.TabNavigationComponent, i3.DecimalPipe],\n      styles: [\".chart-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 400px;\\n  width: 100%;\\n}\\n\\n.chart-loading-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 10;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  text-align: center;\\n  z-index: 5;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 1rem;\\n  color: #6c757d;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #6c757d;\\n}\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .chart-loading-overlay[_ngcontent-%COMP%] {\\n    background-color: rgba(33, 37, 41, 0.8);\\n  }\\n\\n  .no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  cursor: help;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n}\\n\\n.btn-outline-info[_ngcontent-%COMP%] {\\n  color: #0dcaf0;\\n  border-color: #0dcaf0;\\n}\\n\\n.btn-outline-info[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n  background-color: #0dcaf0;\\n  border-color: #0dcaf0;\\n}\\n\\n\\n.sortable-header[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  position: relative;\\n}\\n\\n.sortable-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  margin-left: 5px;\\n  opacity: 0.7;\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n\\n.sortable-header[_ngcontent-%COMP%]   i.bi-sort-up[_ngcontent-%COMP%], .sortable-header[_ngcontent-%COMP%]   i.bi-sort-down[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  color: white;\\n  font-size: 1rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAIA,SAASA,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAI/C,SAAsBC,cAAc,QAAQ,qDAAqD;;;;;;;;;;;;;;;;;ICK/FC,8BAAuF;IACrFA,YACA;IAAAA,iCAA+D;IAAvBA;MAAAA;MAAA;MAAA,qCAAiB,IAAI;IAAA,EAAC;IAACA,iBAAS;;;;IADxEA,eACA;IADAA,6CACA;;;;;IAYAA,8BAA8C;IAEZA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,mDAAe;IAAAA,iBAAI;;;;;IAc7BA,+BAAoE;IAElCA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,iDAAuB;IAAAA,iBAAI;;;;;IAE7CA,+BAA6D;IAC3DA,wBAAoC;IACpCA,yBAAG;IAAAA,qFAAiD;IAAAA,iBAAI;;;;;IAE1DA,iCAA6H;;;;;;IAkFzHA,8BAAmE;IAC7DA,YAAyB;IAAAA,iBAAK;IAClCA,0BAAI;IAAAA,YAAsB;IAAAA,iBAAK;IAC/BA,0BAAI;IAAAA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAAAA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAoC;;IAAAA,iBAAK;IAC7CA,2BAAI;IAAAA,aAAyC;;IAAAA,iBAAK;IAClDA,2BAAI;IAC0CA;MAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;IAC5EA,yBAAiC;IACnCA,iBAAS;;;;IAZPA,eAAyB;IAAzBA,6CAAyB;IACzBA,eAAsB;IAAtBA,0CAAsB;IACtBA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAiC;IAAjCA,oEAAiC;IACjCA,eAAiC;IAAjCA,qEAAiC;IACjCA,eAAiC;IAAjCA,qEAAiC;IACjCA,eAAoC;IAApCA,wEAAoC;IACpCA,eAAyC;IAAzCA,6EAAyC;;;;;IAO/CA,0BAA2D;IACpBA,4DAAwB;IAAAA,iBAAK;;;;;;IAxHhFA,2BAAsB;IAIwFA,sDAAuB;IAAAA,iBAAK;IACpIA,kCAA0K;IAA9HA;MAAAA;MAAA;MAAA,OAASA,+EAAgD,wCAAyB,CAAC;IAAA,EAAC;IAC9HA,wBAAuC;IACzCA,iBAAS;IAEXA,+BAAuB;IAEnBA,kFAKM;IACNA,oFAGM;IACNA,0FAA6H;IAC/HA,iBAAM;IAKVA,gCAAuB;IAE8EA,8CAAmB;IAAAA,iBAAK;IAE3HA,gCAAuB;IAKTA;MAAAA;MAAA;MAAA,OAASA,8BAAO,cAAc,CAAC;IAAA,EAAC;IAClCA,2BACA;IAAAA,yBAGsD;IACxDA,iBAAK;IACLA,+BAA0D;IAAtDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,WAAW,CAAC;IAAA,EAAC;IAC/BA,kCACA;IAAAA,yBAGmD;IACrDA,iBAAK;IACLA,+BAA2D;IAAvDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,YAAY,CAAC;IAAA,EAAC;IAChCA,yBACA;IAAAA,yBAGoD;IACtDA,iBAAK;IACLA,+BAA2D;IAAvDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,YAAY,CAAC;IAAA,EAAC;IAChCA,8CACA;IAAAA,yBAGoD;IACtDA,iBAAK;IACLA,+BAAoD;IAAhDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,KAAK,CAAC;IAAA,EAAC;IACzBA,2BACA;IAAAA,yBAG6C;IAC/CA,iBAAK;IACLA,+BAAoD;IAAhDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,KAAK,CAAC;IAAA,EAAC;IACzBA,2BACA;IAAAA,yBAG6C;IAC/CA,iBAAK;IACLA,+BAAoD;IAAhDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,KAAK,CAAC;IAAA,EAAC;IACzBA,wCACA;IAAAA,yBAG6C;IAC/CA,iBAAK;IACLA,+BAAuD;IAAnDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,QAAQ,CAAC;IAAA,EAAC;IAC5BA,mCACA;IAAAA,yBAGgD;IAClDA,iBAAK;IACLA,+BAA4D;IAAxDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,aAAa,CAAC;IAAA,EAAC;IACjCA,qCACA;IAAAA,yBAGqD;IACvDA,iBAAK;IACLA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,oFAeK;IACLA,iFAEK;IACPA,iBAAQ;;;;IA/GJA,eAA8B;IAA9BA,sDAA8B;IAM9BA,eAA6B;IAA7BA,qDAA6B;IAI1BA,eAA4B;IAA5BA,oDAA4B;IAkB3BA,gBAA6E;IAA7EA,oGAA6E;IAO7EA,eAA0E;IAA1EA,iGAA0E;IAO1EA,eAA2E;IAA3EA,kGAA2E;IAO3EA,eAA2E;IAA3EA,kGAA2E;IAO3EA,eAAoE;IAApEA,2FAAoE;IAOpEA,eAAoE;IAApEA,2FAAoE;IAOpEA,eAAoE;IAApEA,2FAAoE;IAOpEA,eAAuE;IAAvEA,8FAAuE;IAOvEA,eAA4E;IAA5EA,mGAA4E;IAQ3DA,eAAiB;IAAjBA,+CAAiB;IAgBnCA,eAAoD;IAApDA,mFAAoD;;;;;;IAtIvEA,2BAAwC;IAKpCA;MAAAA;MAAA;MAAA,OAAgBA,uDAAgC;IAAA,EAAC;IACnDA,iBAAsB;IAEtBA,2EAKM;IAENA,6EA+HM;IACRA,iBAAM;;;;IA5IFA,eAAuC;IAAvCA,qDAAuC;IAKnCA,eAAa;IAAbA,qCAAa;IAObA,eAAc;IAAdA,sCAAc;;;;;IA2IpBA,8BAA8C;IAEZA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,mDAAe;IAAAA,iBAAI;;;;;IAc7BA,+BAA6E;IAE3CA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,iDAAuB;IAAAA,iBAAI;;;;;IAE7CA,+BAAsE;IACpEA,wBAAoC;IACpCA,yBAAG;IAAAA,qFAAiD;IAAAA,iBAAI;;;;;IAE1DA,iCAAoI;;;;;;IArB5IA,2BAAsB;IAIsFA,oEAAqC;IAAAA,iBAAK;IAChJA,kCAAsL;IAA1IA;MAAAA;MAAA;MAAA,OAASA,6EAA8C,sDAAuC,CAAC;IAAA,EAAC;IAC1IA,wBAAuC;IACzCA,iBAAS;IAEXA,+BAAuB;IAEnBA,kFAKM;IACNA,oFAGM;IACNA,0FAAoI;IACtIA,iBAAM;;;;IAXEA,eAAuC;IAAvCA,gEAAuC;IAMvCA,eAAsC;IAAtCA,+DAAsC;IAInCA,eAAqC;IAArCA,8DAAqC;;;;;;IApCxDA,2BAAkD;IAK9CA;MAAAA;MAAA;MAAA,OAAgBA,gEAAyC;IAAA,EAAC;IAC5DA,iBAAsB;IAEtBA,2EAKM;IAENA,4EAyBM;IACRA,iBAAM;;;;IAtCFA,eAAiD;IAAjDA,+DAAiD;IAK7CA,eAAa;IAAbA,qCAAa;IAObA,eAAc;IAAdA,sCAAc;;;;;IAqCpBA,8BAA8C;IAEZA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,mDAAe;IAAAA,iBAAI;;;;;IAc7BA,+BAA4E;IAE1CA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,iDAAuB;IAAAA,iBAAI;;;;;IAE7CA,+BAAqE;IACnEA,wBAAoC;IACpCA,yBAAG;IAAAA,qFAAiD;IAAAA,iBAAI;;;;;IAE1DA,iCAAkI;;;;;;IArB1IA,2BAAsB;IAIqFA,iEAAkC;IAAAA,iBAAK;IAC5IA,kCAAkL;IAAtIA;MAAAA;MAAA;MAAA,OAASA,4EAA6C,mDAAoC,CAAC;IAAA,EAAC;IACtIA,wBAAuC;IACzCA,iBAAS;IAEXA,+BAAuB;IAEnBA,kFAKM;IACNA,oFAGM;IACNA,0FAAkI;IACpIA,iBAAM;;;;IAXEA,eAAsC;IAAtCA,+DAAsC;IAMtCA,eAAqC;IAArCA,8DAAqC;IAIlCA,eAAoC;IAApCA,6DAAoC;;;;;;IApCvDA,2BAAiD;IAK7CA;MAAAA;MAAA;MAAA,OAAgBA,+DAAwC;IAAA,EAAC;IAC3DA,iBAAsB;IAEtBA,2EAKM;IAENA,4EAyBM;IACRA,iBAAM;;;;IAtCFA,eAAgD;IAAhDA,8DAAgD;IAK5CA,eAAa;IAAbA,qCAAa;IAObA,eAAc;IAAdA,sCAAc;;;;;IAqCpBA,8BAA8C;IAEZA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,mDAAe;IAAAA,iBAAI;;;;;IAQ7BA,gCAAyG;IAAAA,6CAAmB;IAAAA,iBAAO;;;;;IACnIA,gCAAiH;IAAAA,uDAAwB;IAAAA,iBAAO;;;;;IAChJA,gCAAiG;IAAAA,mCAAc;IAAAA,iBAAO;;;;;;IA4EhHA,8BAAmE;IAC7DA,YAAyB;IAAAA,iBAAK;IAClCA,0BAAI;IAAAA,YAAsB;IAAAA,iBAAK;IAC/BA,0BAAI;IAAAA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAAAA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAoC;;IAAAA,iBAAK;IAC7CA,2BAAI;IAAAA,aAAyC;;IAAAA,iBAAK;IAClDA,2BAAI;IAC0CA;MAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;IAC5EA,yBAAiC;IACnCA,iBAAS;;;;IAZPA,eAAyB;IAAzBA,6CAAyB;IACzBA,eAAsB;IAAtBA,0CAAsB;IACtBA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAiC;IAAjCA,oEAAiC;IACjCA,eAAiC;IAAjCA,qEAAiC;IACjCA,eAAiC;IAAjCA,qEAAiC;IACjCA,eAAoC;IAApCA,wEAAoC;IACpCA,eAAyC;IAAzCA,6EAAyC;;;;;IAO/CA,0BAA2D;IACpBA,4DAAwB;IAAAA,iBAAK;;;;;IAlBtEA,6BAAuD;IACrDA,kGAeK;IACLA,+FAEK;IACPA,0BAAe;;;;IAnBUA,eAAiB;IAAjBA,gDAAiB;IAgBnCA,eAAoD;IAApDA,qFAAoD;;;;;;IAMzDA,8BAAsE;IAChEA,YAAyB;IAAAA,iBAAK;IAClCA,0BAAI;IAAAA,YAAsB;IAAAA,iBAAK;IAC/BA,0BAAI;IAAAA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAAAA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAoC;;IAAAA,iBAAK;IAC7CA,2BAAI;IAAAA,aAAyC;;IAAAA,iBAAK;IAClDA,2BAAI;IAC0CA;MAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;IAC5EA,yBAAiC;IACnCA,iBAAS;;;;IAZPA,eAAyB;IAAzBA,6CAAyB;IACzBA,eAAsB;IAAtBA,0CAAsB;IACtBA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAiC;IAAjCA,oEAAiC;IACjCA,eAAiC;IAAjCA,qEAAiC;IACjCA,eAAiC;IAAjCA,qEAAiC;IACjCA,eAAoC;IAApCA,wEAAoC;IACpCA,eAAyC;IAAzCA,6EAAyC;;;;;IAO/CA,0BAAiE;IAC1BA,4DAAwB;IAAAA,iBAAK;;;;;IAlBtEA,6BAA2D;IACzDA,kGAeK;IACLA,+FAEK;IACPA,0BAAe;;;;IAnBUA,eAAoB;IAApBA,mDAAoB;IAgBtCA,eAA0D;IAA1DA,2FAA0D;;;;;;IAM/DA,8BAA+D;IACzDA,YAAyB;IAAAA,iBAAK;IAClCA,0BAAI;IAAAA,YAAsB;IAAAA,iBAAK;IAC/BA,0BAAI;IAAAA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAAAA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAiC;;IAAAA,iBAAK;IAC1CA,2BAAI;IAAAA,aAAoC;;IAAAA,iBAAK;IAC7CA,2BAAI;IAAAA,aAAyC;;IAAAA,iBAAK;IAClDA,2BAAI;IAC0CA;MAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;IAC5EA,yBAAiC;IACnCA,iBAAS;;;;IAZPA,eAAyB;IAAzBA,6CAAyB;IACzBA,eAAsB;IAAtBA,0CAAsB;IACtBA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAiC;IAAjCA,oEAAiC;IACjCA,eAAiC;IAAjCA,qEAAiC;IACjCA,eAAiC;IAAjCA,qEAAiC;IACjCA,eAAoC;IAApCA,wEAAoC;IACpCA,eAAyC;IAAzCA,6EAAyC;;;;;IAO/CA,0BAAmD;IACZA,4DAAwB;IAAAA,iBAAK;;;;;IAlBtEA,6BAAmD;IACjDA,kGAeK;IACLA,+FAEK;IACPA,0BAAe;;;;IAnBUA,eAAa;IAAbA,4CAAa;IAgB/BA,eAA4C;IAA5CA,6EAA4C;;;;;;IA/I/DA,2BAAsB;IAKdA,oFAAmI;IACnIA,oFAAgJ;IAChJA,oFAAsH;IACxHA,iBAAK;IAEPA,+BAAuB;IAKTA;MAAAA;MAAA;MAAA,OAASA,8BAAO,cAAc,CAAC;IAAA,EAAC;IAClCA,2BACA;IAAAA,yBAGsD;IACxDA,iBAAK;IACLA,+BAA0D;IAAtDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,WAAW,CAAC;IAAA,EAAC;IAC/BA,kCACA;IAAAA,yBAGmD;IACrDA,iBAAK;IACLA,+BAA2D;IAAvDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,YAAY,CAAC;IAAA,EAAC;IAChCA,yBACA;IAAAA,yBAGoD;IACtDA,iBAAK;IACLA,+BAA2D;IAAvDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,YAAY,CAAC;IAAA,EAAC;IAChCA,8CACA;IAAAA,yBAGoD;IACtDA,iBAAK;IACLA,+BAAoD;IAAhDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,KAAK,CAAC;IAAA,EAAC;IACzBA,2BACA;IAAAA,yBAG6C;IAC/CA,iBAAK;IACLA,+BAAoD;IAAhDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,KAAK,CAAC;IAAA,EAAC;IACzBA,2BACA;IAAAA,yBAG6C;IAC/CA,iBAAK;IACLA,+BAAoD;IAAhDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,KAAK,CAAC;IAAA,EAAC;IACzBA,wCACA;IAAAA,yBAG6C;IAC/CA,iBAAK;IACLA,+BAAuD;IAAnDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,QAAQ,CAAC;IAAA,EAAC;IAC5BA,mCACA;IAAAA,yBAGgD;IAClDA,iBAAK;IACLA,+BAA4D;IAAxDA;MAAAA;MAAA;MAAA,OAASA,8BAAO,aAAa,CAAC;IAAA,EAAC;IACjCA,qCACA;IAAAA,yBAGqD;IACvDA,iBAAK;IACLA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,qGAoBe;IAEfA,qGAoBe;IAEfA,qGAoBe;IACjBA,iBAAQ;;;;IA9IHA,eAAsC;IAAtCA,+DAAsC;IACtCA,eAA0C;IAA1CA,mEAA0C;IAC1CA,eAAkC;IAAlCA,2DAAkC;IAW/BA,eAA6E;IAA7EA,sGAA6E;IAO7EA,eAA0E;IAA1EA,mGAA0E;IAO1EA,eAA2E;IAA3EA,oGAA2E;IAO3EA,eAA2E;IAA3EA,oGAA2E;IAO3EA,eAAoE;IAApEA,6FAAoE;IAOpEA,eAAoE;IAApEA,6FAAoE;IAOpEA,eAAoE;IAApEA,6FAAoE;IAOpEA,eAAuE;IAAvEA,gGAAuE;IAOvEA,eAA4E;IAA5EA,qGAA4E;IAQnEA,eAAsC;IAAtCA,+DAAsC;IAsBtCA,eAA0C;IAA1CA,mEAA0C;IAsB1CA,eAAkC;IAAlCA,2DAAkC;;;;;;IA7I/DA,2BAAqC;IAKjCA;MAAAA;MAAA;MAAA,OAAgBA,oDAA6B;IAAA,EAAC;IAChDA,iBAAsB;IAEtBA,2EAKM;IAENA,6EAwJM;IACRA,iBAAM;;;;IArKFA,eAAoC;IAApCA,kDAAoC;IAKhCA,eAAa;IAAbA,qCAAa;IAObA,eAAc;IAAdA,sCAAc;;;;;IAoKpBA,8BAA8C;IAEZA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,mDAAe;IAAAA,iBAAI;;;;;IAsD7BA,+BAAsE;IAEpCA,+CAAW;IAAAA,iBAAO;IAElDA,6BAAgB;IAAAA,iDAAuB;IAAAA,iBAAI;;;;;IAE7CA,+BAA+D;IAC7DA,wBAAoC;IACpCA,yBAAG;IAAAA,qFAAiD;IAAAA,iBAAI;;;;;IAE1DA,iCAAsH;;;;;;IA7D9HA,2BAA0C;IAMyEA,mDAAoB;IAAAA,iBAAK;IAClIA,6BAA0B;IAAAA,YAAyD;;IAAAA,iBAAI;IACvFA,8BAAsB;IAAAA,mBAAE;IAAAA,iBAAI;IAIlCA,gCAA2B;IAGuFA,wCAAkB;IAAAA,iBAAK;IACnIA,8BAA0B;IAAAA,aAA4D;;IAAAA,iBAAI;IAC1FA,8BAAsB;IAAAA,mBAAE;IAAAA,iBAAI;IAIlCA,gCAA2B;IAGoFA,8BAAa;IAAAA,iBAAK;IAC3HA,8BAA0B;IAAAA,aAAiE;;IAAAA,iBAAI;IAC/FA,8BAAsB;IAAAA,mBAAE;IAAAA,iBAAI;IAIlCA,gCAA2B;IAG8EA,yDAAoB;IAAAA,iBAAK;IAC5HA,8BAA0B;IAAAA,aAA+C;IAAAA,iBAAI;IAC7EA,8BAAsB;IAAAA,4CAAiB;IAAAA,iBAAI;IAOnDA,gCAAuB;IAE4EA,8CAAmB;IAAAA,iBAAK;IACvHA,mCAA6J;IAAjHA;MAAAA;MAAA;MAAA,OAASA,sEAAuC,+BAAqB,CAAC;IAAA,EAAC;IACjHA,yBAAuC;IACzCA,iBAAS;IAEXA,gCAAuB;IAEnBA,qFAKM;IACNA,qFAGM;IACNA,2FAAsH;IACxHA,iBAAM;;;;IAvDwBA,eAAyD;IAAzDA,qJAAyD;IASzDA,gBAA4D;IAA5DA,0JAA4D;IAS5DA,gBAAiE;IAAjEA,+JAAiE;IASjEA,gBAA+C;IAA/CA,6HAA+C;IAiBrEA,gBAAgC;IAAhCA,yDAAgC;IAMhCA,eAA+B;IAA/BA,wDAA+B;IAI5BA,eAA8B;IAA9BA,uDAA8B;;;;;;IA5EjDA,2BAA2C;IAKvCA;MAAAA;MAAA;MAAA,OAAgBA,yDAAkC;IAAA,EAAC;IACrDA,iBAAsB;IAEtBA,4EAKM;IAENA,8EAiEM;IACRA,iBAAM;;;;IA9EFA,eAA0C;IAA1CA,wDAA0C;IAKtCA,eAAa;IAAbA,qCAAa;IAObA,eAAkC;IAAlCA,iEAAkC;;;ADva5C;AACAH,KAAK,CAACI,QAAQ,CAAC,GAAGH,aAAa,CAAC;AAOhC,OAAM,MAAOI,oBAAoB;EA0F/BC,YACUC,kBAAsC,EACtCC,eAAgC,EAChCC,QAAkB,EAClBC,iBAAoC,EACpCC,iBAAoC,EACpCC,mBAAwC,EACxCC,SAA2B;IAN3B,uBAAkB,GAAlBN,kBAAkB;IAClB,oBAAe,GAAfC,eAAe;IACf,aAAQ,GAARC,QAAQ;IACR,sBAAiB,GAAjBC,iBAAiB;IACjB,sBAAiB,GAAjBC,iBAAiB;IACjB,wBAAmB,GAAnBC,mBAAmB;IACnB,cAAS,GAATC,SAAS;IA1FnB;IACA,+BAA0B,GAAiB,IAAI;IAC/C,6BAAwB,GAAiB,IAAI;IAC7C,4BAAuB,GAAiB,IAAI;IAC5C,sBAAiB,GAAiB,IAAI;IAEtC;IACA,6BAAwB,GAAY,KAAK;IACzC,sCAAiC,GAAY,KAAK;IAClD,qCAAgC,GAAY,KAAK;IACjD,+BAA0B,GAAY,KAAK;IAE3C;IACA,2BAAsB,GAAY,IAAI;IACtC,oCAA+B,GAAY,IAAI;IAC/C,mCAA8B,GAAY,IAAI;IAC9C,6BAAwB,GAAY,IAAI;IAExC;IACA,mBAAc,GAAU,EAAE;IAC1B,sBAAiB,GAAU,EAAE;IAC7B,eAAU,GAAU,EAAE;IACtB,mBAAc,GAAQ,IAAI;IAC1B,qBAAgB,GAAQ,IAAI;IAC5B,cAAS,GAAU,EAAE;IACrB,8BAAyB,GAAQ,IAAI;IACrC,4BAAuB,GAAQ,IAAI;IACnC,2BAAsB,GAAQ,IAAI;IAClC,0BAAqB,GAAQ,IAAI;IAEjC;IACA,uBAAkB,GAAW,SAAS;IAEtC;IACA,eAAU,GAAW,aAAa;IAClC,kBAAa,GAAmB,MAAM;IAEtC;IACA,2BAAsB,GAAkB,EAAE;IAC1C,oCAA+B,GAAkB,EAAE;IACnD,mCAA8B,GAAkB,EAAE;IAClD,wBAAmB,GAAkB,EAAE;IACvC,6BAAwB,GAAkB,EAAE;IAE5C;IACA,YAAO,GAAG,IAAI;IACd,UAAK,GAAkB,IAAI;IAC3B,cAAS,GAAG,YAAY;IACxB,kBAAa,GAAmB,EAAE;IAElC;IACA,SAAI,GAAc,CAChB;MAAEC,EAAE,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAkB,CAAE,EAC/C;MAAED,EAAE,EAAE,sBAAsB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EAC1D;MAAED,EAAE,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACtD;MAAED,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAQ,CAAE,EAClC;MAAED,EAAE,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAe,CAAE,CAChD;IAED;IACA,yBAAoB,GAA0B;MAC5CC,MAAM,EAAE;KACT;IAED,kCAA6B,GAA0B;MACrDC,WAAW,EAAE,EAAE;MACfD,MAAM,EAAE,CAAC;MACTE,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;KACb;IAED,iCAA4B,GAA0B;MACpDH,MAAM,EAAE,EAAE;MACVE,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;KACb;IAED,2BAAsB,GAA0B;MAC9CH,MAAM,EAAE,EAAE;MACVE,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;KACb;EAUG;EAEJC,QAAQ;IACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C;IACA,IAAI,CAACC,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACA,MAAMC,aAAa,GAAG,IAAI,CAACC,aAAa,EAAE;IAC1C,IAAID,aAAa,EAAE;MACjBJ,OAAO,CAACC,GAAG,CAAC,gDAAgDG,aAAa,EAAE,CAAC;MAC5E,IAAI,CAACE,SAAS,GAAGF,aAAa;;IAGhC;IACA,IAAI,CAACG,oBAAoB,GAAG;MAC1BC,UAAU,EAAEC,SAAS;MACrBd,MAAM,EAAE,GAAG;MACXe,QAAQ,EAAED,SAAS;MACnBE,MAAM,EAAEF,SAAS;MACjBG,kBAAkB,EAAE;KACrB;IAED;IACA,IAAI,CAACzB,eAAe,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,IAAS,IAAI;QAClBhB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEe,IAAI,CAAC;QACzD,IAAI,CAACC,SAAS,GAAGD,IAAI;QAErB;QACA,IAAI,CAACE,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAACC,cAAc,CAAC,wBAAwB,CAAC;QAE7C;QACAnB,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QAEvE;QACAmB,UAAU,CAAC,MAAK;UACdpB,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAE,IAAI,CAACM,oBAAoB,CAAC;UACjG,IAAI,CAACc,OAAO,GAAG,IAAI;UAEnB;UACA,IAAIb,UAAU,GAAG,IAAI,CAACD,oBAAoB,CAACC,UAAU;UACrD,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,EAAE;YACpFA,UAAU,GAAGC,SAAS;;UAGxB,IAAIC,QAAc;UAClB,IAAIC,MAAM,GAAS,IAAIY,IAAI,EAAE;UAE7B;UACA,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACZ,MAAM,EAAE;YACrCe,QAAQ,GAAG,IAAIa,IAAI,EAAE;YACrBb,QAAQ,CAACc,OAAO,CAACb,MAAM,CAACc,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;;UAE1C;UAAA,KACK,IAAI,IAAI,CAAClB,oBAAoB,CAACZ,MAAM,KAAK,QAAQ,EAAE;YACtDe,QAAQ,GAAG,IAAIa,IAAI,EAAE;YACrBb,QAAQ,CAACc,OAAO,CAACb,MAAM,CAACc,OAAO,EAAE,GAAGC,MAAM,CAAC,IAAI,CAACnB,oBAAoB,CAACZ,MAAM,CAAC,CAAC;;UAE/E;UAAA,KACK;YACH;YACA,IAAI,IAAI,CAACY,oBAAoB,CAACG,QAAQ,EAAE;cACtCA,QAAQ,GAAG,IAAIa,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACG,QAAQ,CAAC;aACxD,MAAM;cACL;cACAA,QAAQ,GAAG,IAAIa,IAAI,EAAE;cACrBb,QAAQ,CAACc,OAAO,CAACb,MAAM,CAACc,OAAO,EAAE,GAAG,CAAC,CAAC;;YAGxC,IAAI,IAAI,CAAClB,oBAAoB,CAACI,MAAM,EAAE;cACpCA,MAAM,GAAG,IAAIY,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACI,MAAM,CAAC;;;UAIvDX,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;YAAEO,UAAU;YAAEE,QAAQ;YAAEC;UAAM,CAAE,CAAC;UAErF,MAAMgB,YAAY,GAAG,IAAI,CAACzC,kBAAkB,CAAC0C,wBAAwB,CAACpB,UAAU,EAAEE,QAAQ,EAAEC,MAAM,CAAC,CAACG,SAAS,CAAC;YAC5GC,IAAI,EAAGC,IAAS,IAAI;cAClBhB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEe,IAAI,CAAC;cAE3D;cACA,IAAI,CAACa,yBAAyB,GAAGb,IAAI;cAErC;cACA,IAAI,IAAI,CAACc,6BAA6B,EAAE;gBACtC,IAAI,CAACC,gCAAgC,CAACf,IAAI,CAAC;;cAG7C,IAAI,CAACK,OAAO,GAAG,KAAK;cAEpB;cACAD,UAAU,CAAC,MAAK;gBACd,IAAI,CAACY,kBAAkB,EAAE;cAC3B,CAAC,EAAE,GAAG,CAAC;YACT,CAAC;YACDC,KAAK,EAAGA,KAAU,IAAI;cACpBjC,OAAO,CAACiC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;cACjE,IAAI,CAACZ,OAAO,GAAG,KAAK;YACtB;WACD,CAAC;UAEF,IAAI,CAACa,aAAa,CAACC,IAAI,CAACR,YAAY,CAAC;QACvC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDM,KAAK,EAAGA,KAAU,IAAI;QACpBjC,OAAO,CAACiC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MACjE;KACD,CAAC;EACJ;EAEAG,eAAe;IACbpC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD;IACA,IAAI,CAACoC,YAAY,EAAE;IAEnB;IACA,IAAI,CAACC,4BAA4B,EAAE;IAEnC;IACAlB,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACmB,SAAS,CAAC,IAAI,CAACjC,SAAS,CAAC;IAChC,CAAC,EAAE,GAAG,CAAC;IAEP;IACA,IAAI,IAAI,CAACA,SAAS,KAAK,YAAY,IAAI,IAAI,CAACuB,yBAAyB,IAAI,IAAI,CAACC,6BAA6B,EAAE;MAC3G9B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrEmB,UAAU,CAAC,MAAK;QACd,IAAI,CAACW,gCAAgC,CAAC,IAAI,CAACF,yBAAyB,CAAC;MACvE,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL7B,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;MAEpF;MACAmB,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACd,SAAS,KAAK,YAAY,IAAI,IAAI,CAACuB,yBAAyB,IAAI,IAAI,CAACC,6BAA6B,EAAE;UAC3G9B,OAAO,CAACC,GAAG,CAAC,qFAAqF,CAAC;UAClG,IAAI,CAAC8B,gCAAgC,CAAC,IAAI,CAACF,yBAAyB,CAAC;;MAEzE,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQS,4BAA4B;IAClC;IACA,IAAI,CAACE,YAAY,CAACC,OAAO,CAAC,+BAA+B,CAAC,EAAE;MAC1D,MAAMC,oBAAoB,GAA4B;QACpD,aAAa,EAAE,IAAI;QACnB,aAAa,EAAE,IAAI;QACnB,oBAAoB,EAAE,IAAI;QAC1B,oBAAoB,EAAE,KAAK;QAC3B,cAAc,EAAE,KAAK;QACrB,cAAc,EAAE,KAAK;QACrB,cAAc,EAAE;OACjB;MACDF,YAAY,CAACG,OAAO,CAAC,+BAA+B,EAAEC,IAAI,CAACC,SAAS,CAACH,oBAAoB,CAAC,CAAC;;IAG7F;IACA,IAAI,CAACF,YAAY,CAACC,OAAO,CAAC,kCAAkC,CAAC,EAAE;MAC7D,MAAMK,sBAAsB,GAA4B;QACtD,aAAa,EAAE,IAAI;QACnB,aAAa,EAAE,IAAI;QACnB,oBAAoB,EAAE,IAAI;QAC1B,cAAc,EAAE;OACjB;MACDN,YAAY,CAACG,OAAO,CAAC,kCAAkC,EAAEC,IAAI,CAACC,SAAS,CAACC,sBAAsB,CAAC,CAAC;;IAGlG;IACA;EACF;EAEA;;;EAGA5C,qBAAqB;IACnB;IACA,MAAM6C,WAAW,GAAgB;MAC/BC,IAAI,EAAE,QAAQ;MACdtD,KAAK,EAAE,eAAe;MACtBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,CAAC;QAAEzD,KAAK,EAAE;MAAO,CAAE,EAC5B;QAAEyD,KAAK,EAAE,CAAC;QAAEzD,KAAK,EAAE;MAAO,CAAE,EAC5B;QAAEyD,KAAK,EAAE,EAAE;QAAEzD,KAAK,EAAE;MAAQ,CAAE,EAC9B;QAAEyD,KAAK,EAAE,EAAE;QAAEzD,KAAK,EAAE;MAAQ,CAAE,EAC9B;QAAEyD,KAAK,EAAE,QAAQ;QAAEzD,KAAK,EAAE;MAAgB,CAAE;KAE/C;IAED;IACA,MAAM0D,aAAa,GAAgB;MACjCJ,IAAI,EAAE,UAAU;MAChBtD,KAAK,EAAE,IAAI;MACXuD,IAAI,EAAE,MAAM;MACZI,OAAO,EAAGC,MAAM,IAAKA,MAAM,CAAC3D,MAAM,KAAK,QAAQ;MAC/C4D,QAAQ,EAAGD,MAAM,IAAKA,MAAM,CAAC3D,MAAM,KAAK,QAAQ;MAChD6D,YAAY,EAAE;KACf;IAED,MAAMC,WAAW,GAAgB;MAC/BT,IAAI,EAAE,QAAQ;MACdtD,KAAK,EAAE,IAAI;MACXuD,IAAI,EAAE,MAAM;MACZI,OAAO,EAAGC,MAAM,IAAKA,MAAM,CAAC3D,MAAM,KAAK,QAAQ;MAC/C4D,QAAQ,EAAGD,MAAM,IAAKA,MAAM,CAAC3D,MAAM,KAAK,QAAQ;MAChD6D,YAAY,EAAE;KACf;IAED;IACA,IAAI,CAACE,sBAAsB,GAAG,CAC5B;MACEV,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,IAAI;QAAEzD,KAAK,EAAE;MAAkB,CAAE;KAE7C,EACDqD,WAAW,EACXK,aAAa,EACbK,WAAW,CACZ;IAED,IAAI,CAACE,+BAA+B,GAAG,CACrC;MACEX,IAAI,EAAE,aAAa;MACnBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,EAAE;MACXK,QAAQ,EAAE,IAAI;MACdK,UAAU,EAAE,CAAC/E,cAAc,CAAC,CAAC,CAAC,CAAC;MAC/B2E,YAAY,EAAE;KACf,EACD;MACER,IAAI,EAAE,WAAW;MACjBtD,KAAK,EAAE,OAAO;MACduD,IAAI,EAAE;KACP,EACD;MACED,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,QAAQ;MACfuD,IAAI,EAAE;KACP,EACDF,WAAW,EACXK,aAAa,EACbK,WAAW,CACZ;IAED,IAAI,CAACI,8BAA8B,GAAG,CACpC;MACEb,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,IAAI;QAAEzD,KAAK,EAAE;MAAkB,CAAE,CAC3C;MACD6D,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE;KACf,EACD;MACER,IAAI,EAAE,WAAW;MACjBtD,KAAK,EAAE,OAAO;MACduD,IAAI,EAAE;KACP,EACD;MACED,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,QAAQ;MACfuD,IAAI,EAAE;KACP,EACDF,WAAW,EACXK,aAAa,EACbK,WAAW,CACZ;IAED,IAAI,CAACK,mBAAmB,GAAG,CACzB;MACEd,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,IAAI;QAAEzD,KAAK,EAAE;MAAkB,CAAE;KAE7C,EACD;MACEsD,IAAI,EAAE,oBAAoB;MAC1BtD,KAAK,EAAE,SAAS;MAChBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,SAAS;QAAEzD,KAAK,EAAE;MAAqB,CAAE,EAClD;QAAEyD,KAAK,EAAE,aAAa;QAAEzD,KAAK,EAAE;MAA0B,CAAE,EAC3D;QAAEyD,KAAK,EAAE,KAAK;QAAEzD,KAAK,EAAE;MAAgB,CAAE;KAE5C,EACDqD,WAAW,EACXK,aAAa,EACbK,WAAW,CACZ;IAED,IAAI,CAACM,wBAAwB,GAAG,CAC9B;MACEf,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,CAAC;QAAEzD,KAAK,EAAE;MAAkB,CAAE,CACxC;MACD6D,QAAQ,EAAE;KACX,EACD;MACEP,IAAI,EAAE,WAAW;MACjBtD,KAAK,EAAE,OAAO;MACduD,IAAI,EAAE,MAAM;MACZM,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE;KACf,EACD;MACER,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,QAAQ;MACfuD,IAAI,EAAE,MAAM;MACZM,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE;KACf,EACDT,WAAW,EACXK,aAAa,EACbK,WAAW,CACZ;EACH;EAEA;;;EAGAvC,gBAAgB;IACd;IACA,MAAM6B,WAAW,GAAgB;MAC/BC,IAAI,EAAE,QAAQ;MACdtD,KAAK,EAAE,eAAe;MACtBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,CAAC;QAAEzD,KAAK,EAAE;MAAO,CAAE,EAC5B;QAAEyD,KAAK,EAAE,CAAC;QAAEzD,KAAK,EAAE;MAAO,CAAE,EAC5B;QAAEyD,KAAK,EAAE,EAAE;QAAEzD,KAAK,EAAE;MAAQ,CAAE,EAC9B;QAAEyD,KAAK,EAAE,EAAE;QAAEzD,KAAK,EAAE;MAAQ,CAAE,EAC9B;QAAEyD,KAAK,EAAE,QAAQ;QAAEzD,KAAK,EAAE;MAAgB,CAAE;KAE/C;IAED;IACA,MAAMsE,eAAe,GAAG,CACtB;MAAEb,KAAK,EAAE,IAAI;MAAEzD,KAAK,EAAE;IAAkB,CAAE,EAC1C,GAAG,IAAI,CAACuB,SAAS,CACdgD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACb;MACA,MAAMC,aAAa,GAAGF,CAAC,CAACG,oBAAoB,CAACC,aAAa,CAACH,CAAC,CAACE,oBAAoB,CAAC;MAClF;MACA,OAAOD,aAAa,KAAK,CAAC,GAAGA,aAAa,GAAGF,CAAC,CAAClB,IAAI,CAACsB,aAAa,CAACH,CAAC,CAACnB,IAAI,CAAC;IAC3E,CAAC,CAAC,CACDuB,GAAG,CAACC,QAAQ,KAAK;MAChBrB,KAAK,EAAEqB,QAAQ,CAAC/E,EAAE;MAClBC,KAAK,EAAE,GAAG8E,QAAQ,CAACH,oBAAoB,MAAMG,QAAQ,CAACxB,IAAI;KAC3D,CAAC,CAAC,CACN;IAED;IACA,IAAI,CAACyB,iBAAiB,CAAC,IAAI,CAACf,sBAAsB,EAAE,CAAC,EAAE;MACrDV,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAEc;KACV,CAAC;IAEF;IACA,IAAI,CAACS,iBAAiB,CAAC,IAAI,CAACd,+BAA+B,EAAE,CAAC,EAAE;MAC9DX,IAAI,EAAE,aAAa;MACnBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,IAAI,CAACjC,SAAS,CACpBgD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACb;QACA,MAAMC,aAAa,GAAGF,CAAC,CAACG,oBAAoB,CAACC,aAAa,CAACH,CAAC,CAACE,oBAAoB,CAAC;QAClF;QACA,OAAOD,aAAa,KAAK,CAAC,GAAGA,aAAa,GAAGF,CAAC,CAAClB,IAAI,CAACsB,aAAa,CAACH,CAAC,CAACnB,IAAI,CAAC;MAC3E,CAAC,CAAC,CACDuB,GAAG,CAACC,QAAQ,KAAK;QAChBrB,KAAK,EAAEqB,QAAQ,CAAC/E,EAAE;QAClBC,KAAK,EAAE,GAAG8E,QAAQ,CAACH,oBAAoB,MAAMG,QAAQ,CAACxB,IAAI;OAC3D,CAAC,CAAC;MACLO,QAAQ,EAAE,IAAI;MACdK,UAAU,EAAE,CAAC/E,cAAc,CAAC,CAAC,CAAC,CAAC;MAC/B2E,YAAY,EAAE;KACf,CAAC;IAEF;IACA,IAAI,CAACiB,iBAAiB,CAAC,IAAI,CAACZ,8BAA8B,EAAE,CAAC,EAAE;MAC7Db,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,IAAI;QAAEzD,KAAK,EAAE;MAAkB,CAAE,EAC1C,GAAG,IAAI,CAACuB,SAAS,CACdgD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACb;QACA,MAAMC,aAAa,GAAGF,CAAC,CAACG,oBAAoB,CAACC,aAAa,CAACH,CAAC,CAACE,oBAAoB,CAAC;QAClF;QACA,OAAOD,aAAa,KAAK,CAAC,GAAGA,aAAa,GAAGF,CAAC,CAAClB,IAAI,CAACsB,aAAa,CAACH,CAAC,CAACnB,IAAI,CAAC;MAC3E,CAAC,CAAC,CACDuB,GAAG,CAACC,QAAQ,KAAK;QAChBrB,KAAK,EAAEqB,QAAQ,CAAC/E,EAAE;QAClBC,KAAK,EAAE,GAAG8E,QAAQ,CAACH,oBAAoB,MAAMG,QAAQ,CAACxB,IAAI;OAC3D,CAAC,CAAC,CACN;MACDO,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE;KACf,CAAC;IAEF;IACA,IAAI,CAACiB,iBAAiB,CAAC,IAAI,CAACX,mBAAmB,EAAE,CAAC,EAAE;MAClDd,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAEc;KACV,CAAC;IAEF;IACA,IAAI,CAACS,iBAAiB,CAAC,IAAI,CAACV,wBAAwB,EAAE,CAAC,EAAE;MACvDf,IAAI,EAAE,YAAY;MAClBtD,KAAK,EAAE,UAAU;MACjBuD,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,CAAC;QAAEzD,KAAK,EAAE;MAAkB,CAAE,EACvC,GAAG,IAAI,CAACuB,SAAS,CACdgD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACb;QACA,MAAMC,aAAa,GAAGF,CAAC,CAACG,oBAAoB,CAACC,aAAa,CAACH,CAAC,CAACE,oBAAoB,CAAC;QAClF;QACA,OAAOD,aAAa,KAAK,CAAC,GAAGA,aAAa,GAAGF,CAAC,CAAClB,IAAI,CAACsB,aAAa,CAACH,CAAC,CAACnB,IAAI,CAAC;MAC3E,CAAC,CAAC,CACDuB,GAAG,CAACC,QAAQ,KAAK;QAChBrB,KAAK,EAAEqB,QAAQ,CAAC/E,EAAE;QAClBC,KAAK,EAAE,GAAG8E,QAAQ,CAACH,oBAAoB,MAAMG,QAAQ,CAACxB,IAAI;OAC3D,CAAC,CAAC;KAER,CAAC;IAEF;IACA,IAAI,CAACyB,iBAAiB,CAAC,IAAI,CAACf,sBAAsB,EAAE,CAAC,EAAEX,WAAW,CAAC;IACnE,IAAI,CAAC0B,iBAAiB,CAAC,IAAI,CAACd,+BAA+B,EAAE,CAAC,EAAEZ,WAAW,CAAC;IAC5E,IAAI,CAAC0B,iBAAiB,CAAC,IAAI,CAACZ,8BAA8B,EAAE,CAAC,EAAEd,WAAW,CAAC;IAC3E,IAAI,CAAC0B,iBAAiB,CAAC,IAAI,CAACX,mBAAmB,EAAE,CAAC,EAAEf,WAAW,CAAC,CAAC,CAAC;IAClE,IAAI,CAAC0B,iBAAiB,CAAC,IAAI,CAACV,wBAAwB,EAAE,CAAC,EAAEhB,WAAW,CAAC;EACvE;EAEA;;;EAGQ0B,iBAAiB,CAACC,YAA2B,EAAEC,KAAa,EAAEC,QAAqB;IACzF,IAAIF,YAAY,IAAIA,YAAY,CAACG,MAAM,GAAGF,KAAK,EAAE;MAC/C;MACA,MAAMG,aAAa,GAAGJ,YAAY,CAACC,KAAK,CAAC;MACzCD,YAAY,CAACC,KAAK,CAAC,GAAG;QACpB,GAAGG,aAAa;QAChB,GAAGF;OACJ;MAED5E,OAAO,CAACC,GAAG,CAAC,oBAAoB2E,QAAQ,CAAC5B,IAAI,GAAG,EAAE0B,YAAY,CAACC,KAAK,CAAC,CAAC;;EAE1E;EAEA;;;EAGAI,aAAa;IACX/E,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,sDAAsD,CAAC;MACpE;;IAGF,IAAI,CAAC3D,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACa,aAAa,CAACC,IAAI,CACrB,IAAI,CAAChD,eAAe,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,IAAS,IAAI;QAClBhB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEe,IAAI,CAAC;QAC1D,IAAI,CAACC,SAAS,GAAGD,IAAI;QACrB;QACA,IAAI,CAACE,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAACC,cAAc,CAAC,wBAAwB,CAAC;QAE7C;QACAnB,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;QAE3E,IAAI,CAACoB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDY,KAAK,EAAGA,KAAU,IAAI;QACpBjC,OAAO,CAACiC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACZ,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;EACH;EAEA;;;EAGA4D,cAAc;IACZ;IACA;IACA;EAAA;EAKFC,WAAW;IACT;IACA,IAAI,CAAChD,aAAa,CAACiD,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEA;;;;;EAKAC,mBAAmB,CAACC,KAAuC,EAAEC,KAAa;IACxE;IACA,IAAID,KAAK,YAAYE,iBAAiB,EAAE;MACtC;MACA,MAAMC,aAAa,GAAG/G,KAAK,CAACgH,QAAQ,CAACJ,KAAK,CAAC;MAC3C,IAAI,CAAClG,iBAAiB,CAACuG,cAAc,CAACF,aAAa,IAAI,IAAI,EAAEF,KAAK,CAAC;KACpE,MAAM;MACL,IAAI,CAACnG,iBAAiB,CAACuG,cAAc,CAACL,KAAK,EAAEC,KAAK,CAAC;;EAEvD;EAEA;;;EAGAjD,SAAS,CAACsD,GAAQ;IAChB;IACA,IAAI,CAAC5D,KAAK,GAAG,EAAE;IACf;IACA,IAAI,CAAC3C,iBAAiB,CAACwG,aAAa,EAAE;IAEtC;IACA,MAAMC,KAAK,GAAG,OAAOF,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGvE,MAAM,CAACuE,GAAG,CAAC;IACzD,IAAI,CAACvF,SAAS,GAAGyF,KAAK;IAEtB;IACA,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC;IAEzB;IACA,IAAIA,KAAK,KAAK,eAAe,IAAI,IAAI,CAACE,cAAc,EAAE;MACpD;MACA,MAAMC,SAAS,GAAG,IAAI,CAACC,IAAI,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5G,EAAE,KAAK,eAAe,CAAC;MAC/D,IAAI,CAACyG,SAAS,EAAE;QACd,IAAI,CAACC,IAAI,CAAChE,IAAI,CAAC;UAAE1C,EAAE,EAAE,eAAe;UAAEC,KAAK,EAAE;QAAe,CAAE,CAAC;;;IAInE;IACA,QAAQqG,KAAK;MACX,KAAK,YAAY;QACf;QACA,IAAI,CAAC5E,cAAc,CAAC,wBAAwB,CAAC;QAC7C;QACAmF,qBAAqB,CAAC,MAAK;UACzB;UACAlF,UAAU,CAAC,MAAK;YACdpB,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;YAEhE;YACA,IAAI,IAAI,CAAC4B,yBAAyB,IAAI,IAAI,CAACC,6BAA6B,EAAE;cACxE9B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;cAC5D,IAAI,CAAC8B,gCAAgC,CAAC,IAAI,CAACF,yBAAyB,CAAC;aACtE,MAAM;cACL;cACA,IAAI,CAAC0E,yBAAyB,EAAE;;UAEpC,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC;QACF;MACF,KAAK,sBAAsB;QACzB;QACA,IAAI,CAACpF,cAAc,CAAC,kCAAkC,CAAC;QACvD;QACAmF,qBAAqB,CAAC,MAAK;UACzB;UACAlF,UAAU,CAAC,MAAK;YACdpB,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;YAE1E;YACA,IAAI,IAAI,CAACuG,uBAAuB,IAAI,IAAI,CAACC,2BAA2B,EAAE;cACpEzG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;cAC5D,IAAI,CAACyG,8BAA8B,CAAC,IAAI,CAACF,uBAAuB,CAAC;aAClE,MAAM;cACL;cACA,IAAI,CAACG,uBAAuB,EAAE;;UAElC,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC;QACF;MACF,KAAK,qBAAqB;QACxB;QACA,IAAI,CAACxF,cAAc,CAAC,iCAAiC,CAAC;QACtD;QACAmF,qBAAqB,CAAC,MAAK;UACzB;UACAlF,UAAU,CAAC,MAAK;YACdpB,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YAEzE;YACA,IAAI,IAAI,CAAC2G,sBAAsB,IAAI,IAAI,CAACC,0BAA0B,EAAE;cAClE7G,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;cAC5D,IAAI,CAAC6G,6BAA6B,CAAC,IAAI,CAACF,sBAAsB,CAAC;aAChE,MAAM;cACL;cACA,IAAI,CAACG,sBAAsB,EAAE;;UAEjC,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC;QACF;MACF,KAAK,SAAS;QACZ;QACA,MAAMC,aAAa,GAAG,iCAAiC;QACvD,MAAMC,cAAc,GAAGzE,YAAY,CAACC,OAAO,CAACuE,aAAa,CAAC;QAE1D;QACA,IAAI,CAACC,cAAc,EAAE;UACnBjH,OAAO,CAACC,GAAG,CAAC,6FAA6F,CAAC;UAC1G,IAAI,CAACM,oBAAoB,CAACC,UAAU,GAAGC,SAAS;;QAGlD,IAAI,CAACU,cAAc,CAAC,qBAAqB,CAAC;QAE1C;QACAmF,qBAAqB,CAAC,MAAK;UACzB;UACAlF,UAAU,CAAC,MAAK;YACdpB,OAAO,CAACC,GAAG,CAAC,6DAA6D,EAAE,IAAI,CAACM,oBAAoB,CAACC,UAAU,CAAC;YAChH,IAAI,CAAC0G,iBAAiB,EAAE;UAC1B,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC;QACF;MACF,KAAK,eAAe;QAClB;QACA,IAAI,CAAC/F,cAAc,CAAC,2BAA2B,CAAC;QAChD;QACA,IAAI,CAACgG,wBAAwB,GAAG,IAAI;QAEpC;QACA,IAAI,IAAI,CAAClB,cAAc,EAAE;UACvBjG,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE,IAAI,CAACgG,cAAc,CAAC;UAE5F;UACA,IAAIzF,UAAU,GAAG,IAAI,CAACyF,cAAc,CAACzF,UAAU;UAE/C;UACA,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,EAAE;YACpFA,UAAU,GAAG,CAAC;;UAGhB;UACA,IAAI,CAAC4G,sBAAsB,GAAG;YAC5B,GAAG,IAAI,CAACA,sBAAsB;YAC9B5G,UAAU,EAAEA,UAAU;YACtBX,SAAS,EAAE,IAAI,CAACoG,cAAc,CAACpG,SAAS;YACxCC,UAAU,EAAE,IAAI,CAACmG,cAAc,CAACnG;WACjC;UAED;UACA,IAAI,CAACuH,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAACD,sBAAsB,CAAC;;QAG3E;QACAd,qBAAqB,CAAC,MAAK;UACzB;UACAlF,UAAU,CAAC,MAAK;YACdpB,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;YAEnE;YACA,IAAI,CAACqH,gBAAgB,EAAE;UACzB,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC;QACF;IAAM;IAGV;IACAlG,UAAU,CAAC,MAAK;MACd,IAAI,CAACiB,YAAY,EAAE;IACrB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGAlB,cAAc,CAACoG,UAAkB;IAC/BvH,OAAO,CAACC,GAAG,CAAC,+CAA+CsH,UAAU,EAAE,CAAC;IAExE,IAAI;MACF,MAAMP,aAAa,GAAG,eAAeO,UAAU,EAAE;MACjD,MAAMN,cAAc,GAAGzE,YAAY,CAACC,OAAO,CAACuE,aAAa,CAAC;MAE1D,IAAIC,cAAc,EAAE;QAClB,MAAMO,UAAU,GAAG5E,IAAI,CAAC6E,KAAK,CAACR,cAAc,CAAC;QAC7CjH,OAAO,CAACC,GAAG,CAAC,6BAA6BsH,UAAU,kBAAkB,EAAEC,UAAU,CAAC;QAElF;QACA,QAAQD,UAAU;UAChB,KAAK,wBAAwB;YAC3B;YACA,MAAMG,0BAA0B,GAAG9E,IAAI,CAACC,SAAS,CAAC,IAAI,CAACtC,oBAAoB,CAAC;YAC5E,MAAMoH,sBAAsB,GAAG/E,IAAI,CAACC,SAAS,CAAC;cAAE,GAAG,IAAI,CAACtC,oBAAoB;cAAE,GAAGiH;YAAU,CAAE,CAAC;YAE9F,IAAIE,0BAA0B,KAAKC,sBAAsB,EAAE;cACzD,IAAI,CAACpH,oBAAoB,GAAG;gBAAE,GAAG,IAAI,CAACA,oBAAoB;gBAAE,GAAGiH;cAAU,CAAE;cAC3ExH,OAAO,CAACC,GAAG,CAAC,0BAA0BsH,UAAU,GAAG,EAAE,IAAI,CAAChH,oBAAoB,CAAC;aAChF,MAAM;cACLP,OAAO,CAACC,GAAG,CAAC,aAAasH,UAAU,sCAAsC,CAAC;;YAE5E;UACF,KAAK,kCAAkC;YACrC;YACA,MAAMK,mCAAmC,GAAGhF,IAAI,CAACC,SAAS,CAAC,IAAI,CAACgF,6BAA6B,CAAC;YAC9F,MAAMC,+BAA+B,GAAGlF,IAAI,CAACC,SAAS,CAAC;cAAE,GAAG,IAAI,CAACgF,6BAA6B;cAAE,GAAGL;YAAU,CAAE,CAAC;YAEhH,IAAII,mCAAmC,KAAKE,+BAA+B,EAAE;cAC3E,IAAI,CAACD,6BAA6B,GAAG;gBAAE,GAAG,IAAI,CAACA,6BAA6B;gBAAE,GAAGL;cAAU,CAAE;cAC7FxH,OAAO,CAACC,GAAG,CAAC,0BAA0BsH,UAAU,GAAG,EAAE,IAAI,CAACM,6BAA6B,CAAC;aACzF,MAAM;cACL7H,OAAO,CAACC,GAAG,CAAC,aAAasH,UAAU,sCAAsC,CAAC;;YAE5E;UACF,KAAK,iCAAiC;YACpC;YACA,MAAMQ,kCAAkC,GAAGnF,IAAI,CAACC,SAAS,CAAC,IAAI,CAACmF,4BAA4B,CAAC;YAC5F,MAAMC,8BAA8B,GAAGrF,IAAI,CAACC,SAAS,CAAC;cAAE,GAAG,IAAI,CAACmF,4BAA4B;cAAE,GAAGR;YAAU,CAAE,CAAC;YAE9G,IAAIO,kCAAkC,KAAKE,8BAA8B,EAAE;cACzE,IAAI,CAACD,4BAA4B,GAAG;gBAAE,GAAG,IAAI,CAACA,4BAA4B;gBAAE,GAAGR;cAAU,CAAE;cAC3FxH,OAAO,CAACC,GAAG,CAAC,0BAA0BsH,UAAU,GAAG,EAAE,IAAI,CAACS,4BAA4B,CAAC;aACxF,MAAM;cACLhI,OAAO,CAACC,GAAG,CAAC,aAAasH,UAAU,sCAAsC,CAAC;;YAE5E;UACF,KAAK,qBAAqB;YACxB;YACA;YACA,MAAMW,uBAAuB,GAAGtF,IAAI,CAACC,SAAS,CAAC,IAAI,CAACtC,oBAAoB,CAAC;YACzE,MAAM4H,mBAAmB,GAAGvF,IAAI,CAACC,SAAS,CAAC;cAAE,GAAG,IAAI,CAACtC,oBAAoB;cAAE,GAAGiH;YAAU,CAAE,CAAC;YAE3F,IAAIU,uBAAuB,KAAKC,mBAAmB,EAAE;cACnD,IAAI,CAAC5H,oBAAoB,GAAG;gBAAE,GAAG,IAAI,CAACA,oBAAoB;gBAAE,GAAGiH;cAAU,CAAE;cAE3E;cACA,IAAIA,UAAU,CAAC5G,kBAAkB,EAAE;gBACjC,IAAI,CAACA,kBAAkB,GAAG4G,UAAU,CAAC5G,kBAAkB;;cAGzDZ,OAAO,CAACC,GAAG,CAAC,0BAA0BsH,UAAU,GAAG,EAAE,IAAI,CAAChH,oBAAoB,CAAC;aAChF,MAAM;cACLP,OAAO,CAACC,GAAG,CAAC,aAAasH,UAAU,sCAAsC,CAAC;;YAE5E;UACF,KAAK,2BAA2B;YAC9B;YACA,MAAMa,4BAA4B,GAAGxF,IAAI,CAACC,SAAS,CAAC,IAAI,CAACuE,sBAAsB,CAAC;YAChF,MAAMiB,wBAAwB,GAAGzF,IAAI,CAACC,SAAS,CAAC;cAAE,GAAG,IAAI,CAACuE,sBAAsB;cAAE,GAAGI;YAAU,CAAE,CAAC;YAElG,IAAIY,4BAA4B,KAAKC,wBAAwB,EAAE;cAC7D,IAAI,CAACjB,sBAAsB,GAAG;gBAAE,GAAG,IAAI,CAACA,sBAAsB;gBAAE,GAAGI;cAAU,CAAE;cAC/ExH,OAAO,CAACC,GAAG,CAAC,0BAA0BsH,UAAU,GAAG,EAAE,IAAI,CAACH,sBAAsB,CAAC;aAClF,MAAM;cACLpH,OAAO,CAACC,GAAG,CAAC,aAAasH,UAAU,sCAAsC,CAAC;;YAE5E;QAAM;;KAGb,CAAC,OAAOtF,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,4CAA4CsF,UAAU,iBAAiB,EAAEtF,KAAK,CAAC;;EAEjG;EAEA;;;EAGAsE,yBAAyB;IACvBvG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAE/D;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,kEAAkE,CAAC;MAChF;;IAGF,IAAI,CAAC3D,OAAO,GAAG,IAAI;IACnB,IAAI,CAACY,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,CAAC,IAAI,CAACH,6BAA6B,EAAE;MACvC9B,OAAO,CAACgF,IAAI,CAAC,wGAAwG,CAAC;KACvH,MAAM;MACLhF,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;;IAGvF;IACA,IAAI,CAAC,IAAI,CAACgB,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC4D,MAAM,KAAK,CAAC,EAAE;MAClD7E,OAAO,CAACgF,IAAI,CAAC,iEAAiE,CAAC;MAE/E;MACA,IAAI,CAAC7F,eAAe,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;QACtCC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEe,IAAI,CAAC;UAC1E,IAAI,CAACC,SAAS,GAAGD,IAAI;UAErB;UACA,IAAI,CAACE,gBAAgB,EAAE;UAEvB;UACA,IAAI,CAACqF,yBAAyB,EAAE;QAClC,CAAC;QACDtE,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,0DAA0D,EAAEA,KAAK,CAAC;UAChF,IAAI,CAACA,KAAK,GAAG,+DAA+D;UAC5E,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;MAEF;;IAGF;IACA,IAAI,CAAC,IAAI,CAACd,oBAAoB,EAAE;MAC9BP,OAAO,CAACgF,IAAI,CAAC,oFAAoF,CAAC;MAClG,IAAI,CAACzE,oBAAoB,GAAG;QAC1BC,UAAU,EAAEC,SAAS;QACrBd,MAAM,EAAE,GAAG;QACXe,QAAQ,EAAED,SAAS;QACnBE,MAAM,EAAEF;OACT;;IAGH;IACA;IACA,IAAID,UAAU,GAAG,IAAI,CAACD,oBAAoB,CAACC,UAAU;IACrD,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,EAAE;MACpFA,UAAU,GAAGC,SAAS;;IAExBT,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEO,UAAU,CAAC;IAClE,IAAIE,QAA0B;IAC9B,IAAIC,MAAwB;IAE5B;IACA,IAAI,CAAC,IAAI,CAACJ,oBAAoB,CAACZ,MAAM,EAAE;MACrC,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MACvCd,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK,IAAI,IAAI,CAAC/H,oBAAoB,CAACZ,MAAM,KAAK,QAAQ,EAAE;MACtD,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAGC,MAAM,CAAC,IAAI,CAACnB,oBAAoB,CAACZ,MAAM,CAAC,CAAC;MAC5EgB,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK;MACH;MACA,IAAI,IAAI,CAAC/H,oBAAoB,CAACG,QAAQ,EAAE;QACtCA,QAAQ,GAAG,IAAIa,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACG,QAAQ,CAAC;OACxD,MAAM;QACL;QACAA,QAAQ,GAAG,IAAIa,IAAI,EAAE;QACrBb,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAACe,OAAO,EAAE,GAAG,CAAC,CAAC;;MAG1C,IAAI,IAAI,CAAClB,oBAAoB,CAACI,MAAM,EAAE;QACpCA,MAAM,GAAG,IAAIY,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACI,MAAM,CAAC;OACpD,MAAM;QACL;QACAA,MAAM,GAAG,IAAIY,IAAI,EAAE;;;IAIvBvB,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE;MAAEO,UAAU;MAAEE,QAAQ;MAAEC;IAAM,CAAE,CAAC;IAEtG,IAAI;MACF,MAAMgB,YAAY,GAAG,IAAI,CAACzC,kBAAkB,CAAC0C,wBAAwB,CAACpB,UAAU,EAAEE,QAAQ,EAAEC,MAAM,CAAC,CAACG,SAAS,CAAC;QAC5GC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEe,IAAI,CAAC;UAEtE,IAAI,CAACA,IAAI,EAAE;YACThB,OAAO,CAACiC,KAAK,CAAC,sDAAsD,CAAC;YACrE,IAAI,CAACA,KAAK,GAAG,mDAAmD;YAChE,IAAI,CAACZ,OAAO,GAAG,KAAK;YACpB;;UAGF;UACA,IAAI,CAACL,IAAI,CAACuH,MAAM,IAAI,CAACvH,IAAI,CAACwH,SAAS,EAAE;YACnCxI,OAAO,CAACgF,IAAI,CAAC,2FAA2F,CAAC;;UAG3G;UACA,IAAI,CAACnD,yBAAyB,GAAGb,IAAI;UAErC;UACA,IAAI,CAACe,gCAAgC,CAACf,IAAI,CAAC;UAC3C,IAAI,CAACK,OAAO,GAAG,KAAK;QACtB,CAAC;QACDY,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;UAC7E,IAAI,CAACA,KAAK,GAAG,uDAAuD,IACvDA,KAAK,CAACwG,OAAO,GAAGxG,KAAK,CAACwG,OAAO,GAAG,yBAAyB,CAAC;UACvE,IAAI,CAACpH,OAAO,GAAG,KAAK;UAEpB;UACA,IAAIY,KAAK,CAACyG,MAAM,KAAK,CAAC,EAAE;YACtB1I,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;YACzFmB,UAAU,CAAC,MAAK;cACdpB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;cAC7D,IAAI,CAACsG,yBAAyB,EAAE;YAClC,CAAC,EAAE,IAAI,CAAC;;QAEZ,CAAC;QACDoC,QAAQ,EAAE,MAAK;UACb3I,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QACjE;OACD,CAAC;MAEF,IAAI,CAACiC,aAAa,CAACC,IAAI,CAACR,YAAY,CAAC;KACtC,CAAC,OAAOM,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE,IAAI,CAACA,KAAK,GAAG,8EAA8E;MAC3F,IAAI,CAACZ,OAAO,GAAG,KAAK;;IAGtB;IACA,IAAI,IAAI,CAACf,SAAS,KAAK,YAAY,EAAE;MACnC;MACAc,UAAU,CAAC,MAAK;QACd,IAAI,CAACY,kBAAkB,EAAE;MAC3B,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEA;;;EAGA4G,wBAAwB,CAACC,UAAiC;IACxD7I,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE4I,UAAU,CAAC;IAE1E;IACA,MAAMC,gBAAgB,GAAGlG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACtC,oBAAoB,CAAC;IAClE,MAAMwI,YAAY,GAAGnG,IAAI,CAACC,SAAS,CAACgG,UAAU,CAAC;IAE/C;IACA;IACA,IAAIC,gBAAgB,KAAKC,YAAY,EAAE;MACrC/I,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;;IAG3F;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,iEAAiE,CAAC;MAC/E;;IAGF;IACA,IAAI6D,UAAU,CAAClJ,MAAM,KAAK,QAAQ,EAAE;MAClC,IAAIkJ,UAAU,CAACnI,QAAQ,IAAI,OAAOmI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAClEmI,UAAU,CAACnI,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAErD,IAAImI,UAAU,CAAClI,MAAM,IAAI,OAAOkI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QAC9DkI,UAAU,CAAClI,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAGnD;IAAA,KACK,IAAIkI,UAAU,CAACnI,QAAQ,IAAImI,UAAU,CAAClI,MAAM,EAAE;MACjD,IAAI,OAAOkI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAC3CmI,UAAU,CAACnI,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAErD,IAAI,OAAOmI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QACzCkI,UAAU,CAAClI,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAInD,IAAI,CAACJ,oBAAoB,GAAGsI,UAAU;IACtC7I,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE,IAAI,CAACM,oBAAoB,CAAC;IAE5F;IACAa,UAAU,CAAC,MAAK;MACd,IAAI,CAACmF,yBAAyB,EAAE;IAClC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;;;EAGAI,uBAAuB;IACrB3G,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAE7D;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,gEAAgE,CAAC;MAC9E;;IAGF,IAAI,CAAC3D,OAAO,GAAG,IAAI;IACnB,IAAI,CAACY,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,CAAC,IAAI,CAAChB,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC4D,MAAM,KAAK,CAAC,EAAE;MAClD7E,OAAO,CAACgF,IAAI,CAAC,+DAA+D,CAAC;MAE7E;MACA,IAAI,CAAC7F,eAAe,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;QACtCC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEe,IAAI,CAAC;UACxE,IAAI,CAACC,SAAS,GAAGD,IAAI;UAErB;UACA,IAAI,CAACE,gBAAgB,EAAE;UAEvB;UACA,IAAI,CAACyF,uBAAuB,EAAE;QAChC,CAAC;QACD1E,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,wDAAwD,EAAEA,KAAK,CAAC;UAC9E,IAAI,CAACA,KAAK,GAAG,+DAA+D;UAC5E,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;MAEF;;IAGF;IACA,IAAI,CAAC,IAAI,CAACwG,6BAA6B,CAACjI,WAAW,IAAI,IAAI,CAACiI,6BAA6B,CAACjI,WAAW,CAACiF,MAAM,GAAG,CAAC,EAAE;MAChH;MACA,IAAI,CAACxD,OAAO,GAAG,KAAK;MACpB;;IAGF;IACA,MAAMzB,WAAW,GAAG,IAAI,CAACiI,6BAA6B,CAACjI,WAAW;IAClE,MAAME,UAAU,GAAG,IAAI,CAAC+H,6BAA6B,CAAC/H,UAAU;IAChE,MAAMD,SAAS,GAAG,IAAI,CAACgI,6BAA6B,CAAChI,SAAS;IAC9D,IAAIa,QAA0B;IAC9B,IAAIC,MAAwB;IAE5B;IACA,IAAI,CAAC,IAAI,CAACkH,6BAA6B,CAAClI,MAAM,EAAE;MAC9C,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MACvCd,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK,IAAI,IAAI,CAACT,6BAA6B,CAAClI,MAAM,KAAK,QAAQ,EAAE;MAC/D,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAGC,MAAM,CAAC,IAAI,CAACmG,6BAA6B,CAAClI,MAAM,CAAC,CAAC;MACrFgB,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK;MACH;MACA,IAAI,IAAI,CAACT,6BAA6B,CAACnH,QAAQ,EAAE;QAC/CA,QAAQ,GAAG,IAAIa,IAAI,CAAC,IAAI,CAACsG,6BAA6B,CAACnH,QAAQ,CAAC;OACjE,MAAM;QACL;QACAA,QAAQ,GAAG,IAAIa,IAAI,EAAE;QACrBb,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAACe,OAAO,EAAE,GAAG,CAAC,CAAC;;MAG1C,IAAI,IAAI,CAACoG,6BAA6B,CAAClH,MAAM,EAAE;QAC7CA,MAAM,GAAG,IAAIY,IAAI,CAAC,IAAI,CAACsG,6BAA6B,CAAClH,MAAM,CAAC;OAC7D,MAAM;QACL;QACAA,MAAM,GAAG,IAAIY,IAAI,EAAE;;;IAIvB,IAAI;MACFvB,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;QAAEL,WAAW;QAAEE,UAAU;QAAED,SAAS;QAAEa,QAAQ;QAAEC;MAAM,CAAE,CAAC;MAE5H,MAAMgB,YAAY,GAAG,IAAI,CAACzC,kBAAkB,CAAC8J,sBAAsB,CAACpJ,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEa,QAAQ,EAAEC,MAAM,CAAC,CAACG,SAAS,CAAC;QAClIC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEe,IAAI,CAAC;UAEpE,IAAI,CAACA,IAAI,EAAE;YACThB,OAAO,CAACiC,KAAK,CAAC,oDAAoD,CAAC;YACnE,IAAI,CAACA,KAAK,GAAG,mDAAmD;YAChE,IAAI,CAACZ,OAAO,GAAG,KAAK;YACpB;;UAGF;UACA,IAAI,CAACmF,uBAAuB,GAAGxF,IAAI;UAEnC;UACA,IAAI,CAAC0F,8BAA8B,CAAC1F,IAAI,CAAC;UACzC,IAAI,CAACK,OAAO,GAAG,KAAK;QACtB,CAAC;QACDY,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACA,KAAK,GAAG,8DAA8D,IAC9DA,KAAK,CAACwG,OAAO,GAAGxG,KAAK,CAACwG,OAAO,GAAG,yBAAyB,CAAC;UACvE,IAAI,CAACpH,OAAO,GAAG,KAAK;UAEpB;UACA,IAAIY,KAAK,CAACyG,MAAM,KAAK,CAAC,EAAE;YACtB1I,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;YACvFmB,UAAU,CAAC,MAAK;cACdpB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;cAC3D,IAAI,CAAC0G,uBAAuB,EAAE;YAChC,CAAC,EAAE,IAAI,CAAC;;QAEZ,CAAC;QACDgC,QAAQ,EAAE,MAAK;UACb3I,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC/D;OACD,CAAC;MAEF,IAAI,CAACiC,aAAa,CAACC,IAAI,CAACR,YAAY,CAAC;KACtC,CAAC,OAAOM,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE,IAAI,CAACA,KAAK,GAAG,qFAAqF;MAClG,IAAI,CAACZ,OAAO,GAAG,KAAK;;EAExB;EAEA;;;EAGA4H,iCAAiC,CAACJ,UAAiC;IACjE7I,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE4I,UAAU,CAAC;IAEnF;IACA,MAAMC,gBAAgB,GAAGlG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACgF,6BAA6B,CAAC;IAC3E,MAAMkB,YAAY,GAAGnG,IAAI,CAACC,SAAS,CAACgG,UAAU,CAAC;IAE/C;IACA;IACA,IAAIC,gBAAgB,KAAKC,YAAY,EAAE;MACrC/I,OAAO,CAACC,GAAG,CAAC,qFAAqF,CAAC;;IAGpG;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,0EAA0E,CAAC;MACxF;;IAGF;IACA,IAAI6D,UAAU,CAAClJ,MAAM,KAAK,QAAQ,EAAE;MAClC,IAAIkJ,UAAU,CAACnI,QAAQ,IAAI,OAAOmI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAClEmI,UAAU,CAACnI,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAErD,IAAImI,UAAU,CAAClI,MAAM,IAAI,OAAOkI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QAC9DkI,UAAU,CAAClI,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAGnD;IAAA,KACK,IAAIkI,UAAU,CAACnI,QAAQ,IAAImI,UAAU,CAAClI,MAAM,EAAE;MACjD,IAAI,OAAOkI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAC3CmI,UAAU,CAACnI,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAErD,IAAI,OAAOmI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QACzCkI,UAAU,CAAClI,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAInD,IAAI,CAACkH,6BAA6B,GAAGgB,UAAU;IAC/C7I,OAAO,CAACC,GAAG,CAAC,6DAA6D,EAAE,IAAI,CAAC4H,6BAA6B,CAAC;IAC9G,IAAI,CAAClB,uBAAuB,EAAE;EAChC;EAEA;;;EAGAI,sBAAsB;IACpB/G,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,+DAA+D,CAAC;MAC7E;;IAGF,IAAI,CAAC3D,OAAO,GAAG,IAAI;IACnB,IAAI,CAACY,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,CAAC,IAAI,CAAChB,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC4D,MAAM,KAAK,CAAC,EAAE;MAClD7E,OAAO,CAACgF,IAAI,CAAC,8DAA8D,CAAC;MAE5E;MACA,IAAI,CAAC7F,eAAe,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;QACtCC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEe,IAAI,CAAC;UACvE,IAAI,CAACC,SAAS,GAAGD,IAAI;UAErB;UACA,IAAI,CAACE,gBAAgB,EAAE;UAEvB;UACA,IAAI,CAAC6F,sBAAsB,EAAE;QAC/B,CAAC;QACD9E,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;UAC7E,IAAI,CAACA,KAAK,GAAG,+DAA+D;UAC5E,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;MAEF;;IAGF;IACA,IAAI,CAAC,IAAI,CAAC2G,4BAA4B,CAACxH,UAAU,IAC7C,IAAI,CAACwH,4BAA4B,CAACxH,UAAU,KAAK,IAAI,IACrDc,MAAM,CAAC,IAAI,CAAC0G,4BAA4B,CAACxH,UAAU,CAAC,KAAK,MAAM,EAAE;MACnER,OAAO,CAACgF,IAAI,CAAC,yEAAyE,CAAC;MACvF;MACA,IAAI,CAAC3D,OAAO,GAAG,KAAK;MACpB;;IAGF;IACA,MAAMb,UAAU,GAAG,IAAI,CAACwH,4BAA4B,CAACxH,UAAU;IAC/D,MAAMV,UAAU,GAAG,IAAI,CAACkI,4BAA4B,CAAClI,UAAU;IAC/D,MAAMD,SAAS,GAAG,IAAI,CAACmI,4BAA4B,CAACnI,SAAS;IAC7D,IAAIa,QAA0B;IAC9B,IAAIC,MAAwB;IAE5B;IACA,IAAI,CAAC,IAAI,CAACqH,4BAA4B,CAACrI,MAAM,EAAE;MAC7C,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MACvCd,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK,IAAI,IAAI,CAACN,4BAA4B,CAACrI,MAAM,KAAK,QAAQ,EAAE;MAC9D,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAGC,MAAM,CAAC,IAAI,CAACsG,4BAA4B,CAACrI,MAAM,CAAC,CAAC;MACpFgB,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK;MACH;MACA,IAAI,IAAI,CAACN,4BAA4B,CAACtH,QAAQ,EAAE;QAC9CA,QAAQ,GAAG,IAAIa,IAAI,CAAC,IAAI,CAACyG,4BAA4B,CAACtH,QAAQ,CAAC;OAChE,MAAM;QACL;QACAA,QAAQ,GAAG,IAAIa,IAAI,EAAE;QACrBb,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAACe,OAAO,EAAE,GAAG,CAAC,CAAC;;MAG1C,IAAI,IAAI,CAACuG,4BAA4B,CAACrH,MAAM,EAAE;QAC5CA,MAAM,GAAG,IAAIY,IAAI,CAAC,IAAI,CAACyG,4BAA4B,CAACrH,MAAM,CAAC;OAC5D,MAAM;QACL;QACAA,MAAM,GAAG,IAAIY,IAAI,EAAE;;;IAIvB,IAAI;MACFvB,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;QAAEO,UAAU;QAAEV,UAAU;QAAED,SAAS;QAAEa,QAAQ;QAAEC;MAAM,CAAE,CAAC;MAE1H,MAAMgB,YAAY,GAAG,IAAI,CAACzC,kBAAkB,CAACgK,qBAAqB,CAAC1I,UAAU,EAAEV,UAAU,EAAED,SAAS,EAAEa,QAAQ,EAAEC,MAAM,CAAC,CAACG,SAAS,CAAC;QAChIC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEe,IAAI,CAAC;UAEnE,IAAI,CAACA,IAAI,EAAE;YACThB,OAAO,CAACiC,KAAK,CAAC,mDAAmD,CAAC;YAClE,IAAI,CAACA,KAAK,GAAG,mDAAmD;YAChE,IAAI,CAACZ,OAAO,GAAG,KAAK;YACpB;;UAGF;UACA,IAAI,CAACuF,sBAAsB,GAAG5F,IAAI;UAElC;UACA,IAAI,CAAC8F,6BAA6B,CAAC9F,IAAI,CAAC;UACxC,IAAI,CAACK,OAAO,GAAG,KAAK;QACtB,CAAC;QACDY,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,IAAI,CAACA,KAAK,GAAG,2DAA2D,IAC3DA,KAAK,CAACwG,OAAO,GAAGxG,KAAK,CAACwG,OAAO,GAAG,yBAAyB,CAAC;UACvE,IAAI,CAACpH,OAAO,GAAG,KAAK;UAEpB;UACA,IAAIY,KAAK,CAACyG,MAAM,KAAK,CAAC,EAAE;YACtB1I,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;YACtFmB,UAAU,CAAC,MAAK;cACdpB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;cAC1D,IAAI,CAAC8G,sBAAsB,EAAE;YAC/B,CAAC,EAAE,IAAI,CAAC;;QAEZ,CAAC;QACD4B,QAAQ,EAAE,MAAK;UACb3I,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D;OACD,CAAC;MAEF,IAAI,CAACiC,aAAa,CAACC,IAAI,CAACR,YAAY,CAAC;KACtC,CAAC,OAAOM,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE,IAAI,CAACA,KAAK,GAAG,kFAAkF;MAC/F,IAAI,CAACZ,OAAO,GAAG,KAAK;;EAExB;EAEA;;;EAGA8H,gCAAgC,CAACN,UAAiC;IAChE7I,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAE4I,UAAU,CAAC;IAElF;IACA,MAAMC,gBAAgB,GAAGlG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACmF,4BAA4B,CAAC;IAC1E,MAAMe,YAAY,GAAGnG,IAAI,CAACC,SAAS,CAACgG,UAAU,CAAC;IAE/C;IACA;IACA,IAAIC,gBAAgB,KAAKC,YAAY,EAAE;MACrC/I,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;;IAGnG;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,yEAAyE,CAAC;MACvF;;IAGF;IACA,IAAI6D,UAAU,CAACrI,UAAU,KAAK,IAAI,IAAIqI,UAAU,CAACrI,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACuH,UAAU,CAACrI,UAAU,CAAC,KAAK,MAAM,EAAE;MACrHR,OAAO,CAACgF,IAAI,CAAC,mFAAmF,CAAC;MACjG;MACA,IAAI,CAACgD,4BAA4B,GAAGa,UAAU;MAC9C;;IAGF;IACA,IAAIA,UAAU,CAAClJ,MAAM,KAAK,QAAQ,EAAE;MAClC,IAAIkJ,UAAU,CAACnI,QAAQ,IAAI,OAAOmI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAClEmI,UAAU,CAACnI,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAErD,IAAImI,UAAU,CAAClI,MAAM,IAAI,OAAOkI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QAC9DkI,UAAU,CAAClI,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAGnD;IAAA,KACK,IAAIkI,UAAU,CAACnI,QAAQ,IAAImI,UAAU,CAAClI,MAAM,EAAE;MACjD,IAAI,OAAOkI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAC3CmI,UAAU,CAACnI,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAErD,IAAI,OAAOmI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QACzCkI,UAAU,CAAClI,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAInD,IAAI,CAACqH,4BAA4B,GAAGa,UAAU;IAC9C7I,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE,IAAI,CAAC+H,4BAA4B,CAAC;IAC5G,IAAI,CAACjB,sBAAsB,EAAE;EAC/B;EAEA;;;EAGAqC,qBAAqB;IACnBpJ,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAE3D;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,8DAA8D,CAAC;MAC5E;;IAGF;IACA,IAAI,CAAC3D,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,CAAC,IAAI,CAACJ,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC4D,MAAM,KAAK,CAAC,EAAE;MAClD7E,OAAO,CAACgF,IAAI,CAAC,6DAA6D,CAAC;MAE3E;MACA,IAAI,CAAC7F,eAAe,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;QACtCC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEe,IAAI,CAAC;UACtE,IAAI,CAACC,SAAS,GAAGD,IAAI;UAErB;UACA,IAAI,CAACE,gBAAgB,EAAE;UAEvB;UACA,IAAI,CAACkI,qBAAqB,EAAE;QAC9B,CAAC;QACDnH,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;UAC5E,IAAI,CAACoH,iBAAiB,GAAG,EAAE;UAC3B,IAAI,CAAChI,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;MAEF;;IAGF;IACA;IACA,IAAIb,UAAU,GAAG,IAAI,CAACD,oBAAoB,CAACC,UAAU;IACrD,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,IAAIA,UAAU,KAAK,CAAC,EAAE;MACxGA,UAAU,GAAGC,SAAS;MACtBT,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;IAE5F,IAAIS,QAA0B;IAC9B,IAAIC,MAAwB;IAE5B;IACA,IAAI,CAAC,IAAI,CAACJ,oBAAoB,CAACZ,MAAM,EAAE;MACrC,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MACvCd,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK,IAAI,IAAI,CAAC/H,oBAAoB,CAACZ,MAAM,KAAK,QAAQ,EAAE;MACtD,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAGC,MAAM,CAAC,IAAI,CAACnB,oBAAoB,CAACZ,MAAM,CAAC,CAAC;MAC5EgB,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK;MACH;MACA,IAAI,IAAI,CAAC/H,oBAAoB,CAACG,QAAQ,EAAE;QACtCA,QAAQ,GAAG,IAAIa,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACG,QAAQ,CAAC;OACxD,MAAM;QACL;QACAA,QAAQ,GAAG,IAAIa,IAAI,EAAE;QACrBb,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAACe,OAAO,EAAE,GAAG,CAAC,CAAC;;MAG1C,IAAI,IAAI,CAAClB,oBAAoB,CAACI,MAAM,EAAE;QACpCA,MAAM,GAAG,IAAIY,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACI,MAAM,CAAC;OACpD,MAAM;QACL;QACAA,MAAM,GAAG,IAAIY,IAAI,EAAE;;;IAIvB,IAAI;MACFvB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE;QAAEO,UAAU;QAAEE,QAAQ;QAAEC;MAAM,CAAE,CAAC;MAElG,MAAMgB,YAAY,GAAG,IAAI,CAACzC,kBAAkB,CAACoK,oBAAoB,CAAC9I,UAAU,EAAEE,QAAQ,EAAEC,MAAM,EAAE,EAAE,CAAC,CAACG,SAAS,CAAC;QAC5GC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEe,IAAI,CAAC;UAElE,IAAI,CAACA,IAAI,EAAE;YACThB,OAAO,CAACiC,KAAK,CAAC,kDAAkD,CAAC;YACjE,IAAI,CAACoH,iBAAiB,GAAG,EAAE;YAC3B,IAAI,CAAChI,OAAO,GAAG,KAAK;YACpB;;UAGF,IAAI,CAACgI,iBAAiB,GAAGrI,IAAI;UAE7B;UACA,IAAI,CAACuI,kBAAkB,EAAE;UAEzB,IAAI,CAAClI,OAAO,GAAG,KAAK;QACtB,CAAC;QACDY,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrE,IAAI,CAACoH,iBAAiB,GAAG,EAAE;UAC3B,IAAI,CAAChI,OAAO,GAAG,KAAK;UAEpB;UACA,IAAIY,KAAK,CAACyG,MAAM,KAAK,CAAC,EAAE;YACtB1I,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;YACrFmB,UAAU,CAAC,MAAK;cACdpB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;cACzD,IAAI,CAACmJ,qBAAqB,EAAE;YAC9B,CAAC,EAAE,IAAI,CAAC;;QAEZ,CAAC;QACDT,QAAQ,EAAE,MAAK;UACb3I,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC7D;OACD,CAAC;MAEF,IAAI,CAACiC,aAAa,CAACC,IAAI,CAACR,YAAY,CAAC;KACtC,CAAC,OAAOM,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,IAAI,CAACoH,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAAChI,OAAO,GAAG,KAAK;;EAExB;EAEA;;;EAGAW,kBAAkB;IAChBhC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAExD;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,2DAA2D,CAAC;MACzE;;IAGF;IACA,IAAI,CAAC3D,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,CAAC,IAAI,CAACJ,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC4D,MAAM,KAAK,CAAC,EAAE;MAClD7E,OAAO,CAACgF,IAAI,CAAC,0DAA0D,CAAC;MAExE;MACA,IAAI,CAAC7F,eAAe,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;QACtCC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEe,IAAI,CAAC;UACnE,IAAI,CAACC,SAAS,GAAGD,IAAI;UAErB;UACA,IAAI,CAACE,gBAAgB,EAAE;UAEvB;UACA,IAAI,CAACc,kBAAkB,EAAE;QAC3B,CAAC;QACDC,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;UACzE,IAAI,CAACuH,cAAc,GAAG,EAAE;QAC1B;OACD,CAAC;MAEF;;IAGF;IACA;IACA,IAAIhJ,UAAU,GAAG,IAAI,CAACD,oBAAoB,CAACC,UAAU;IACrD,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,IAAIA,UAAU,KAAK,CAAC,EAAE;MACxGA,UAAU,GAAGC,SAAS;MACtBT,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;;IAEzF,IAAIS,QAA0B;IAC9B,IAAIC,MAAwB;IAE5B;IACA,IAAI,CAAC,IAAI,CAACJ,oBAAoB,CAACZ,MAAM,EAAE;MACrC,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MACvCd,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK,IAAI,IAAI,CAAC/H,oBAAoB,CAACZ,MAAM,KAAK,QAAQ,EAAE;MACtD,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAGC,MAAM,CAAC,IAAI,CAACnB,oBAAoB,CAACZ,MAAM,CAAC,CAAC;MAC5EgB,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK;MACH;MACA,IAAI,IAAI,CAAC/H,oBAAoB,CAACG,QAAQ,EAAE;QACtCA,QAAQ,GAAG,IAAIa,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACG,QAAQ,CAAC;OACxD,MAAM;QACL;QACAA,QAAQ,GAAG,IAAIa,IAAI,EAAE;QACrBb,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAACe,OAAO,EAAE,GAAG,CAAC,CAAC;;MAG1C,IAAI,IAAI,CAAClB,oBAAoB,CAACI,MAAM,EAAE;QACpCA,MAAM,GAAG,IAAIY,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACI,MAAM,CAAC;OACpD,MAAM;QACL;QACAA,MAAM,GAAG,IAAIY,IAAI,EAAE;;;IAIvB,IAAI;MACFvB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAAEO,UAAU;QAAEE,QAAQ;QAAEC;MAAM,CAAE,CAAC;MAE/F,MAAMgB,YAAY,GAAG,IAAI,CAACzC,kBAAkB,CAACuK,iBAAiB,CAACjJ,UAAU,EAAEE,QAAQ,EAAEC,MAAM,EAAE,EAAE,CAAC,CAACG,SAAS,CAAC;QACzGC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEe,IAAI,CAAC;UAE/D,IAAI,CAACA,IAAI,EAAE;YACThB,OAAO,CAACiC,KAAK,CAAC,+CAA+C,CAAC;YAC9D,IAAI,CAACuH,cAAc,GAAG,EAAE;YACxB,IAAI,CAACnI,OAAO,GAAG,KAAK;YACpB;;UAGF,IAAI,CAACmI,cAAc,GAAGxI,IAAI;UAE1B;UACA,IAAI,CAACuI,kBAAkB,EAAE;UAEzB,IAAI,CAAClI,OAAO,GAAG,KAAK;QACtB,CAAC;QACDY,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;UAChE,IAAI,CAACuH,cAAc,GAAG,EAAE;UACxB,IAAI,CAACnI,OAAO,GAAG,KAAK;UAEpB;UACA,IAAIY,KAAK,CAACyG,MAAM,KAAK,CAAC,EAAE;YACtB1I,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;YAClFmB,UAAU,CAAC,MAAK;cACdpB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;cACtD,IAAI,CAAC+B,kBAAkB,EAAE;YAC3B,CAAC,EAAE,IAAI,CAAC;;QAEZ,CAAC;QACD2G,QAAQ,EAAE,MAAK;UACb3I,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAC1D;OACD,CAAC;MAEF,IAAI,CAACiC,aAAa,CAACC,IAAI,CAACR,YAAY,CAAC;KACtC,CAAC,OAAOM,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,IAAI,CAACuH,cAAc,GAAG,EAAE;MACxB,IAAI,CAACnI,OAAO,GAAG,KAAK;;EAExB;EAEA;;;EAGAqI,cAAc;IACZ;IACA;IACA,IAAIlJ,UAAU,GAAG,IAAI,CAACD,oBAAoB,CAACC,UAAU;IACrD,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,IAAIA,UAAU,KAAK,CAAC,EAAE;MACxGA,UAAU,GAAGC,SAAS;MACtBT,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;;IAErF,IAAIS,QAA0B;IAC9B,IAAIC,MAAwB;IAE5B;IACA,IAAI,CAAC,IAAI,CAACJ,oBAAoB,CAACZ,MAAM,EAAE;MACrC,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MACvCd,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK,IAAI,IAAI,CAAC/H,oBAAoB,CAACZ,MAAM,KAAK,QAAQ,EAAE;MACtD,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAGC,MAAM,CAAC,IAAI,CAACnB,oBAAoB,CAACZ,MAAM,CAAC,CAAC;MAC5EgB,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK;MACH;MACA,IAAI,IAAI,CAAC/H,oBAAoB,CAACG,QAAQ,EAAE;QACtCA,QAAQ,GAAG,IAAIa,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACG,QAAQ,CAAC;OACxD,MAAM;QACL;QACAA,QAAQ,GAAG,IAAIa,IAAI,EAAE;QACrBb,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAACe,OAAO,EAAE,GAAG,CAAC,CAAC;;MAG1C,IAAI,IAAI,CAAClB,oBAAoB,CAACI,MAAM,EAAE;QACpCA,MAAM,GAAG,IAAIY,IAAI,CAAC,IAAI,CAAChB,oBAAoB,CAACI,MAAM,CAAC;OACpD,MAAM;QACL;QACAA,MAAM,GAAG,IAAIY,IAAI,EAAE;;;IAIvB,IAAI;MACFvB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;QAAEO,UAAU;QAAEE,QAAQ;QAAEC;MAAM,CAAE,CAAC;MAE3F,MAAMgB,YAAY,GAAG,IAAI,CAACzC,kBAAkB,CAACyK,aAAa,CAACnJ,UAAU,EAAEE,QAAQ,EAAEC,MAAM,CAAC,CAACG,SAAS,CAAC;QACjGC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEe,IAAI,CAAC;UAE3D,IAAI,CAACA,IAAI,EAAE;YACThB,OAAO,CAACiC,KAAK,CAAC,2CAA2C,CAAC;YAC1D,IAAI,CAAC2H,UAAU,GAAG,EAAE;YACpB;;UAGF,IAAI,CAACA,UAAU,GAAG5I,IAAI;UAEtB;UACA,IAAI,CAACuI,kBAAkB,EAAE;QAC3B,CAAC;QACDtH,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAAC2H,UAAU,GAAG,EAAE;UAEpB;UACA,IAAI3H,KAAK,CAACyG,MAAM,KAAK,CAAC,EAAE;YACtB1I,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;YAC9EmB,UAAU,CAAC,MAAK;cACdpB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;cAClD,IAAI,CAACyJ,cAAc,EAAE;YACvB,CAAC,EAAE,IAAI,CAAC;;QAEZ,CAAC;QACDf,QAAQ,EAAE,MAAK;UACb3I,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACtD;OACD,CAAC;MAEF,IAAI,CAACiC,aAAa,CAACC,IAAI,CAACR,YAAY,CAAC;KACtC,CAAC,OAAOM,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAI,CAAC2H,UAAU,GAAG,EAAE;;EAExB;EAEA;;;EAGAtC,gBAAgB;IACdtH,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,yDAAyD,CAAC;MACvE;;IAGF,IAAI,CAAC3D,OAAO,GAAG,IAAI;IACnB,IAAI,CAACY,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,CAAC,IAAI,CAACgE,cAAc,KAAK,CAAC,IAAI,CAACmB,sBAAsB,CAACvH,SAAS,IAAI,CAAC,IAAI,CAACuH,sBAAsB,CAACtH,UAAU,CAAC,EAAE;MAC/GE,OAAO,CAACgF,IAAI,CAAC,0EAA0E,CAAC;MACxF;MACA,IAAI,CAAC3D,OAAO,GAAG,KAAK;MACpB;;IAGF;IACA,IAAI,IAAI,CAAC4E,cAAc,KAAK,CAAC,IAAI,CAACmB,sBAAsB,CAACvH,SAAS,IAAI,CAAC,IAAI,CAACuH,sBAAsB,CAACtH,UAAU,CAAC,EAAE;MAC9GE,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE,IAAI,CAACmH,sBAAsB,CAACvH,SAAS,GAAG,IAAI,CAACoG,cAAc,CAACpG,SAAS;MACrE,IAAI,CAACuH,sBAAsB,CAACtH,UAAU,GAAG,IAAI,CAACmG,cAAc,CAACnG,UAAU;MAEvE;MACA,IAAIU,UAAU,GAAG,IAAI,CAACyF,cAAc,CAACzF,UAAU;MAE/C;MACA,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,EAAE;QACpFA,UAAU,GAAG,CAAC;;MAGhB;MACA,IAAI,CAAC4G,sBAAsB,CAAC5G,UAAU,GAAGA,UAAU;MAEnD;MACA,IAAI,CAAC6G,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAACD,sBAAsB,CAAC;;IAG3E;IACA,IAAI,CAAC,IAAI,CAACnG,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC4D,MAAM,KAAK,CAAC,EAAE;MAClD7E,OAAO,CAACgF,IAAI,CAAC,wDAAwD,CAAC;MAEtE;MACA,IAAI,CAAC7F,eAAe,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;QACtCC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEe,IAAI,CAAC;UACjE,IAAI,CAACC,SAAS,GAAGD,IAAI;UAErB;UACA,IAAI,CAACE,gBAAgB,EAAE;UAEvB;UACA,IAAI,CAACoG,gBAAgB,EAAE;QACzB,CAAC;QACDrF,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE,IAAI,CAACA,KAAK,GAAG,+DAA+D;UAC5E,IAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;MAEF;;IAGF;IACA,IAAI,CAAC,IAAI,CAAC+F,sBAAsB,CAAC5G,UAAU,IAAI,CAAC,IAAI,CAAC4G,sBAAsB,CAACvH,SAAS,IAAI,CAAC,IAAI,CAACuH,sBAAsB,CAACtH,UAAU,EAAE;MAChI;MACA,IAAI,CAACuB,OAAO,GAAG,KAAK;MACpB;;IAGF;IACA;IACA,IAAIb,UAAU,GAAkB,IAAI,CAAC4G,sBAAsB,CAAC5G,UAAU,IAAI,IAAI;IAC9E;IACA,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,EAAE;MACpFA,UAAU,GAAG,CAAC,CAAC,CAAC;MAChBR,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;MAErF;MACA,IAAI,CAACmH,sBAAsB,CAAC5G,UAAU,GAAG,CAAC;;IAG5CR,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEO,UAAU,CAAC;IACzE,MAAMX,SAAS,GAAG,IAAI,CAACuH,sBAAsB,CAACvH,SAAS;IACvD,MAAMC,UAAU,GAAG,IAAI,CAACsH,sBAAsB,CAACtH,UAAU;IACzD,IAAIY,QAA0B;IAC9B,IAAIC,MAAwB;IAE5B;IACA,IAAI,CAAC,IAAI,CAACyG,sBAAsB,CAACzH,MAAM,EAAE;MACvC,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MACvCd,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK,IAAI,IAAI,CAAClB,sBAAsB,CAACzH,MAAM,KAAK,QAAQ,EAAE;MACxD,MAAM2I,KAAK,GAAG,IAAI/G,IAAI,EAAE;MACxBb,QAAQ,GAAG,IAAIa,IAAI,EAAE;MACrBb,QAAQ,CAACc,OAAO,CAAC8G,KAAK,CAAC7G,OAAO,EAAE,GAAGC,MAAM,CAAC,IAAI,CAAC0F,sBAAsB,CAACzH,MAAM,CAAC,CAAC;MAC9EgB,MAAM,GAAG2H,KAAK;;IAEhB;IAAA,KACK;MACH;MACA,IAAI,IAAI,CAAClB,sBAAsB,CAAC1G,QAAQ,EAAE;QACxCA,QAAQ,GAAG,IAAIa,IAAI,CAAC,IAAI,CAAC6F,sBAAsB,CAAC1G,QAAQ,CAAC;OAC1D,MAAM;QACL;QACAA,QAAQ,GAAG,IAAIa,IAAI,EAAE;QACrBb,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAACe,OAAO,EAAE,GAAG,CAAC,CAAC;;MAG1C,IAAI,IAAI,CAAC2F,sBAAsB,CAACzG,MAAM,EAAE;QACtCA,MAAM,GAAG,IAAIY,IAAI,CAAC,IAAI,CAAC6F,sBAAsB,CAACzG,MAAM,CAAC;OACtD,MAAM;QACL;QACAA,MAAM,GAAG,IAAIY,IAAI,EAAE;;;IAIvB,IAAI;MACFvB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;QAAEO,UAAU;QAAEX,SAAS;QAAEC,UAAU;QAAEY,QAAQ;QAAEC;MAAM,CAAE,CAAC;MAEpH;MACA,IAAI,CAACd,SAAS,IAAI,CAACC,UAAU,EAAE;QAC7BE,OAAO,CAACiC,KAAK,CAAC,wEAAwE,CAAC;QACvF,IAAI,CAACA,KAAK,GAAG,mEAAmE;QAChF,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpB;;MAGF;MACA,MAAMwI,eAAe,GAAGrJ,UAAU,KAAK,CAAC,GAAG,IAAI,GAAGA,UAAU;MAC5D,MAAMmB,YAAY,GAAG,IAAI,CAACzC,kBAAkB,CAAC4K,eAAe,CAACD,eAAe,EAAEhK,SAAS,EAAEC,UAAU,EAAEY,QAAQ,EAAEC,MAAM,CAAC,CAACG,SAAS,CAAC;QAC/HC,IAAI,EAAGC,IAAS,IAAI;UAClBhB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEe,IAAI,CAAC;UAE7D,IAAI,CAACA,IAAI,EAAE;YACThB,OAAO,CAACiC,KAAK,CAAC,6CAA6C,CAAC;YAC5D,IAAI,CAACA,KAAK,GAAG,mDAAmD;YAChE,IAAI,CAACZ,OAAO,GAAG,KAAK;YACpB;;UAGF;UACA;UACA,IAAIL,IAAI,CAACnB,SAAS,IAAImB,IAAI,CAAClB,UAAU,EAAE;YACrCE,OAAO,CAACC,GAAG,CAAC,oEAAoE,EAAE;cAChFJ,SAAS,EAAEmB,IAAI,CAACnB,SAAS;cACzBC,UAAU,EAAEkB,IAAI,CAAClB;aAClB,CAAC;YAEF,IAAI,CAACsH,sBAAsB,CAACvH,SAAS,GAAGmB,IAAI,CAACnB,SAAS;YACtD,IAAI,CAACuH,sBAAsB,CAACtH,UAAU,GAAGkB,IAAI,CAAClB,UAAU;YAExD;YACA,IAAI,CAACuH,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAACD,sBAAsB,CAAC;;UAG3E,IAAI,CAAC2C,gBAAgB,GAAG/I,IAAI;UAC5B,IAAI,CAACgJ,qBAAqB,GAAGhJ,IAAI;UAEjC;UACA,MAAMiJ,OAAO,GAAGjJ,IAAI,IAAIA,IAAI,CAACuH,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACuH,MAAM,CAAC,IAAIvH,IAAI,CAACuH,MAAM,CAAC1D,MAAM,GAAG,CAAC;UAC3F,IAAI,CAACsC,wBAAwB,GAAG8C,OAAO;UAEvC;UACA,IAAI,CAACG,uBAAuB,CAACpJ,IAAI,CAAC;UAClC,IAAI,CAACK,OAAO,GAAG,KAAK;QACtB,CAAC;QACDY,KAAK,EAAGA,KAAU,IAAI;UACpBjC,OAAO,CAACiC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,IAAI,CAACA,KAAK,GAAG,sCAAsC,IACtCA,KAAK,CAACwG,OAAO,GAAGxG,KAAK,CAACwG,OAAO,GAAG,yBAAyB,CAAC;UACvE,IAAI,CAACpH,OAAO,GAAG,KAAK;UAEpB;UACA,IAAIY,KAAK,CAACyG,MAAM,KAAK,CAAC,EAAE;YACtB1I,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;YAChFmB,UAAU,CAAC,MAAK;cACdpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;cACpD,IAAI,CAACqH,gBAAgB,EAAE;YACzB,CAAC,EAAE,IAAI,CAAC;;QAEZ,CAAC;QACDqB,QAAQ,EAAE,MAAK;UACb3I,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACxD;OACD,CAAC;MAEF,IAAI,CAACiC,aAAa,CAACC,IAAI,CAACR,YAAY,CAAC;KACtC,CAAC,OAAOM,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,IAAI,CAACA,KAAK,GAAG,6DAA6D;MAC1E,IAAI,CAACZ,OAAO,GAAG,KAAK;;EAExB;EAEA;;;EAGA6F,iBAAiB;IACflH,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE,IAAI,CAACW,kBAAkB,CAAC;IAEpF,QAAQ,IAAI,CAACA,kBAAkB;MAC7B,KAAK,SAAS;QACZ,IAAI,CAACoB,kBAAkB,EAAE;QACzB;MACF,KAAK,aAAa;QAChB,IAAI,CAACoH,qBAAqB,EAAE;QAC5B;MACF,KAAK,KAAK;QACR,IAAI,CAACM,cAAc,EAAE;QACrB;MACF;QACE,IAAI,CAAC1H,kBAAkB,EAAE;QACzB;IAAM;EAEZ;EAIA;;;EAGAqI,qBAAqB,CAACxB,UAAiC;IACrD7I,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE4I,UAAU,CAAC;IAEvE;IACA,MAAMC,gBAAgB,GAAGlG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACtC,oBAAoB,CAAC;IAClE,MAAMwI,YAAY,GAAGnG,IAAI,CAACC,SAAS,CAACgG,UAAU,CAAC;IAE/C;IACA;IACA,IAAIC,gBAAgB,KAAKC,YAAY,EAAE;MACrC/I,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;;IAGxF;IACA,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,8DAA8D,CAAC;MAC5E;;IAGF;IACA,IAAI6D,UAAU,CAAClJ,MAAM,KAAK,QAAQ,EAAE;MAClC,IAAIkJ,UAAU,CAACnI,QAAQ,IAAI,OAAOmI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAClEmI,UAAU,CAACnI,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAErD,IAAImI,UAAU,CAAClI,MAAM,IAAI,OAAOkI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QAC9DkI,UAAU,CAAClI,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAGnD;IAAA,KACK,IAAIkI,UAAU,CAACnI,QAAQ,IAAImI,UAAU,CAAClI,MAAM,EAAE;MACjD,IAAI,OAAOkI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAC3CmI,UAAU,CAACnI,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAErD,IAAI,OAAOmI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QACzCkI,UAAU,CAAClI,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAInD;IACA,IAAIkI,UAAU,CAACjI,kBAAkB,EAAE;MACjC,IAAI,CAACA,kBAAkB,GAAGiI,UAAU,CAACjI,kBAAkB;KACxD,MAAM;MACL;MACA,IAAI,CAACA,kBAAkB,GAAG,SAAS;MACnCiI,UAAU,CAACjI,kBAAkB,GAAG,SAAS;;IAG3C,IAAI,CAACL,oBAAoB,GAAGsI,UAAU;IACtC7I,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE,IAAI,CAACM,oBAAoB,CAAC;IACzF,IAAI,CAAC2G,iBAAiB,EAAE;IAExB;IACA9F,UAAU,CAAC,MAAK;MACd,IAAI,CAACiB,YAAY,EAAE;IACrB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGAiI,0BAA0B,CAACzB,UAAiC;IAC1D7I,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE4I,UAAU,CAAC;IAE5E;IACA,IAAI,IAAI,CAACxH,OAAO,EAAE;MAChBrB,OAAO,CAACgF,IAAI,CAAC,mEAAmE,CAAC;MACjF;;IAGF;IACA,IAAI,CAACoC,sBAAsB,GAAG;MAAE,GAAGyB;IAAU,CAAE;IAE/C;IACA,IAAIA,UAAU,CAACrI,UAAU,KAAK,IAAI,IAAIqI,UAAU,CAACrI,UAAU,KAAKC,SAAS,IAAIa,MAAM,CAACuH,UAAU,CAACrI,UAAU,CAAC,KAAK,MAAM,EAAE;MACrHR,OAAO,CAACC,GAAG,CAAC,gGAAgG,CAAC;MAC7G,IAAI,CAACmH,sBAAsB,CAAC5G,UAAU,GAAG,CAAC;KAC3C,MAAM;MACLR,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAE4I,UAAU,CAACrI,UAAU,CAAC;;IAGrG;IACA,IAAI,IAAI,CAACyF,cAAc,EAAE;MACvB,IAAI,IAAI,CAACA,cAAc,CAACpG,SAAS,KAAK,IAAI,CAACuH,sBAAsB,CAACvH,SAAS,IACvE,IAAI,CAACoG,cAAc,CAACnG,UAAU,KAAK,IAAI,CAACsH,sBAAsB,CAACtH,UAAU,IACzE,IAAI,CAACmG,cAAc,CAACzF,UAAU,KAAK,IAAI,CAAC4G,sBAAsB,CAAC5G,UAAU,EAAE;QAC7ER,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;QAC7F,IAAI,CAACgG,cAAc,GAAG,IAAI;;;IAI9B;IACA,IAAI4C,UAAU,CAAClJ,MAAM,KAAK,QAAQ,EAAE;MAClC,IAAIkJ,UAAU,CAACnI,QAAQ,IAAI,OAAOmI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAClE,IAAI,CAAC0G,sBAAsB,CAAC1G,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAEtE,IAAImI,UAAU,CAAClI,MAAM,IAAI,OAAOkI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QAC9D,IAAI,CAACyG,sBAAsB,CAACzG,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAGpE;IAAA,KACK,IAAIkI,UAAU,CAACnI,QAAQ,IAAImI,UAAU,CAAClI,MAAM,EAAE;MACjD,IAAI,OAAOkI,UAAU,CAACnI,QAAQ,KAAK,QAAQ,EAAE;QAC3C,IAAI,CAAC0G,sBAAsB,CAAC1G,QAAQ,GAAG,IAAIa,IAAI,CAACsH,UAAU,CAACnI,QAAQ,CAAC;;MAEtE,IAAI,OAAOmI,UAAU,CAAClI,MAAM,KAAK,QAAQ,EAAE;QACzC,IAAI,CAACyG,sBAAsB,CAACzG,MAAM,GAAG,IAAIY,IAAI,CAACsH,UAAU,CAAClI,MAAM,CAAC;;;IAIpEX,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE,IAAI,CAACmH,sBAAsB,CAAC;IAEhG;IACA,IAAI,CAAC,IAAI,CAACA,sBAAsB,CAACvH,SAAS,IAAI,CAAC,IAAI,CAACuH,sBAAsB,CAACtH,UAAU,EAAE;MACrFE,OAAO,CAACgF,IAAI,CAAC,6GAA6G,CAAC;MAC3H;;IAGF;IACA,MAAM8D,gBAAgB,GAAGlG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACuE,sBAAsB,CAAC;IACpE,MAAM2B,YAAY,GAAGnG,IAAI,CAACC,SAAS,CAACgG,UAAU,CAAC;IAE/C;IACA;IACA,IAAIC,gBAAgB,KAAKC,YAAY,EAAE;MACrC/I,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;KAC5F,MAAM;MACL;MACA,IAAI,CAACoH,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAACD,sBAAsB,CAAC;;IAG3E;IACA,IAAI,CAACD,wBAAwB,GAAG,IAAI;IAEpC;IACA,IAAI,CAACG,gBAAgB,EAAE;EACzB;EAEA;;;EAGAiD,gBAAgB,CAACC,MAAW;IAC1BxK,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEuK,MAAM,CAAC;IAClE,IAAI,CAACvE,cAAc,GAAGuE,MAAM;IAE5B;IACA,IAAIhK,UAAU,GAAGgK,MAAM,CAAChK,UAAU;IAElC;IACA,IAAI,IAAI,CAACF,SAAS,KAAK,YAAY,IAAI,IAAI,CAACA,SAAS,KAAK,SAAS,EAAE;MACnE,IAAI,IAAI,CAACC,oBAAoB,IAAI,IAAI,CAACA,oBAAoB,CAACC,UAAU,KAAKC,SAAS,EAAE;QACnFD,UAAU,GAAG,IAAI,CAACD,oBAAoB,CAACC,UAAU;QACjD;QACA,IAAIA,UAAU,KAAK,IAAI,IAAIc,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,EAAE;UACxDA,UAAU,GAAG,CAAC;;;;IAIpB;IAAA,KACK,IAAI,IAAI,CAACF,SAAS,KAAK,qBAAqB,EAAE;MACjD,IAAI,IAAI,CAAC0H,4BAA4B,IAAI,IAAI,CAACA,4BAA4B,CAACxH,UAAU,KAAKC,SAAS,EAAE;QACnGD,UAAU,GAAG,IAAI,CAACwH,4BAA4B,CAACxH,UAAU;QACzD;QACA,IAAIA,UAAU,KAAK,IAAI,IAAIc,MAAM,CAACd,UAAU,CAAC,KAAK,MAAM,EAAE;UACxDA,UAAU,GAAGC,SAAS;;;;IAI5B;IACA;IAEA;IACA;IACA;IACA,IAAID,UAAU,KAAKC,SAAS,EAAE;MAC5BD,UAAU,GAAGgK,MAAM,CAAChK,UAAU;;IAGhC;IACA,IAAIA,UAAU,KAAK,IAAI,EAAE;MACvBA,UAAU,GAAG,CAAC;;IAGhBR,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DO,UAAU,EAAEA,UAAU;MACtBX,SAAS,EAAE2K,MAAM,CAAC3K,SAAS;MAC3BC,UAAU,EAAE0K,MAAM,CAAC1K;KACpB,CAAC;IAEF;IACA,IAAI,CAACsH,sBAAsB,GAAG;MAC5B,GAAG,IAAI,CAACA,sBAAsB;MAC9B5G,UAAU,EAAEA,UAAU;MACtBX,SAAS,EAAE2K,MAAM,CAAC3K,SAAS;MAC3BC,UAAU,EAAE0K,MAAM,CAAC1K;KACpB;IAED;IACA,IAAI,CAACuH,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAACD,sBAAsB,CAAC;IAEzE;IACA,IAAI,CAACD,wBAAwB,GAAG,IAAI;IAEpC;IACA,IAAI,CAAC7G,SAAS,GAAG,eAAe;IAEhC;IACA,IAAI,CAAC0F,aAAa,CAAC,eAAe,CAAC;IAEnC;IACA,IAAI,CAACsB,gBAAgB,EAAE;EACzB;EAEA;;;EAGAmD,qBAAqB,CAACC,SAAiB,EAAEC,QAAe;IACtD,IAAI;MACF,MAAMC,UAAU,GAA4B,EAAE;MAE9C;MACAD,QAAQ,CAACxF,OAAO,CAAC0F,OAAO,IAAG;QACzB,IAAIA,OAAO,CAACnL,KAAK,EAAE;UACjB;UACA,IAAI,IAAI,CAACoL,0BAA0B,IAAIJ,SAAS,KAAK,YAAY,EAAE;YACjE,MAAM/F,KAAK,GAAGgG,QAAQ,CAACI,OAAO,CAACF,OAAO,CAAC;YACvCD,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC,GAAG,IAAI,CAACoL,0BAA0B,CAACE,gBAAgB,CAACrG,KAAK,CAAC;WACpF,MAAM,IAAI,IAAI,CAACsG,wBAAwB,IAAIP,SAAS,KAAK,sBAAsB,EAAE;YAChF,MAAM/F,KAAK,GAAGgG,QAAQ,CAACI,OAAO,CAACF,OAAO,CAAC;YACvCD,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC,GAAG,IAAI,CAACuL,wBAAwB,CAACD,gBAAgB,CAACrG,KAAK,CAAC;WAClF,MAAM,IAAI,IAAI,CAACuG,uBAAuB,IAAIR,SAAS,KAAK,qBAAqB,EAAE;YAC9E,MAAM/F,KAAK,GAAGgG,QAAQ,CAACI,OAAO,CAACF,OAAO,CAAC;YACvCD,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC,GAAG,IAAI,CAACwL,uBAAuB,CAACF,gBAAgB,CAACrG,KAAK,CAAC;WACjF,MAAM,IAAI,IAAI,CAACwG,iBAAiB,IAAIT,SAAS,KAAK,eAAe,EAAE;YAClE,MAAM/F,KAAK,GAAGgG,QAAQ,CAACI,OAAO,CAACF,OAAO,CAAC;YACvCD,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC,GAAG,IAAI,CAACyL,iBAAiB,CAACH,gBAAgB,CAACrG,KAAK,CAAC;WAC3E,MAAM;YACL;YACAiG,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC,GAAG,CAACmL,OAAO,CAACO,MAAM;;;MAGjD,CAAC,CAAC;MAEF;MACA5I,YAAY,CAACG,OAAO,CAAC,sBAAsB+H,SAAS,EAAE,EAAE9H,IAAI,CAACC,SAAS,CAAC+H,UAAU,CAAC,CAAC;MACnF5K,OAAO,CAACC,GAAG,CAAC,qCAAqCyK,SAAS,WAAW,EAAEE,UAAU,CAAC;KACnF,CAAC,OAAO3I,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,wDAAwDyI,SAAS,GAAG,EAAEzI,KAAK,CAAC;;EAE9F;EAEA;;;EAGA+D,aAAa,CAACD,KAAa;IACzB,IAAI;MACFvD,YAAY,CAACG,OAAO,CAAC,wBAAwB,EAAEoD,KAAK,CAAC;MACrD/F,OAAO,CAACC,GAAG,CAAC,4BAA4B8F,KAAK,EAAE,CAAC;KACjD,CAAC,OAAO9D,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;;EAE/D;EAEA;;;EAGA5B,aAAa;IACX,IAAI;MACF,MAAMC,SAAS,GAAGkC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;MAChEzC,OAAO,CAACC,GAAG,CAAC,2CAA2CK,SAAS,EAAE,CAAC;MACnE,OAAOA,SAAS;KACjB,CAAC,OAAO2B,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,IAAI;;EAEf;EAEA;;;;;EAKAoF,UAAU,CAACE,UAAkB,EAAEsB,UAAe;IAC5C,IAAI;MACF,MAAM7B,aAAa,GAAG,eAAeO,UAAU,EAAE;MACjD/E,YAAY,CAACG,OAAO,CAACqE,aAAa,EAAEpE,IAAI,CAACC,SAAS,CAACgG,UAAU,CAAC,CAAC;MAC/D7I,OAAO,CAACC,GAAG,CAAC,oBAAoBsH,UAAU,mBAAmB,EAAEsB,UAAU,CAAC;KAC3E,CAAC,OAAO5G,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,iCAAiCsF,UAAU,mBAAmB,EAAEtF,KAAK,CAAC;;EAExF;EAEA;;;;EAIAoJ,MAAM,CAACC,MAAc;IACnBtL,OAAO,CAACC,GAAG,CAAC,kCAAkCqL,MAAM,EAAE,CAAC;IAEvD,MAAMC,eAAe,GAAG,IAAI,CAAChM,mBAAmB,CAACiM,iCAAiC,CAChF,IAAI,CAACC,UAAU,EACf,IAAI,CAACC,aAAa,EAClBJ,MAAM,EACN,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;IAAA,CAC9D;IAED;IACA,IAAI,CAACG,UAAU,GAAGF,eAAe,CAACD,MAAM;IACxC,IAAI,CAACI,aAAa,GAAGH,eAAe,CAACI,SAAS;IAE9C;IACA,MAAMC,SAAS,GAAG,IAAI,CAACtL,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;IACtE,IAAI,CAACf,mBAAmB,CAACsM,gBAAgB,CAAC,aAAa,EAAED,SAAS,EAAEL,eAAe,CAAC;IAEpFvL,OAAO,CAACC,GAAG,CAAC,yBAAyB,IAAI,CAACwL,UAAU,IAAI,IAAI,CAACC,aAAa,EAAE,CAAC;IAE7E;IACA,IAAI,CAACnC,kBAAkB,EAAE;EAC3B;EAEA;;;EAGAA,kBAAkB;IAChBvJ,OAAO,CAACC,GAAG,CAAC,yCAAyC,IAAI,CAACwL,UAAU,IAAI,IAAI,CAACC,aAAa,EAAE,CAAC;IAE7F;IACA,IAAI,IAAI,CAAClC,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC3E,MAAM,GAAG,CAAC,EAAE;MACzD,IAAI,CAAC2E,cAAc,GAAG,IAAI,CAACsC,WAAW,CAAC,CAAC,GAAG,IAAI,CAACtC,cAAc,CAAC,CAAC;;IAGlE;IACA,IAAI,IAAI,CAACH,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACxE,MAAM,GAAG,CAAC,EAAE;MAC/D,IAAI,CAACwE,iBAAiB,GAAG,IAAI,CAACyC,WAAW,CAAC,CAAC,GAAG,IAAI,CAACzC,iBAAiB,CAAC,CAAC;;IAGxE;IACA,IAAI,IAAI,CAACO,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC/E,MAAM,GAAG,CAAC,EAAE;MACjD,IAAI,CAAC+E,UAAU,GAAG,IAAI,CAACkC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAClC,UAAU,CAAC,CAAC;;EAE5D;EAEA;;;;;EAKAkC,WAAW,CAACC,OAAc;IACxB,OAAOA,OAAO,CAAC9H,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,IAAI6H,MAAW;MACf,IAAIC,MAAW;MAEf;MACA,QAAQ,IAAI,CAACR,UAAU;QACrB,KAAK,cAAc;UACjBO,MAAM,GAAG9H,CAAC,CAACgI,YAAY,EAAEC,WAAW,EAAE,IAAI,EAAE;UAC5CF,MAAM,GAAG9H,CAAC,CAAC+H,YAAY,EAAEC,WAAW,EAAE,IAAI,EAAE;UAC5C;QACF,KAAK,WAAW;UACdH,MAAM,GAAG9H,CAAC,CAACrE,SAAS,EAAEsM,WAAW,EAAE,IAAI,EAAE;UACzCF,MAAM,GAAG9H,CAAC,CAACtE,SAAS,EAAEsM,WAAW,EAAE,IAAI,EAAE;UACzC;QACF,KAAK,YAAY;UACfH,MAAM,GAAG9H,CAAC,CAACpE,UAAU,EAAEqM,WAAW,EAAE,IAAI,EAAE;UAC1CF,MAAM,GAAG9H,CAAC,CAACrE,UAAU,EAAEqM,WAAW,EAAE,IAAI,EAAE;UAC1C;QACF,KAAK,YAAY;UACfH,MAAM,GAAG9H,CAAC,CAACkI,UAAU,IAAI,CAAC;UAC1BH,MAAM,GAAG9H,CAAC,CAACiI,UAAU,IAAI,CAAC;UAC1B;QACF,KAAK,KAAK;UACRJ,MAAM,GAAG9H,CAAC,CAACmI,GAAG,IAAI,CAAC;UACnBJ,MAAM,GAAG9H,CAAC,CAACkI,GAAG,IAAI,CAAC;UACnB;QACF,KAAK,KAAK;UACRL,MAAM,GAAG9H,CAAC,CAACoI,GAAG,IAAI,CAAC;UACnBL,MAAM,GAAG9H,CAAC,CAACmI,GAAG,IAAI,CAAC;UACnB;QACF,KAAK,KAAK;UACRN,MAAM,GAAG9H,CAAC,CAACqI,GAAG,IAAI,CAAC;UACnBN,MAAM,GAAG9H,CAAC,CAACoI,GAAG,IAAI,CAAC;UACnB;QACF,KAAK,QAAQ;UACXP,MAAM,GAAG9H,CAAC,CAACsI,MAAM,IAAI,CAAC;UACtBP,MAAM,GAAG9H,CAAC,CAACqI,MAAM,IAAI,CAAC;UACtB;QACF,KAAK,aAAa;UAChBR,MAAM,GAAG9H,CAAC,CAACuI,WAAW,IAAI,CAAC;UAC3BR,MAAM,GAAG9H,CAAC,CAACsI,WAAW,IAAI,CAAC;UAC3B;QACF;UACET,MAAM,GAAG,CAAC;UACVC,MAAM,GAAG,CAAC;MAAC;MAGf;MACA,IAAI,IAAI,CAACP,aAAa,KAAK,KAAK,EAAE;QAChC,IAAI,OAAOM,MAAM,KAAK,QAAQ,EAAE;UAC9B,OAAOA,MAAM,CAAC1H,aAAa,CAAC2H,MAAM,CAAC;SACpC,MAAM;UACL,OAAOD,MAAM,GAAGC,MAAM;;OAEzB,MAAM;QACL,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;UAC9B,OAAOC,MAAM,CAAC3H,aAAa,CAAC0H,MAAM,CAAC;SACpC,MAAM;UACL,OAAOC,MAAM,GAAGD,MAAM;;;IAG5B,CAAC,CAAC;EACJ;EAEA;;;EAGQ7L,gBAAgB;IACtB,MAAMuM,WAAW,GAAG;MAAEpB,MAAM,EAAE,KAAK;MAAEK,SAAS,EAAE;IAAwB,CAAE;IAC1E,MAAMgB,iBAAiB,GAAG,IAAI,CAACpN,mBAAmB,CAACY,gBAAgB,CAAC,aAAa,EAAE,SAAS,EAAEuM,WAAW,CAAC;IAE1G,IAAIC,iBAAiB,EAAE;MACrB,IAAI,CAAClB,UAAU,GAAGkB,iBAAiB,CAACrB,MAAM;MAC1C,IAAI,CAACI,aAAa,GAAGiB,iBAAiB,CAAChB,SAAS;;EAEpD;EAEA;;;EAGAiB,qBAAqB,CAAClC,SAAiB;IACrC,IAAI;MACF,MAAMmC,cAAc,GAAGrK,YAAY,CAACC,OAAO,CAAC,sBAAsBiI,SAAS,EAAE,CAAC;MAE9E,IAAImC,cAAc,EAAE;QAClB,MAAMjC,UAAU,GAAGhI,IAAI,CAAC6E,KAAK,CAACoF,cAAc,CAAC;QAC7C7M,OAAO,CAACC,GAAG,CAAC,6CAA6CyK,SAAS,GAAG,EAAEE,UAAU,CAAC;QAClF,OAAOA,UAAU;;KAEpB,CAAC,OAAO3I,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,wDAAwDyI,SAAS,GAAG,EAAEzI,KAAK,CAAC;;IAG5F,OAAO,IAAI;EACb;EAEA;;;EAGAF,gCAAgC,CAACf,IAAS;IACxChB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEe,IAAI,CAAC;IAE7D;IACA,IAAI,CAAC8L,wBAAwB,GAAG,IAAI;IAEpC;IACA,IAAI,CAACjL,yBAAyB,GAAGb,IAAI;IAErC;IACA,MAAM+L,SAAS,GAAG,IAAI,CAACC,qCAAqC,CAAChM,IAAI,CAAC;IAClEhB,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE8M,SAAS,CAAC;IAEvE;IACA,MAAM9C,OAAO,GAAG8C,SAAS,CAACxE,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAAC4C,SAAS,CAACxE,MAAM,CAAC,IAAIwE,SAAS,CAACxE,MAAM,CAAC1D,MAAM,GAAG,CAAC;IAClG7E,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEgK,OAAO,EAAE,SAAS,EAAE8C,SAAS,CAACxE,MAAM,CAAC;IAEhG,IAAI,CAAC0B,OAAO,EAAE;MACZ;MACA,IAAI,CAACgD,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACH,wBAAwB,GAAG,KAAK;MAErC;MACA,IAAI,IAAI,CAAChC,0BAA0B,EAAE;QACnC,IAAI,CAACA,0BAA0B,CAACoC,OAAO,EAAE;QACzC,IAAI,CAACpC,0BAA0B,GAAG,IAAI;;MAGxC;;IAGF;IACA,IAAI,CAACmC,sBAAsB,GAAG,IAAI;IAElC;IACA,IAAI,CAAC,IAAI,CAACnL,6BAA6B,EAAE;MACvC9B,OAAO,CAACgF,IAAI,CAAC,iGAAiG,CAAC;MAE/G;MACA5D,UAAU,CAAC,MAAK;QACdpB,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;QAC3F,IAAI,IAAI,CAAC6B,6BAA6B,EAAE;UACtC,IAAI,CAACC,gCAAgC,CAACf,IAAI,CAAC;SAC5C,MAAM;UACLhB,OAAO,CAACiC,KAAK,CAAC,qFAAqF,CAAC;UACpG,IAAI,CAAC6K,wBAAwB,GAAG,KAAK,CAAC,CAAC;;MAE3C,CAAC,EAAE,IAAI,CAAC;MAER;;IAGF9M,OAAO,CAACC,GAAG,CAAC,+EAA+E,CAAC;IAE5F,IAAI;MACF;MACA,IAAI,CAACkN,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACvL,6BAA6B,CAACwL,aAAa,CAAC,EAAE;QAC7EtN,OAAO,CAACgF,IAAI,CAAC,8EAA8E,CAAC;QAE5F;QACA5D,UAAU,CAAC,MAAK;UACdpB,OAAO,CAACC,GAAG,CAAC,0FAA0F,CAAC;UACvG,IAAI,IAAI,CAAC6B,6BAA6B,IAAIqL,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACvL,6BAA6B,CAACwL,aAAa,CAAC,EAAE;YAClH,IAAI,CAACvL,gCAAgC,CAACf,IAAI,CAAC;WAC5C,MAAM;YACLhB,OAAO,CAACiC,KAAK,CAAC,+EAA+E,CAAC;;QAElG,CAAC,EAAE,IAAI,CAAC;QAER;;MAGF;MACA,MAAMsL,GAAG,GAAG,IAAI,CAACzL,6BAA6B,CAACwL,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;MAC7E,IAAI,CAACD,GAAG,EAAE;QACRvN,OAAO,CAACiC,KAAK,CAAC,kFAAkF,CAAC;QACjG;;MAEFjC,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;MAE1F,IAAI,IAAI,CAAC6K,0BAA0B,EAAE;QACnC9K,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE,IAAI,CAAC6K,0BAA0B,CAACoC,OAAO,EAAE;;MAG3C;MACA,MAAMO,YAAY,GAAGV,SAAS,CAACxE,MAAM,CAAC1D,MAAM;MAC5C,MAAM6I,aAAa,GAAGX,SAAS,CAACY,OAAO,CAAC9I,MAAM;MAC9C,MAAM+I,gBAAgB,GAAGb,SAAS,CAACc,UAAU,CAAChJ,MAAM;MACpD,MAAMiJ,aAAa,GAAGf,SAAS,CAACgB,OAAO,CAAClJ,MAAM;MAC9C,MAAMmJ,aAAa,GAAGjB,SAAS,CAACkB,OAAO,CAACpJ,MAAM;MAC9C,MAAMqJ,aAAa,GAAGnB,SAAS,CAACoB,OAAO,CAACtJ,MAAM;MAC9C,MAAMuJ,aAAa,GAAGrB,SAAS,CAACsB,OAAO,CAACxJ,MAAM;MAC9C,MAAMyJ,oBAAoB,GAAGvB,SAAS,CAACwB,cAAc,CAAC1J,MAAM;MAE5D7E,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5DwN,YAAY;QACZC,aAAa;QACbE,gBAAgB;QAChBE,aAAa;QACbE,aAAa;QACbE,aAAa;QACbE,aAAa;QACbE;OACD,CAAC;MAEF;MACA,IAAIZ,aAAa,KAAKD,YAAY,EAAE;QAClCzN,OAAO,CAACgF,IAAI,CAAC,gFAAgF,CAAC;QAC9F+H,SAAS,CAACY,OAAO,GAAG,IAAI,CAACa,iBAAiB,CAACzB,SAAS,CAACY,OAAO,EAAEF,YAAY,CAAC;;MAE7E,IAAIG,gBAAgB,KAAKH,YAAY,EAAE;QACrCzN,OAAO,CAACgF,IAAI,CAAC,mFAAmF,CAAC;QACjG+H,SAAS,CAACc,UAAU,GAAG,IAAI,CAACW,iBAAiB,CAACzB,SAAS,CAACc,UAAU,EAAEJ,YAAY,CAAC;;MAEnF,IAAIK,aAAa,KAAKL,YAAY,EAAE;QAClCzN,OAAO,CAACgF,IAAI,CAAC,gFAAgF,CAAC;QAC9F+H,SAAS,CAACgB,OAAO,GAAG,IAAI,CAACS,iBAAiB,CAACzB,SAAS,CAACgB,OAAO,EAAEN,YAAY,CAAC;;MAE7E,IAAIO,aAAa,KAAKP,YAAY,EAAE;QAClCzN,OAAO,CAACgF,IAAI,CAAC,gFAAgF,CAAC;QAC9F+H,SAAS,CAACkB,OAAO,GAAG,IAAI,CAACO,iBAAiB,CAACzB,SAAS,CAACkB,OAAO,EAAER,YAAY,CAAC;;MAE7E,IAAIS,aAAa,KAAKT,YAAY,EAAE;QAClCzN,OAAO,CAACgF,IAAI,CAAC,gFAAgF,CAAC;QAC9F+H,SAAS,CAACoB,OAAO,GAAG,IAAI,CAACK,iBAAiB,CAACzB,SAAS,CAACoB,OAAO,EAAEV,YAAY,CAAC;;MAE7E,IAAIW,aAAa,KAAKX,YAAY,EAAE;QAClCzN,OAAO,CAACgF,IAAI,CAAC,gFAAgF,CAAC;QAC9F+H,SAAS,CAACsB,OAAO,GAAG,IAAI,CAACG,iBAAiB,CAACzB,SAAS,CAACsB,OAAO,EAAEZ,YAAY,CAAC;;MAE7E,IAAIa,oBAAoB,KAAKb,YAAY,EAAE;QACzCzN,OAAO,CAACgF,IAAI,CAAC,uFAAuF,CAAC;QACrG+H,SAAS,CAACwB,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAACzB,SAAS,CAACwB,cAAc,EAAEd,YAAY,CAAC;;MAG3F;MACA,IAAI;QACFzN,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QAEpE;QACA,IAAI,CAAC,IAAI,CAAC6B,6BAA6B,IAAI,CAACqL,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACvL,6BAA6B,CAACwL,aAAa,CAAC,EAAE;UACpHtN,OAAO,CAACiC,KAAK,CAAC,oEAAoE,CAAC;UACnF;;QAGF;QACA,MAAMwM,KAAK,GAAG,IAAI,CAAC3M,6BAA6B,CAACwL,aAAa,CAACmB,KAAK;QACpE,MAAMC,MAAM,GAAG,IAAI,CAAC5M,6BAA6B,CAACwL,aAAa,CAACoB,MAAM;QACtE1O,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;UAAEwO,KAAK;UAAEC;QAAM,CAAE,CAAC;QAEpF;QACA,MAAM9D,UAAU,GAAG,IAAI,CAACgC,qBAAqB,CAAC,YAAY,CAAC;QAE3D;QACA,MAAMjC,QAAQ,GAAG,CACf;UACEjL,KAAK,EAAE,aAAa;UACpBsB,IAAI,EAAE+L,SAAS,CAACY,OAAO;UACvBgB,WAAW,EAAE,uBAAuB;UACpCC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,aAAa,CAAC,GAAG;SACnD,EACD;UACElL,KAAK,EAAE,aAAa;UACpBsB,IAAI,EAAE+L,SAAS,CAACc,UAAU;UAC1Bc,WAAW,EAAE,uBAAuB;UACpCC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,aAAa,CAAC,GAAG;SACnD,EACD;UACElL,KAAK,EAAE,oBAAoB;UAC3BsB,IAAI,EAAE+L,SAAS,CAACgB,OAAO;UACvBY,WAAW,EAAE,uBAAuB;UACpCC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,oBAAoB,CAAC,GAAG;SAC1D,EACD;UACElL,KAAK,EAAE,oBAAoB;UAC3BsB,IAAI,EAAE+L,SAAS,CAACkB,OAAO;UACvBU,WAAW,EAAE,uBAAuB;UACpCC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,oBAAoB,CAAC,GAAG;SAC1D,EACD;UACElL,KAAK,EAAE,cAAc;UACrBsB,IAAI,EAAE+L,SAAS,CAACoB,OAAO;UACvBQ,WAAW,EAAE,wBAAwB;UACrCC,eAAe,EAAE,0BAA0B;UAC3CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,cAAc,CAAC,GAAG;SACpD,EACD;UACElL,KAAK,EAAE,cAAc;UACrBsB,IAAI,EAAE+L,SAAS,CAACsB,OAAO;UACvBM,WAAW,EAAE,uBAAuB;UACpCC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,cAAc,CAAC,GAAG;SACpD,EACD;UACElL,KAAK,EAAE,cAAc;UACrBsB,IAAI,EAAE+L,SAAS,CAACwB,cAAc;UAC9BI,WAAW,EAAE,wBAAwB;UACrCC,eAAe,EAAE,0BAA0B;UAC3CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,cAAc,CAAC,GAAG,IAAI;UACvDoE,OAAO,EAAE;SACV,CACF;QAED;QACA,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAc,CAACvE,QAAQ,CAAC;QAEjD;QACA,IAAI,CAACG,0BAA0B,GAAG,IAAInM,KAAK,CAAC4O,GAAG,EAAE;UAC/CtK,IAAI,EAAE,MAAM;UACZjC,IAAI,EAAE;YACJuH,MAAM,EAAEwE,SAAS,CAACxE,MAAM;YACxBoC,QAAQ,EAAEA;WACX;UACHzH,OAAO,EAAE;YACPiM,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACP7J,KAAK,EAAE;gBACL8J,OAAO,EAAE,IAAI;gBACbC,IAAI,EAAExC,SAAS,CAACvH,KAAK,IAAI;eAC1B;cACDgK,OAAO,EAAE;gBACPC,IAAI,EAAE,OAAO;gBACbC,SAAS,EAAE;eACZ;cACDC,MAAM,EAAE;gBACNC,QAAQ,EAAE,KAAK;gBACfrH,MAAM,EAAE;kBACNsH,QAAQ,EAAE;iBACX;gBACDC,OAAO,EAAE,CAACC,EAAE,EAAEC,UAAU,EAAEL,MAAM,KAAI;kBAClC;kBACA,MAAMhL,KAAK,GAAGqL,UAAU,CAACC,YAAY;kBACrC,MAAMC,EAAE,GAAGP,MAAM,CAACpK,KAAK;kBACvB,IAAIZ,KAAK,KAAKlE,SAAS,EAAE;oBACvB,IAAIyP,EAAE,CAAClF,gBAAgB,CAACrG,KAAK,CAAC,EAAE;sBAC9BuL,EAAE,CAACC,IAAI,CAACxL,KAAK,CAAC;sBACdqL,UAAU,CAAC5E,MAAM,GAAG,IAAI;qBACzB,MAAM;sBACL8E,EAAE,CAACE,IAAI,CAACzL,KAAK,CAAC;sBACdqL,UAAU,CAAC5E,MAAM,GAAG,KAAK;;oBAG3B;oBACA,IAAI,CAACiF,oBAAoB,CAACH,EAAE,CAAC;oBAE7B;oBACA9O,UAAU,CAAC,MAAK;sBACd;sBACA,IAAI,CAACqJ,qBAAqB,CAAC,YAAY,EAAEyF,EAAE,CAAClP,IAAI,CAAC2J,QAAQ,CAAC;oBAC5D,CAAC,EAAE,CAAC,CAAC;;gBAET;;aAEH;YACD2F,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDjB,OAAO,EAAE,IAAI;gBACb9J,KAAK,EAAE;kBACL8J,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE;;eAET;cACDiB,CAAC,EAAE;gBACDlB,OAAO,EAAEL,WAAW,CAACwB,YAAY;gBACjCjL,KAAK,EAAE;kBACL8J,OAAO,EAAEL,WAAW,CAACwB,YAAY;kBACjClB,IAAI,EAAE;iBACP;gBACDmB,WAAW,EAAE;eACd;cACDC,EAAE,EAAE;gBACFrB,OAAO,EAAEL,WAAW,CAAC2B,aAAa;gBAClChB,QAAQ,EAAE,OAAO;gBACjBpK,KAAK,EAAE;kBACL8J,OAAO,EAAEL,WAAW,CAAC2B,aAAa;kBAClCrB,IAAI,EAAE;iBACP;gBACDmB,WAAW,EAAE,IAAI;gBACjBG,IAAI,EAAE;kBACJC,eAAe,EAAE,CAAC7B,WAAW,CAACwB,YAAY,CAAC;;;;;SAKpD,CAAC;;QACAzQ,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QAEvE;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAAC0J,0BAA0B,EAAE;YACnC,IAAI,CAACL,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAACK,0BAA0B,CAAC9J,IAAI,CAAC2J,QAAQ,CAAC;;UAEzF;UACA,IAAI,CAACmC,wBAAwB,GAAG,KAAK;QACvC,CAAC,EAAE,GAAG,CAAC;OACR,CAAC,OAAO7K,KAAK,EAAE;QACdjC,OAAO,CAACiC,KAAK,CAAC,+DAA+D,EAAEA,KAAK,CAAC;QACrF,IAAI,CAAC6K,wBAAwB,GAAG,KAAK,CAAC,CAAC;;KAE1C,CAAC,OAAO7K,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,kFAAkF,EAAEA,KAAK,CAAC;MACxG,IAAI,CAAC6K,wBAAwB,GAAG,KAAK,CAAC,CAAC;;EAEzC;EAEA;;;EAGApG,8BAA8B,CAAC1F,IAAS;IACtChB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEe,IAAI,CAAC;IAE3D;IACA,IAAI,CAAC+P,iCAAiC,GAAG,IAAI;IAE7C;IACA,IAAI,CAACvK,uBAAuB,GAAGxF,IAAI;IAEnC;IACA,MAAMiJ,OAAO,GAAGjJ,IAAI,IAAIA,IAAI,CAACuH,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACuH,MAAM,CAAC,IAAIvH,IAAI,CAACuH,MAAM,CAAC1D,MAAM,GAAG,CAAC,IAC3E7D,IAAI,CAAC2J,QAAQ,IAAIT,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAAC2J,QAAQ,CAAC,IAAI3J,IAAI,CAAC2J,QAAQ,CAAC9F,MAAM,GAAG,CAAC;IAEzF,IAAI,CAACoF,OAAO,EAAE;MACZ;MACA,IAAI,CAAC+G,+BAA+B,GAAG,KAAK;MAC5C,IAAI,CAACD,iCAAiC,GAAG,KAAK;MAE9C;MACA,IAAI,IAAI,CAAC9F,wBAAwB,EAAE;QACjC,IAAI,CAACA,wBAAwB,CAACiC,OAAO,EAAE;QACvC,IAAI,CAACjC,wBAAwB,GAAG,IAAI;;MAGtC;;IAGF;IACA,IAAI,CAAC+F,+BAA+B,GAAG,IAAI;IAE3C;IACA,IAAI,CAAC,IAAI,CAACvK,2BAA2B,EAAE;MACrCzG,OAAO,CAACgF,IAAI,CAAC,+FAA+F,CAAC;MAE7G;MACA5D,UAAU,CAAC,MAAK;QACdpB,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;QACzF,IAAI,IAAI,CAACwG,2BAA2B,EAAE;UACpC,IAAI,CAACC,8BAA8B,CAAC1F,IAAI,CAAC;SAC1C,MAAM;UACLhB,OAAO,CAACiC,KAAK,CAAC,mFAAmF,CAAC;UAClG,IAAI,CAAC8O,iCAAiC,GAAG,KAAK,CAAC,CAAC;;MAEpD,CAAC,EAAE,IAAI,CAAC;MAER;;IAGF/Q,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;IAE1F,IAAI;MACF;MACA,IAAI,CAACkN,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC5G,2BAA2B,CAAC6G,aAAa,CAAC,EAAE;QAC3EtN,OAAO,CAACgF,IAAI,CAAC,4EAA4E,CAAC;QAE1F;QACA5D,UAAU,CAAC,MAAK;UACdpB,OAAO,CAACC,GAAG,CAAC,wFAAwF,CAAC;UACrG,IAAI,IAAI,CAACwG,2BAA2B,IAAI0G,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC5G,2BAA2B,CAAC6G,aAAa,CAAC,EAAE;YAC9G,IAAI,CAAC5G,8BAA8B,CAAC1F,IAAI,CAAC;WAC1C,MAAM;YACLhB,OAAO,CAACiC,KAAK,CAAC,6EAA6E,CAAC;;QAEhG,CAAC,EAAE,IAAI,CAAC;QAER;;MAGF;MACA,MAAMsL,GAAG,GAAG,IAAI,CAAC9G,2BAA2B,CAAC6G,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;MAC3E,IAAI,CAACD,GAAG,EAAE;QACRvN,OAAO,CAACiC,KAAK,CAAC,gFAAgF,CAAC;QAC/F;;MAEFjC,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;MAEtF,IAAI,IAAI,CAACgL,wBAAwB,EAAE;QACjCjL,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,IAAI,CAACgL,wBAAwB,CAACiC,OAAO,EAAE;;MAG3C,IAAI;QACFlN,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAElE;QACA,IAAI,CAAC,IAAI,CAACwG,2BAA2B,IAAI,CAAC0G,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC5G,2BAA2B,CAAC6G,aAAa,CAAC,EAAE;UAChHtN,OAAO,CAACiC,KAAK,CAAC,kEAAkE,CAAC;UACjF;;QAGF;QACA,MAAMwM,KAAK,GAAG,IAAI,CAAChI,2BAA2B,CAAC6G,aAAa,CAACmB,KAAK;QAClE,MAAMC,MAAM,GAAG,IAAI,CAACjI,2BAA2B,CAAC6G,aAAa,CAACoB,MAAM;QACpE1O,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;UAAEwO,KAAK;UAAEC;QAAM,CAAE,CAAC;QAElF;QACA,MAAM9D,UAAU,GAAG,IAAI,CAACgC,qBAAqB,CAAC,sBAAsB,CAAC;QAErE;QACA,IAAIhC,UAAU,IAAI5J,IAAI,CAAC2J,QAAQ,EAAE;UAC/B3J,IAAI,CAAC2J,QAAQ,CAACxF,OAAO,CAAE0F,OAAY,IAAI;YACrC,IAAIA,OAAO,CAACnL,KAAK,IAAIkL,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC,KAAKe,SAAS,EAAE;cAC5DoK,OAAO,CAACO,MAAM,GAAG,CAACR,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC;;UAE/C,CAAC,CAAC;;QAGJ;QACA,MAAMuR,cAAc,GAAG,CAAC,GAAGjQ,IAAI,CAACuH,MAAM,CAAC;QAEvC;QACA,MAAM2I,qBAAqB,GAAG,IAAI,CAAC1R,SAAS,CAAC2R,2BAA2B,CAACF,cAAc,EAAE;UACvFG,OAAO,EAAE,IAAI;UACbC,SAAS,EAAE,EAAE;UACbC,iBAAiB,EAAE,IAAI,CAAC;SACzB,CAAC;QAEF;QACA,MAAMC,WAAW,GAAG;UAClBpC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,OAAO,EAAE;YACP7J,KAAK,EAAE;cACL8J,OAAO,EAAE,IAAI;cACbC,IAAI,EAAEvO,IAAI,CAACwE,KAAK,IAAI;aACrB;YACDgK,OAAO,EAAE;cACPC,IAAI,EAAE,OAAO;cACbC,SAAS,EAAE;aACZ;YACDC,MAAM,EAAE;cACNC,QAAQ,EAAE,KAAK;cACfrH,MAAM,EAAE;gBACNsH,QAAQ,EAAE;eACX;cACDC,OAAO,EAAE,CAACC,EAAO,EAAEC,UAAe,EAAEL,MAAW,KAAI;gBACjD;gBACA,MAAMhL,KAAK,GAAGqL,UAAU,CAACC,YAAY;gBACrC,MAAMC,EAAE,GAAGP,MAAM,CAACpK,KAAK;gBACvB,IAAIZ,KAAK,KAAKlE,SAAS,EAAE;kBACvB,IAAIyP,EAAE,CAAClF,gBAAgB,CAACrG,KAAK,CAAC,EAAE;oBAC9BuL,EAAE,CAACC,IAAI,CAACxL,KAAK,CAAC;oBACdqL,UAAU,CAAC5E,MAAM,GAAG,IAAI;mBACzB,MAAM;oBACL8E,EAAE,CAACE,IAAI,CAACzL,KAAK,CAAC;oBACdqL,UAAU,CAAC5E,MAAM,GAAG,KAAK;;kBAG3B;kBACAhK,UAAU,CAAC,MAAK;oBACd;oBACA,IAAI,CAACqJ,qBAAqB,CAAC,sBAAsB,EAAEyF,EAAE,CAAClP,IAAI,CAAC2J,QAAQ,CAAC;kBACtE,CAAC,EAAE,CAAC,CAAC;kBAEL;kBACAuF,EAAE,CAACsB,MAAM,EAAE;;cAEf;;WAEH;UACDlB,MAAM,EAAE;YACNC,CAAC,EAAE;cACDjB,OAAO,EAAE,IAAI;cACb9J,KAAK,EAAE;gBACL8J,OAAO,EAAE,IAAI;gBACbC,IAAI,EAAE;;aAET;YACDiB,CAAC,EAAE;cACDlB,OAAO,EAAE,IAAI;cACb9J,KAAK,EAAE;gBACL8J,OAAO,EAAE,IAAI;gBACbC,IAAI,EAAE;eACP;cACDmB,WAAW,EAAE;;;SAGlB;QAED;QACA,MAAMe,YAAY,GAAG,IAAI,CAACjS,SAAS,CAACkS,gBAAgB,CAACH,WAAW,EAAEL,qBAAqB,CAAC;QAExF;QACA,IAAI,CAACjG,wBAAwB,GAAG,IAAItM,KAAK,CAAC4O,GAAG,EAAE;UAC7CtK,IAAI,EAAE,KAAK;UACXjC,IAAI,EAAE;YACJuH,MAAM,EAAEvH,IAAI,CAACuH,MAAM;YACnBoC,QAAQ,EAAE3J,IAAI,CAAC2J;WAChB;UACDzH,OAAO,EAAEuO;SACV,CAAC;QACFzR,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QAErE;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAAC6J,wBAAwB,EAAE;YACjC,IAAI,CAACR,qBAAqB,CAAC,sBAAsB,EAAE,IAAI,CAACQ,wBAAwB,CAACjK,IAAI,CAAC2J,QAAQ,CAAC;;UAEjG;UACA,IAAI,CAACoG,iCAAiC,GAAG,KAAK;QAChD,CAAC,EAAE,GAAG,CAAC;OACR,CAAC,OAAO9O,KAAK,EAAE;QACdjC,OAAO,CAACiC,KAAK,CAAC,6DAA6D,EAAEA,KAAK,CAAC;QACnF,IAAI,CAAC8O,iCAAiC,GAAG,KAAK,CAAC,CAAC;;KAEnD,CAAC,OAAO9O,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,gFAAgF,EAAEA,KAAK,CAAC;MACtG,IAAI,CAAC8O,iCAAiC,GAAG,KAAK,CAAC,CAAC;;EAElD;EAEA;;;EAGAjK,6BAA6B,CAAC9F,IAAS;IACrChB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEe,IAAI,CAAC;IAE1D;IACA,IAAI,CAAC2Q,gCAAgC,GAAG,IAAI;IAE5C;IACA,IAAI,CAAC/K,sBAAsB,GAAG5F,IAAI;IAElC;IACA,MAAMiJ,OAAO,GAAGjJ,IAAI,IAAIA,IAAI,CAACuH,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACuH,MAAM,CAAC,IAAIvH,IAAI,CAACuH,MAAM,CAAC1D,MAAM,GAAG,CAAC,IAC3E7D,IAAI,CAAC2J,QAAQ,IAAIT,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAAC2J,QAAQ,CAAC,IAAI3J,IAAI,CAAC2J,QAAQ,CAAC9F,MAAM,GAAG,CAAC;IAEzF,IAAI,CAACoF,OAAO,EAAE;MACZ;MACA,IAAI,CAAC2H,8BAA8B,GAAG,KAAK;MAC3C,IAAI,CAACD,gCAAgC,GAAG,KAAK;MAE7C;MACA,IAAI,IAAI,CAACzG,uBAAuB,EAAE;QAChC,IAAI,CAACA,uBAAuB,CAACgC,OAAO,EAAE;QACtC,IAAI,CAAChC,uBAAuB,GAAG,IAAI;;MAGrC;;IAGF;IACA,IAAI,CAAC0G,8BAA8B,GAAG,IAAI;IAE1C;IACA,IAAI,CAAC,IAAI,CAAC/K,0BAA0B,EAAE;MACpC7G,OAAO,CAACgF,IAAI,CAAC,8FAA8F,CAAC;MAE5G;MACA5D,UAAU,CAAC,MAAK;QACdpB,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;QACxF,IAAI,IAAI,CAAC4G,0BAA0B,EAAE;UACnC,IAAI,CAACC,6BAA6B,CAAC9F,IAAI,CAAC;SACzC,MAAM;UACLhB,OAAO,CAACiC,KAAK,CAAC,kFAAkF,CAAC;UACjG,IAAI,CAAC0P,gCAAgC,GAAG,KAAK,CAAC,CAAC;;MAEnD,CAAC,EAAE,IAAI,CAAC;MAER;;IAGF3R,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;IAEzF,IAAI;MACF;MACA,IAAI,CAACkN,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACxG,0BAA0B,CAACyG,aAAa,CAAC,EAAE;QAC1EtN,OAAO,CAACgF,IAAI,CAAC,2EAA2E,CAAC;QAEzF;QACA5D,UAAU,CAAC,MAAK;UACdpB,OAAO,CAACC,GAAG,CAAC,uFAAuF,CAAC;UACpG,IAAI,IAAI,CAAC4G,0BAA0B,IAAIsG,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACxG,0BAA0B,CAACyG,aAAa,CAAC,EAAE;YAC5G,IAAI,CAACxG,6BAA6B,CAAC9F,IAAI,CAAC;WACzC,MAAM;YACLhB,OAAO,CAACiC,KAAK,CAAC,4EAA4E,CAAC;;QAE/F,CAAC,EAAE,IAAI,CAAC;QAER;;MAGF;MACA,MAAMsL,GAAG,GAAG,IAAI,CAAC1G,0BAA0B,CAACyG,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;MAC1E,IAAI,CAACD,GAAG,EAAE;QACRvN,OAAO,CAACiC,KAAK,CAAC,+EAA+E,CAAC;QAC9F;;MAEFjC,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;MAErF,IAAI,IAAI,CAACiL,uBAAuB,EAAE;QAChClL,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE,IAAI,CAACiL,uBAAuB,CAACgC,OAAO,EAAE;;MAG1C,IAAI;QACFlN,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QAEjE;QACA,IAAI,CAAC,IAAI,CAAC4G,0BAA0B,IAAI,CAACsG,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACxG,0BAA0B,CAACyG,aAAa,CAAC,EAAE;UAC9GtN,OAAO,CAACiC,KAAK,CAAC,iEAAiE,CAAC;UAChF;;QAGF;QACA,MAAMwM,KAAK,GAAG,IAAI,CAAC5H,0BAA0B,CAACyG,aAAa,CAACmB,KAAK;QACjE,MAAMC,MAAM,GAAG,IAAI,CAAC7H,0BAA0B,CAACyG,aAAa,CAACoB,MAAM;QACnE1O,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE;UAAEwO,KAAK;UAAEC;QAAM,CAAE,CAAC;QAEjF;QACA,MAAM9D,UAAU,GAAG,IAAI,CAACgC,qBAAqB,CAAC,qBAAqB,CAAC;QAEpE;QACA,IAAIhC,UAAU,IAAI5J,IAAI,CAAC2J,QAAQ,EAAE;UAC/B3J,IAAI,CAAC2J,QAAQ,CAACxF,OAAO,CAAE0F,OAAY,IAAI;YACrC,IAAIA,OAAO,CAACnL,KAAK,IAAIkL,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC,KAAKe,SAAS,EAAE;cAC5DoK,OAAO,CAACO,MAAM,GAAG,CAACR,UAAU,CAACC,OAAO,CAACnL,KAAK,CAAC;;UAE/C,CAAC,CAAC;;QAGJ;QACA,MAAMuR,cAAc,GAAG,CAAC,GAAGjQ,IAAI,CAACuH,MAAM,CAAC;QAEvC;QACA,MAAM2I,qBAAqB,GAAG,IAAI,CAAC1R,SAAS,CAAC2R,2BAA2B,CAACF,cAAc,EAAE;UACvFG,OAAO,EAAE,IAAI;UACbC,SAAS,EAAE,EAAE;UACbC,iBAAiB,EAAE,IAAI,CAAC;SACzB,CAAC;QAEF;QACA,MAAMC,WAAW,GAAG;UAClBpC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,OAAO,EAAE;YACP7J,KAAK,EAAE;cACL8J,OAAO,EAAE,IAAI;cACbC,IAAI,EAAEvO,IAAI,CAACwE,KAAK,IAAI;aACrB;YACDgK,OAAO,EAAE;cACPC,IAAI,EAAE,OAAO;cACbC,SAAS,EAAE;aACZ;YACDC,MAAM,EAAE;cACNC,QAAQ,EAAE,KAAK;cACfrH,MAAM,EAAE;gBACNsH,QAAQ,EAAE;eACX;cACDC,OAAO,EAAE,CAACC,EAAO,EAAEC,UAAe,EAAEL,MAAW,KAAI;gBACjD;gBACA,MAAMhL,KAAK,GAAGqL,UAAU,CAACC,YAAY;gBACrC,MAAMC,EAAE,GAAGP,MAAM,CAACpK,KAAK;gBACvB,IAAIZ,KAAK,KAAKlE,SAAS,EAAE;kBACvB,IAAIyP,EAAE,CAAClF,gBAAgB,CAACrG,KAAK,CAAC,EAAE;oBAC9BuL,EAAE,CAACC,IAAI,CAACxL,KAAK,CAAC;oBACdqL,UAAU,CAAC5E,MAAM,GAAG,IAAI;mBACzB,MAAM;oBACL8E,EAAE,CAACE,IAAI,CAACzL,KAAK,CAAC;oBACdqL,UAAU,CAAC5E,MAAM,GAAG,KAAK;;kBAG3B;kBACAhK,UAAU,CAAC,MAAK;oBACd;oBACA,IAAI,CAACqJ,qBAAqB,CAAC,qBAAqB,EAAEyF,EAAE,CAAClP,IAAI,CAAC2J,QAAQ,CAAC;kBACrE,CAAC,EAAE,CAAC,CAAC;kBAEL;kBACAuF,EAAE,CAACsB,MAAM,EAAE;;cAEf;;WAEH;UACDlB,MAAM,EAAE;YACNC,CAAC,EAAE;cACDjB,OAAO,EAAE,IAAI;cACb9J,KAAK,EAAE;gBACL8J,OAAO,EAAE,IAAI;gBACbC,IAAI,EAAE;;aAET;YACDiB,CAAC,EAAE;cACDlB,OAAO,EAAE,IAAI;cACb9J,KAAK,EAAE;gBACL8J,OAAO,EAAE,IAAI;gBACbC,IAAI,EAAE;eACP;cACDmB,WAAW,EAAE;;;SAGlB;QAED;QACA,MAAMe,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAEL,qBAAqB,CAAC;QAE9E;QACA,IAAI,CAAChG,uBAAuB,GAAG,IAAIvM,KAAK,CAAC4O,GAAG,EAAE;UAC5CtK,IAAI,EAAE,KAAK;UACXjC,IAAI,EAAE;YACJuH,MAAM,EAAEvH,IAAI,CAACuH,MAAM;YACnBoC,QAAQ,EAAE3J,IAAI,CAAC2J;WAChB;UACDzH,OAAO,EAAEuO;SACV,CAAC;QACFzR,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QAEpE;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAAC8J,uBAAuB,EAAE;YAChC,IAAI,CAACT,qBAAqB,CAAC,qBAAqB,EAAE,IAAI,CAACS,uBAAuB,CAAClK,IAAI,CAAC2J,QAAQ,CAAC;;UAE/F;UACA,IAAI,CAACgH,gCAAgC,GAAG,KAAK;QAC/C,CAAC,EAAE,GAAG,CAAC;OACR,CAAC,OAAO1P,KAAK,EAAE;QACdjC,OAAO,CAACiC,KAAK,CAAC,4DAA4D,EAAEA,KAAK,CAAC;QAClF,IAAI,CAAC0P,gCAAgC,GAAG,KAAK,CAAC,CAAC;;KAElD,CAAC,OAAO1P,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,+EAA+E,EAAEA,KAAK,CAAC;MACrG,IAAI,CAAC0P,gCAAgC,GAAG,KAAK,CAAC,CAAC;;EAEjD;EAEA;;;EAGAvH,uBAAuB,CAACpJ,IAAS;IAC/BhB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEe,IAAI,CAAC;IAEpD;IACA,IAAI,CAAC6Q,0BAA0B,GAAG,IAAI;IAEtC;IACA,IAAI,CAAC7H,qBAAqB,GAAGhJ,IAAI;IAEjC;IACA,MAAMiJ,OAAO,GAAGjJ,IAAI,IAAIA,IAAI,CAACuH,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACuH,MAAM,CAAC,IAAIvH,IAAI,CAACuH,MAAM,CAAC1D,MAAM,GAAG,CAAC;IAE3F,IAAI,CAACoF,OAAO,EAAE;MACZ;MACA,IAAI,CAAC9C,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAAC0K,0BAA0B,GAAG,KAAK;MAEvC;MACA,IAAI,IAAI,CAAC1G,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,CAAC+B,OAAO,EAAE;QAChC,IAAI,CAAC/B,iBAAiB,GAAG,IAAI;;MAG/B;;IAGF;IACA,IAAInK,IAAI,CAACnB,SAAS,IAAImB,IAAI,CAAClB,UAAU,EAAE;MACrCE,OAAO,CAACC,GAAG,CAAC,uEAAuE,EAAE;QACnFJ,SAAS,EAAEmB,IAAI,CAACnB,SAAS;QACzBC,UAAU,EAAEkB,IAAI,CAAClB;OAClB,CAAC;MAEF;MACA,IAAI,IAAI,CAACsH,sBAAsB,CAACvH,SAAS,KAAKmB,IAAI,CAACnB,SAAS,IACxD,IAAI,CAACuH,sBAAsB,CAACtH,UAAU,KAAKkB,IAAI,CAAClB,UAAU,EAAE;QAE9D,IAAI,CAACsH,sBAAsB,CAACvH,SAAS,GAAGmB,IAAI,CAACnB,SAAS;QACtD,IAAI,CAACuH,sBAAsB,CAACtH,UAAU,GAAGkB,IAAI,CAAClB,UAAU;QAExD;QACA,IAAI,CAACuH,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAACD,sBAAsB,CAAC;;;IAI7E;IACA,IAAI,CAACD,wBAAwB,GAAG,IAAI;IAEpC;IACA,IAAI,CAAC,IAAI,CAAC2K,oBAAoB,EAAE;MAC9B9R,OAAO,CAACgF,IAAI,CAAC,wFAAwF,CAAC;MAEtG;MACA5D,UAAU,CAAC,MAAK;QACdpB,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;QAClF,IAAI,IAAI,CAAC6R,oBAAoB,EAAE;UAC7B,IAAI,CAAC1H,uBAAuB,CAACpJ,IAAI,CAAC;SACnC,MAAM;UACLhB,OAAO,CAACiC,KAAK,CAAC,4EAA4E,CAAC;UAC3F,IAAI,CAAC4P,0BAA0B,GAAG,KAAK,CAAC,CAAC;;MAE7C,CAAC,EAAE,IAAI,CAAC;MAER;;IAGF7R,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;IAEnF,IAAI;MACF;MACA,IAAI,CAACkN,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACyE,oBAAoB,CAACxE,aAAa,CAAC,EAAE;QACpEtN,OAAO,CAACgF,IAAI,CAAC,qEAAqE,CAAC;QAEnF;QACA5D,UAAU,CAAC,MAAK;UACdpB,OAAO,CAACC,GAAG,CAAC,iFAAiF,CAAC;UAC9F,IAAI,IAAI,CAAC6R,oBAAoB,IAAI3E,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACyE,oBAAoB,CAACxE,aAAa,CAAC,EAAE;YAChG,IAAI,CAAClD,uBAAuB,CAACpJ,IAAI,CAAC;WACnC,MAAM;YACLhB,OAAO,CAACiC,KAAK,CAAC,sEAAsE,CAAC;;QAEzF,CAAC,EAAE,IAAI,CAAC;QAER;;MAGF;MACA,MAAMsL,GAAG,GAAG,IAAI,CAACuE,oBAAoB,CAACxE,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;MACpE,IAAI,CAACD,GAAG,EAAE;QACRvN,OAAO,CAACiC,KAAK,CAAC,yEAAyE,CAAC;QACxF;;MAEFjC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAE/E,IAAI,IAAI,CAACkL,iBAAiB,EAAE;QAC1BnL,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9D,IAAI,CAACkL,iBAAiB,CAAC+B,OAAO,EAAE;;MAGpC,IAAI;QACFlN,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAE3D;QACA,IAAI,CAAC,IAAI,CAAC6R,oBAAoB,IAAI,CAAC3E,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACyE,oBAAoB,CAACxE,aAAa,CAAC,EAAE;UAClGtN,OAAO,CAACiC,KAAK,CAAC,2DAA2D,CAAC;UAC1E;;QAGF;QACA,MAAMwM,KAAK,GAAG,IAAI,CAACqD,oBAAoB,CAACxE,aAAa,CAACmB,KAAK;QAC3D,MAAMC,MAAM,GAAG,IAAI,CAACoD,oBAAoB,CAACxE,aAAa,CAACoB,MAAM;QAC7D1O,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;UAAEwO,KAAK;UAAEC;QAAM,CAAE,CAAC;QAE3E;QACA,IAAIzE,OAAO,EAAE;UACX,MAAMwD,YAAY,GAAGzM,IAAI,CAACuH,MAAM,CAAC1D,MAAM;UAEvC;UACA,IAAI,CAAC7D,IAAI,CAAC2M,OAAO,IAAI3M,IAAI,CAAC2M,OAAO,CAAC9I,MAAM,KAAK4I,YAAY,EAAE;YACzDzN,OAAO,CAACgF,IAAI,CAAC,uEAAuE,CAAC;YACrFhE,IAAI,CAAC2M,OAAO,GAAG,IAAI,CAACa,iBAAiB,CAACxN,IAAI,CAAC2M,OAAO,IAAI,EAAE,EAAEF,YAAY,CAAC;;UAEzE,IAAI,CAACzM,IAAI,CAAC6M,UAAU,IAAI7M,IAAI,CAAC6M,UAAU,CAAChJ,MAAM,KAAK4I,YAAY,EAAE;YAC/DzN,OAAO,CAACgF,IAAI,CAAC,0EAA0E,CAAC;YACxFhE,IAAI,CAAC6M,UAAU,GAAG,IAAI,CAACW,iBAAiB,CAACxN,IAAI,CAAC6M,UAAU,IAAI,EAAE,EAAEJ,YAAY,CAAC;;UAE/E,IAAI,CAACzM,IAAI,CAAC+M,OAAO,IAAI/M,IAAI,CAAC+M,OAAO,CAAClJ,MAAM,KAAK4I,YAAY,EAAE;YACzDzN,OAAO,CAACgF,IAAI,CAAC,uEAAuE,CAAC;YACrFhE,IAAI,CAAC+M,OAAO,GAAG,IAAI,CAACS,iBAAiB,CAACxN,IAAI,CAAC+M,OAAO,IAAI,EAAE,EAAEN,YAAY,CAAC;;UAEzE,IAAI,CAACzM,IAAI,CAACuN,cAAc,IAAIvN,IAAI,CAACuN,cAAc,CAAC1J,MAAM,KAAK4I,YAAY,EAAE;YACvEzN,OAAO,CAACgF,IAAI,CAAC,8EAA8E,CAAC;YAC5FhE,IAAI,CAACuN,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAACxN,IAAI,CAACuN,cAAc,IAAI,EAAE,EAAEd,YAAY,CAAC;;;QAIzF;QACA,MAAM7C,UAAU,GAAG,IAAI,CAACgC,qBAAqB,CAAC,eAAe,CAAC;QAE9D;QACA,MAAMjC,QAAQ,GAAG,CACf;UACEjL,KAAK,EAAE,aAAa;UACpBsB,IAAI,EAAEA,IAAI,CAAC2M,OAAO;UAClBgB,WAAW,EAAE,uBAAuB;UACpCC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,aAAa,CAAC,GAAG;SACnD,EACD;UACElL,KAAK,EAAE,aAAa;UACpBsB,IAAI,EAAEA,IAAI,CAAC6M,UAAU;UACrBc,WAAW,EAAE,uBAAuB;UACpCC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,aAAa,CAAC,GAAG;SACnD,EACD;UACElL,KAAK,EAAE,oBAAoB;UAC3BsB,IAAI,EAAEA,IAAI,CAAC+M,OAAO;UAClBY,WAAW,EAAE,uBAAuB;UACpCC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,oBAAoB,CAAC,GAAG;SAC1D,EACD;UACElL,KAAK,EAAE,cAAc;UACrBsB,IAAI,EAAEA,IAAI,CAACuN,cAAc;UACzBI,WAAW,EAAE,wBAAwB;UACrCC,eAAe,EAAE,0BAA0B;UAC3CC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE,KAAK;UACX3D,MAAM,EAAER,UAAU,GAAG,CAACA,UAAU,CAAC,cAAc,CAAC,GAAG,KAAK;UACxDoE,OAAO,EAAE;SACV,CACF;QAED;QACA,IAAI,CAAC7D,iBAAiB,GAAG,IAAIxM,KAAK,CAAC4O,GAAG,EAAE;UACtCtK,IAAI,EAAE,MAAM;UACZjC,IAAI,EAAE;YACJuH,MAAM,EAAEvH,IAAI,CAACuH,MAAM;YACnBoC,QAAQ,EAAEA;WACX;UACDzH,OAAO,EAAE;YACPiM,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACP7J,KAAK,EAAE;gBACL8J,OAAO,EAAE,IAAI;gBACbC,IAAI,EAAEvO,IAAI,CAACwE,KAAK,IAAI;eACrB;cACDgK,OAAO,EAAE;gBACPC,IAAI,EAAE,OAAO;gBACbC,SAAS,EAAE;eACZ;cACDC,MAAM,EAAE;gBACNC,QAAQ,EAAE,KAAK;gBACfrH,MAAM,EAAE;kBACNsH,QAAQ,EAAE;iBACX;gBACDC,OAAO,EAAE,CAACC,EAAE,EAAEC,UAAU,EAAEL,MAAM,KAAI;kBAClC;kBACA,MAAMhL,KAAK,GAAGqL,UAAU,CAACC,YAAY;kBACrC,MAAMC,EAAE,GAAGP,MAAM,CAACpK,KAAK;kBACvB,IAAIZ,KAAK,KAAKlE,SAAS,EAAE;oBACvB,IAAIyP,EAAE,CAAClF,gBAAgB,CAACrG,KAAK,CAAC,EAAE;sBAC9BuL,EAAE,CAACC,IAAI,CAACxL,KAAK,CAAC;sBACdqL,UAAU,CAAC5E,MAAM,GAAG,IAAI;qBACzB,MAAM;sBACL8E,EAAE,CAACE,IAAI,CAACzL,KAAK,CAAC;sBACdqL,UAAU,CAAC5E,MAAM,GAAG,KAAK;;oBAG3B;oBACAhK,UAAU,CAAC,MAAK;sBACd;sBACA,IAAI,CAACqJ,qBAAqB,CAAC,eAAe,EAAEyF,EAAE,CAAClP,IAAI,CAAC2J,QAAQ,CAAC;oBAC/D,CAAC,EAAE,CAAC,CAAC;oBAEL;oBACAuF,EAAE,CAACsB,MAAM,EAAE;;gBAEf;;aAEH;YACDlB,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDjB,OAAO,EAAE,IAAI;gBACb9J,KAAK,EAAE;kBACL8J,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE;;eAET;cACDiB,CAAC,EAAE;gBACDlB,OAAO,EAAE,IAAI;gBACb9J,KAAK,EAAE;kBACL8J,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE;iBACP;gBACDmB,WAAW,EAAE;eACd;cACDC,EAAE,EAAE;gBACFrB,OAAO,EAAE,IAAI;gBACbM,QAAQ,EAAE,OAAO;gBACjBpK,KAAK,EAAE;kBACL8J,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAE;iBACP;gBACDmB,WAAW,EAAE,IAAI;gBACjBG,IAAI,EAAE;kBACJC,eAAe,EAAE;;;;;SAK1B,CAAC;QACF9Q,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAE9D;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAAC+J,iBAAiB,EAAE;YAC1B,IAAI,CAACV,qBAAqB,CAAC,eAAe,EAAE,IAAI,CAACU,iBAAiB,CAACnK,IAAI,CAAC2J,QAAQ,CAAC;;UAEnF;UACA,IAAI,CAACkH,0BAA0B,GAAG,KAAK;QACzC,CAAC,EAAE,GAAG,CAAC;OACR,CAAC,OAAO5P,KAAK,EAAE;QACdjC,OAAO,CAACiC,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;QAC5E,IAAI,CAAC4P,0BAA0B,GAAG,KAAK,CAAC,CAAC;;KAE5C,CAAC,OAAO5P,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,yEAAyE,EAAEA,KAAK,CAAC;MAC/F,IAAI,CAAC4P,0BAA0B,GAAG,KAAK,CAAC,CAAC;;EAE3C;EAMA;;;;;;EAMQH,gBAAgB,CAACK,UAAe,EAAEC,eAAoB;IAC5D,IAAI,CAACA,eAAe,IAAIC,MAAM,CAACC,IAAI,CAACF,eAAe,CAAC,CAACnN,MAAM,KAAK,CAAC,EAAE;MACjE,OAAOkN,UAAU;;IAGnB,MAAMI,MAAM,GAAG;MAAE,GAAGJ;IAAU,CAAE;IAEhC;IACA,IAAIC,eAAe,CAAC1B,MAAM,EAAE;MAC1B6B,MAAM,CAAC7B,MAAM,GAAG6B,MAAM,CAAC7B,MAAM,IAAI,EAAE;MACnC,IAAI0B,eAAe,CAAC1B,MAAM,CAACC,CAAC,EAAE;QAC5B4B,MAAM,CAAC7B,MAAM,CAACC,CAAC,GAAG4B,MAAM,CAAC7B,MAAM,CAACC,CAAC,IAAI,EAAE;QACvC4B,MAAM,CAAC7B,MAAM,CAACC,CAAC,CAAC6B,KAAK,GAAG;UACtB,GAAGD,MAAM,CAAC7B,MAAM,CAACC,CAAC,CAAC6B,KAAK;UACxB,GAAGJ,eAAe,CAAC1B,MAAM,CAACC,CAAC,CAAC6B;SAC7B;;;IAIL;IACA,IAAIJ,eAAe,CAAC3C,OAAO,EAAE;MAC3B8C,MAAM,CAAC9C,OAAO,GAAG8C,MAAM,CAAC9C,OAAO,IAAI,EAAE;MACrC,IAAI2C,eAAe,CAAC3C,OAAO,CAACG,OAAO,EAAE;QACnC2C,MAAM,CAAC9C,OAAO,CAACG,OAAO,GAAG2C,MAAM,CAAC9C,OAAO,CAACG,OAAO,IAAI,EAAE;QACrD2C,MAAM,CAAC9C,OAAO,CAACG,OAAO,CAAC6C,SAAS,GAAGF,MAAM,CAAC9C,OAAO,CAACG,OAAO,CAAC6C,SAAS,IAAI,EAAE;QAEzE;QACAJ,MAAM,CAACC,IAAI,CAACF,eAAe,CAAC3C,OAAO,CAACG,OAAO,CAAC6C,SAAS,CAAC,CAAClN,OAAO,CAACmN,GAAG,IAAG;UACnEH,MAAM,CAAC9C,OAAO,CAACG,OAAO,CAAC6C,SAAS,CAACC,GAAG,CAAC,GAAGN,eAAe,CAAC3C,OAAO,CAACG,OAAO,CAAC6C,SAAS,CAACC,GAAG,CAAC;QACxF,CAAC,CAAC;;;IAIN,OAAOH,MAAM;EACf;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;;;EAGQnF,qCAAqC,CAAChM,IAAS;IACrDhB,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEe,IAAI,CAAC;IAE1E;IACA,MAAM+L,SAAS,GAUX;MACFxE,MAAM,EAAE,EAAE;MACVoF,OAAO,EAAE,EAAE;MACXE,UAAU,EAAE,EAAE;MACdE,OAAO,EAAE,EAAE;MACXE,OAAO,EAAE,EAAE;MACXE,OAAO,EAAE,EAAE;MACXE,OAAO,EAAE,EAAE;MACXE,cAAc,EAAE,EAAE;MAClB/I,KAAK,EAAE;KACR;IAED;IACA,IAAI,CAACxE,IAAI,EAAE;MACThB,OAAO,CAACgF,IAAI,CAAC,6DAA6D,CAAC;MAC3E,OAAO+H,SAAS;;IAGlB,IAAI;MACF;MACA,IAAI/L,IAAI,CAACuH,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACuH,MAAM,CAAC,IAAIvH,IAAI,CAACuH,MAAM,CAAC1D,MAAM,GAAG,CAAC,EAAE;QACvE7E,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;QACzF8M,SAAS,CAACxE,MAAM,GAAGvH,IAAI,CAACuH,MAAM;QAC9BwE,SAAS,CAACY,OAAO,GAAG3M,IAAI,CAAC2M,OAAO,IAAI,EAAE;QACtCZ,SAAS,CAACc,UAAU,GAAG7M,IAAI,CAAC6M,UAAU,IAAI,EAAE;QAC5Cd,SAAS,CAACgB,OAAO,GAAG/M,IAAI,CAAC+M,OAAO,IAAI,EAAE;QACtChB,SAAS,CAACkB,OAAO,GAAGjN,IAAI,CAACiN,OAAO,IAAI,EAAE;QACtClB,SAAS,CAACoB,OAAO,GAAGnN,IAAI,CAACmN,OAAO,IAAI,EAAE;QACtCpB,SAAS,CAACsB,OAAO,GAAGrN,IAAI,CAACqN,OAAO,IAAI,EAAE;QACtCtB,SAAS,CAACwB,cAAc,GAAGvN,IAAI,CAACuN,cAAc,IAAI,EAAE;QAEpD;QACA,IAAIvN,IAAI,CAACwE,KAAK,EAAE;UACduH,SAAS,CAACvH,KAAK,GAAGxE,IAAI,CAACwE,KAAK;;;MAGhC;MAAA,KACK,IAAIxE,IAAI,CAACwH,SAAS,IAAI0B,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACwH,SAAS,CAAC,IAAIxH,IAAI,CAACwH,SAAS,CAAC3D,MAAM,GAAG,CAAC,EAAE;QACrF7E,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;QAC7E,MAAMuI,SAAS,GAAGxH,IAAI,CAACwH,SAAS;QAEhC;QACAA,SAAS,CAACvE,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;UAChC,MAAMoO,KAAK,GAAGrO,CAAC,CAACsO,IAAI,GAAG,IAAIjR,IAAI,CAAC2C,CAAC,CAACsO,IAAI,CAAC,CAACC,OAAO,EAAE,GAAG,CAAC;UACrD,MAAMC,KAAK,GAAGvO,CAAC,CAACqO,IAAI,GAAG,IAAIjR,IAAI,CAAC4C,CAAC,CAACqO,IAAI,CAAC,CAACC,OAAO,EAAE,GAAG,CAAC;UACrD,OAAOF,KAAK,GAAGG,KAAK;QACtB,CAAC,CAAC;QAEF;QACA3F,SAAS,CAACxE,MAAM,GAAGC,SAAS,CAACjE,GAAG,CAAEoO,IAAS,IAAI;UAC7C,IAAIA,IAAI,CAACH,IAAI,EAAE;YACb,MAAMA,IAAI,GAAG,IAAIjR,IAAI,CAACoR,IAAI,CAACH,IAAI,CAAC;YAChC,OAAO,IAAI,CAACI,UAAU,CAACJ,IAAI,CAAC;;UAE9B,OAAO,EAAE;QACX,CAAC,CAAC,CAACK,MAAM,CAAEnT,KAAa,IAAKA,KAAK,KAAK,EAAE,CAAC;QAE1C;QACAqN,SAAS,CAACY,OAAO,GAAGnF,SAAS,CAACjE,GAAG,CAAEoO,IAAS,IAAKA,IAAI,CAACpG,GAAG,KAAK9L,SAAS,GAAGkS,IAAI,CAACpG,GAAG,GAAG,CAAC,CAAC;QACvFQ,SAAS,CAACc,UAAU,GAAGrF,SAAS,CAACjE,GAAG,CAAEoO,IAAS,IAAKA,IAAI,CAACnG,MAAM,KAAK/L,SAAS,GAAGkS,IAAI,CAACnG,MAAM,GAAG,CAAC,CAAC;QAChGO,SAAS,CAACgB,OAAO,GAAGvF,SAAS,CAACjE,GAAG,CAAEoO,IAAS,IAAKA,IAAI,CAAClG,WAAW,KAAKhM,SAAS,GAAGkS,IAAI,CAAClG,WAAW,GAAG,CAAC,CAAC;QACvGM,SAAS,CAACkB,OAAO,GAAGzF,SAAS,CAACjE,GAAG,CAAEoO,IAAS,IAAKA,IAAI,CAACG,WAAW,KAAKrS,SAAS,GAAGkS,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;QACvG/F,SAAS,CAACoB,OAAO,GAAG3F,SAAS,CAACjE,GAAG,CAAEoO,IAAS,IAAKA,IAAI,CAACtG,GAAG,KAAK5L,SAAS,GAAGkS,IAAI,CAACtG,GAAG,GAAG,CAAC,CAAC;QACvFU,SAAS,CAACsB,OAAO,GAAG7F,SAAS,CAACjE,GAAG,CAAEoO,IAAS,IAAKA,IAAI,CAACrG,GAAG,KAAK7L,SAAS,GAAGkS,IAAI,CAACrG,GAAG,GAAG,CAAC,CAAC;QACvFS,SAAS,CAACwB,cAAc,GAAG/F,SAAS,CAACjE,GAAG,CAAEoO,IAAS,IAAKA,IAAI,CAACvG,UAAU,KAAK3L,SAAS,GAAGkS,IAAI,CAACvG,UAAU,GAAG,CAAC,CAAC;QAE5G;QACA,IAAIpL,IAAI,CAACwE,KAAK,EAAE;UACduH,SAAS,CAACvH,KAAK,GAAGxE,IAAI,CAACwE,KAAK;;OAE/B,MACI;QACHxF,OAAO,CAACgF,IAAI,CAAC,4DAA4D,CAAC;QAE1E;QACA,IAAI,OAAOhE,IAAI,KAAK,QAAQ,EAAE;UAC5B;UACA,KAAK,MAAMsR,GAAG,IAAItR,IAAI,EAAE;YACtB,IAAIsR,GAAG,KAAK,QAAQ,IAAIpI,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACsR,GAAG,CAAC,CAAC,EAAE;cAChDvF,SAAS,CAACxE,MAAM,GAAGvH,IAAI,CAACsR,GAAG,CAAC,CAAC/N,GAAG,CAAEoO,IAAS,IAAKrR,MAAM,CAACqR,IAAI,CAAC,CAAC;aAC9D,MAAM,IAAIL,GAAG,KAAK,SAAS,IAAIpI,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACsR,GAAG,CAAC,CAAC,EAAE;cACxDvF,SAAS,CAACY,OAAO,GAAG3M,IAAI,CAACsR,GAAG,CAAC,CAAC/N,GAAG,CAAEoO,IAAS,IAAKjR,MAAM,CAACiR,IAAI,CAAC,IAAI,CAAC,CAAC;aACpE,MAAM,IAAIL,GAAG,KAAK,YAAY,IAAIpI,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACsR,GAAG,CAAC,CAAC,EAAE;cAC3DvF,SAAS,CAACc,UAAU,GAAG7M,IAAI,CAACsR,GAAG,CAAC,CAAC/N,GAAG,CAAEoO,IAAS,IAAKjR,MAAM,CAACiR,IAAI,CAAC,IAAI,CAAC,CAAC;aACvE,MAAM,IAAIL,GAAG,KAAK,SAAS,IAAIpI,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACsR,GAAG,CAAC,CAAC,EAAE;cACxDvF,SAAS,CAACgB,OAAO,GAAG/M,IAAI,CAACsR,GAAG,CAAC,CAAC/N,GAAG,CAAEoO,IAAS,IAAKjR,MAAM,CAACiR,IAAI,CAAC,IAAI,CAAC,CAAC;aACpE,MAAM,IAAIL,GAAG,KAAK,SAAS,IAAIpI,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACsR,GAAG,CAAC,CAAC,EAAE;cACxDvF,SAAS,CAACkB,OAAO,GAAGjN,IAAI,CAACsR,GAAG,CAAC,CAAC/N,GAAG,CAAEoO,IAAS,IAAKjR,MAAM,CAACiR,IAAI,CAAC,IAAI,CAAC,CAAC;aACpE,MAAM,IAAIL,GAAG,KAAK,SAAS,IAAIpI,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACsR,GAAG,CAAC,CAAC,EAAE;cACxDvF,SAAS,CAACoB,OAAO,GAAGnN,IAAI,CAACsR,GAAG,CAAC,CAAC/N,GAAG,CAAEoO,IAAS,IAAKjR,MAAM,CAACiR,IAAI,CAAC,IAAI,CAAC,CAAC;aACpE,MAAM,IAAIL,GAAG,KAAK,SAAS,IAAIpI,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACsR,GAAG,CAAC,CAAC,EAAE;cACxDvF,SAAS,CAACsB,OAAO,GAAGrN,IAAI,CAACsR,GAAG,CAAC,CAAC/N,GAAG,CAAEoO,IAAS,IAAKjR,MAAM,CAACiR,IAAI,CAAC,IAAI,CAAC,CAAC;aACpE,MAAM,IAAIL,GAAG,KAAK,gBAAgB,IAAIpI,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAACsR,GAAG,CAAC,CAAC,EAAE;cAC/DvF,SAAS,CAACwB,cAAc,GAAGvN,IAAI,CAACsR,GAAG,CAAC,CAAC/N,GAAG,CAAEoO,IAAS,IAAKjR,MAAM,CAACiR,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3E,MAAM,IAAIL,GAAG,KAAK,OAAO,IAAI,OAAOtR,IAAI,CAACsR,GAAG,CAAC,KAAK,QAAQ,EAAE;cAC3DvF,SAAS,CAACvH,KAAK,GAAGxE,IAAI,CAACsR,GAAG,CAAC;;;;;KAKpC,CAAC,OAAOrQ,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,mEAAmE,EAAEA,KAAK,CAAC;;IAG3FjC,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE8M,SAAS,CAAC;IAChF,OAAOA,SAAS;EAClB;EAEA;;;EAGQ1K,YAAY;IAClBrC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IAEnD;IACA,MAAM8S,WAAW,GAA2B;MAC1C,wBAAwB,EAAE,kEAAkE,GAC1F,2HAA2H,GAC3H,sEAAsE,GACtE,yFAAyF;MAC3F,sBAAsB,EAAE,4EAA4E,GAClG,gHAAgH,GAChH,6HAA6H,GAC7H,0EAA0E;MAC5E,qBAAqB,EAAE,+EAA+E,GACpG,4EAA4E,GAC5E,0HAA0H,GAC1H,gGAAgG;MAClG,iBAAiB,EAAE,uFAAuF,GACxG,qHAAqH,GACrH,qGAAqG;MACvG,SAAS,EAAE,sGAAsG,GAC/G,qHAAqH,GACrH,qGAAqG;MACvG,qBAAqB,EAAE,8EAA8E,GACnG,qHAAqH,GACrH,qGAAqG;MACvG,aAAa,EAAE,wCAAwC,GACrD,qHAAqH,GACrH,qGAAqG;MACvG,eAAe,EAAE,oDAAoD,GACnE,2HAA2H,GAC3H,sEAAsE,GACtE,8EAA8E;MAChF,mBAAmB,EAAE,sEAAsE,GACzF,uEAAuE,GACvE,+EAA+E;MACjF,sBAAsB,EAAE,oEAAoE,GAC1F,kFAAkF,GAClF,wEAAwE,GACxE,qGAAqG;MACvG,mBAAmB,EAAE,2EAA2E,GAC9F,sFAAsF,GACtF,0CAA0C,GAC1C,uGAAuG;MACzG,aAAa,EAAE,wDAAwD,GACrE,qDAAqD,GACrD;KACH;IAED,IAAI;MACF;MACA3R,UAAU,CAAC,MAAK;QACd,IAAI;UACF;UACA,IAAI,OAAO4R,SAAS,KAAK,WAAW,EAAE;YACpChT,OAAO,CAACiC,KAAK,CAAC,4CAA4C,CAAC;YAC3D;;UAGF;UACA,IAAI,OAAO+Q,SAAS,CAACC,OAAO,KAAK,WAAW,EAAE;YAC5CjT,OAAO,CAACiC,KAAK,CAAC,oDAAoD,CAAC;YACnE;;UAGF;UACA,MAAMiR,gBAAgB,GAAG,EAAE,CAACC,KAAK,CAACC,IAAI,CAACjG,QAAQ,CAACkG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;UAC/FH,gBAAgB,CAAC/N,OAAO,CAAEmO,EAAO,IAAI;YACnC,IAAI;cACF;cACA,MAAMC,OAAO,GAAGP,SAAS,CAACC,OAAO,CAACO,WAAW,CAACF,EAAE,CAAC;cACjD,IAAIC,OAAO,EAAE;gBACXA,OAAO,CAACE,OAAO,EAAE;;aAEpB,CAAC,OAAOxR,KAAK,EAAE;cACdjC,OAAO,CAACgF,IAAI,CAAC,wDAAwD,EAAE/C,KAAK,CAAC;;UAEjF,CAAC,CAAC;UAEF;UACA,MAAMyR,kBAAkB,GAAG,EAAE,CAACP,KAAK,CAACC,IAAI,CAACjG,QAAQ,CAACkG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;UACjGrT,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyT,kBAAkB,CAAC7O,MAAM,EAAE,qBAAqB,CAAC;UAExF6O,kBAAkB,CAACvO,OAAO,CAAEwO,gBAAqB,IAAI;YACnD,IAAI;cACF,MAAMC,QAAQ,GAAGD,gBAAgB,CAACE,YAAY,CAAC,gBAAgB,CAAC;cAChE,MAAMC,OAAO,GAAGf,WAAW,CAACa,QAAQ,CAAC,IAAI,4BAA4B;cAErE;cACA,IAAIpO,KAAK,GAAG,EAAE;cACd,QAAQoO,QAAQ;gBACd,KAAK,wBAAwB;kBAC3BpO,KAAK,GAAG,yBAAyB;kBACjC;gBACF,KAAK,sBAAsB;kBACzBA,KAAK,GAAG,uCAAuC;kBAC/C;gBACF,KAAK,qBAAqB;kBACxBA,KAAK,GAAG,oCAAoC;kBAC5C;gBACF,KAAK,iBAAiB;kBACpBA,KAAK,GAAG,qBAAqB;kBAC7B;gBACF,KAAK,qBAAqB;kBACxBA,KAAK,GAAG,0BAA0B;kBAClC;gBACF,KAAK,aAAa;kBAChBA,KAAK,GAAG,gBAAgB;kBACxB;gBACF,KAAK,eAAe;kBAClBA,KAAK,GAAG,eAAe;kBACvB;gBACF,KAAK,mBAAmB;kBACtBA,KAAK,GAAG,sBAAsB;kBAC9B;gBACF,KAAK,sBAAsB;kBACzBA,KAAK,GAAG,oBAAoB;kBAC5B;gBACF,KAAK,mBAAmB;kBACtBA,KAAK,GAAG,eAAe;kBACvB;gBACF,KAAK,aAAa;kBAChBA,KAAK,GAAG,sBAAsB;kBAC9B;gBACF;kBACEA,KAAK,GAAG,UAAU;cAAC;cAGvB;cACA,IAAIwN,SAAS,CAACC,OAAO,CAACU,gBAAgB,EAAE;gBACtCI,SAAS,EAAE,MAAM;gBACjBC,OAAO,EAAE,OAAO;gBAChBC,SAAS,EAAE,KAAK;gBAChBzO,KAAK,EAAEA,KAAK;gBACZsO,OAAO,EAAEA,OAAO;gBAChBI,IAAI,EAAE;eACP,CAAC;aACH,CAAC,OAAOjS,KAAK,EAAE;cACdjC,OAAO,CAACiC,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;;UAE3E,CAAC,CAAC;UAEFjC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;SAC9D,CAAC,OAAOgC,KAAK,EAAE;UACdjC,OAAO,CAACiC,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;;MAE3E,CAAC,EAAE,GAAG,CAAC;KACR,CAAC,OAAOA,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;;EAE3E;EAIA;;;EAGA2Q,UAAU,CAACJ,IAAU;IACnB,OAAO,IAAI,CAACpT,QAAQ,CAAC+U,SAAS,CAAC3B,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE;EAC1D;EAEA;;;;;;EAMQhE,iBAAiB,CAAC4F,KAAY,EAAEvP,MAAc;IACpD,IAAI,CAACuP,KAAK,EAAE;MACV,OAAO,IAAIlK,KAAK,CAACrF,MAAM,CAAC,CAACkK,IAAI,CAAC,CAAC,CAAC;;IAGlC,IAAIqF,KAAK,CAACvP,MAAM,KAAKA,MAAM,EAAE;MAC3B,OAAOuP,KAAK;;IAGd,IAAIA,KAAK,CAACvP,MAAM,GAAGA,MAAM,EAAE;MACzB,OAAOuP,KAAK,CAACjB,KAAK,CAAC,CAAC,EAAEtO,MAAM,CAAC;;IAG/B;IACA,MAAMwP,MAAM,GAAG,CAAC,GAAGD,KAAK,CAAC;IACzB,OAAOC,MAAM,CAACxP,MAAM,GAAGA,MAAM,EAAE;MAC7BwP,MAAM,CAAClS,IAAI,CAAC,CAAC,CAAC;;IAGhB,OAAOkS,MAAM;EACf;EAEA;;;EAGQnF,cAAc,CAACvE,QAAe,EAAEpF,KAAW;IACjD,IAAIkL,YAAY,GAAG,KAAK;IACxB,IAAIG,aAAa,GAAG,KAAK;IAEzBjG,QAAQ,CAACxF,OAAO,CAAC,CAAC0F,OAAO,EAAElG,KAAK,KAAI;MAClC;MACA,MAAM2P,SAAS,GAAG/O,KAAK,GAAGA,KAAK,CAACyF,gBAAgB,CAACrG,KAAK,CAAC,GAAG,CAACkG,OAAO,CAACO,MAAM;MAEzE,IAAIkJ,SAAS,EAAE;QACb,IAAIzJ,OAAO,CAACmE,OAAO,KAAK,IAAI,EAAE;UAC5B;UACA4B,aAAa,GAAG,IAAI;SACrB,MAAM;UACL;UACAH,YAAY,GAAG,IAAI;;;IAGzB,CAAC,CAAC;IAEF,OAAO;MAAEA,YAAY;MAAEG;IAAa,CAAE;EACxC;EAEA;;;EAGQP,oBAAoB,CAAC9K,KAAU;IACrC,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACvE,IAAI,IAAI,CAACuE,KAAK,CAACvE,IAAI,CAAC2J,QAAQ,EAAE;MACjD;;IAGF,MAAMsE,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC3J,KAAK,CAACvE,IAAI,CAAC2J,QAAQ,EAAEpF,KAAK,CAAC;IAEnE;IACA,IAAIA,KAAK,CAACrC,OAAO,IAAIqC,KAAK,CAACrC,OAAO,CAACoN,MAAM,EAAE;MACzC,IAAI/K,KAAK,CAACrC,OAAO,CAACoN,MAAM,CAACE,CAAC,EAAE;QAC1BjL,KAAK,CAACrC,OAAO,CAACoN,MAAM,CAACE,CAAC,CAAClB,OAAO,GAAGL,WAAW,CAACwB,YAAY;QACzDlL,KAAK,CAACrC,OAAO,CAACoN,MAAM,CAACE,CAAC,CAAChL,KAAK,CAAC8J,OAAO,GAAGL,WAAW,CAACwB,YAAY;;MAGjE,IAAIlL,KAAK,CAACrC,OAAO,CAACoN,MAAM,CAACK,EAAE,EAAE;QAC3BpL,KAAK,CAACrC,OAAO,CAACoN,MAAM,CAACK,EAAE,CAACrB,OAAO,GAAGL,WAAW,CAAC2B,aAAa;QAC3DrL,KAAK,CAACrC,OAAO,CAACoN,MAAM,CAACK,EAAE,CAACnL,KAAK,CAAC8J,OAAO,GAAGL,WAAW,CAAC2B,aAAa;QACjE;QACArL,KAAK,CAACrC,OAAO,CAACoN,MAAM,CAACK,EAAE,CAACE,IAAI,CAACC,eAAe,GAAG,CAAC7B,WAAW,CAACwB,YAAY;;;IAI5E;IACAlL,KAAK,CAACiM,MAAM,EAAE;EAChB;;;uBA16HWxS,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAuV;MAAAC;QAAA;;;;;;;;;;;;;;;;;;;UCxBjC1V,8BAAuB;UAEfA,8BAAS;UAAAA,iBAAK;UAIpBA,6CAGkC;UAAhCA;YAAA,OAAayO,qBAAiB;UAAA,EAAC;UACjCzO,iBAAqB;UAGrBA,qEAGM;UAGNA,qEA+IM;UAGNA,qEAyCM;UAGNA,qEAyCM;UAGNA,qEAwKM;UAGNA,uEAiFM;UACRA,iBAAM;;;UAnfFA,eAAa;UAAbA,+BAAa;UAMTA,eAAW;UAAXA,gCAAW;UAMXA,eAAgC;UAAhCA,qDAAgC;UAkJhCA,eAA0C;UAA1CA,+DAA0C;UA4C1CA,eAAyC;UAAzCA,8DAAyC;UA4CzCA,eAA6B;UAA7BA,kDAA6B;UA2K7BA,eAAmC;UAAnCA,wDAAmC", "names": ["Chart", "registerables", "min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i0", "register", "PerformanceComponent", "constructor", "performanceService", "instanceService", "datePipe", "chartModalService", "changeDetectorRef", "tableSortingService", "textUtils", "id", "label", "period", "instanceIds", "className", "methodName", "ngOnInit", "console", "log", "initE<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadSortingState", "lastActiveTab", "loadActiveTab", "activeTab", "aggregatedFilterData", "instanceId", "undefined", "fromDate", "toDate", "methodsDisplayType", "getAll", "subscribe", "next", "data", "instances", "in<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "loadLastFilter", "setTimeout", "loading", "String", "Date", "setDate", "getDate", "Number", "subscription", "getAggregatedPerformance", "aggregatedPerformanceData", "aggregatedPerformanceChartRef", "renderAggregatedPerformanceChart", "loadSlowestMethods", "error", "subscriptions", "push", "ngAfterViewInit", "initPopovers", "initDefaultMetricsVisibility", "changeTab", "localStorage", "getItem", "aggregatedVisibility", "setItem", "JSON", "stringify", "methodDetailVisibility", "periodField", "name", "type", "options", "value", "fromDateField", "visible", "values", "required", "errorMessage", "toDateField", "aggregatedFilterFields", "instancesComparison<PERSON><PERSON><PERSON><PERSON><PERSON>s", "validators", "versionsComparisonFilter<PERSON>ields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "methodDetailFilter<PERSON><PERSON>s", "instanceOptions", "sort", "a", "b", "abbrevCompare", "customerAbbreviation", "localeCompare", "map", "instance", "updateFilterField", "filterFields", "index", "newField", "length", "original<PERSON>ield", "loadInstances", "warn", "onPeriodChange", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "openFullscreenChart", "chart", "title", "HTMLCanvasElement", "chartInstance", "<PERSON><PERSON><PERSON>", "openChartModal", "tab", "detectChanges", "tabId", "saveActiveTab", "<PERSON><PERSON><PERSON><PERSON>", "tabExists", "tabs", "some", "t", "requestAnimationFrame", "loadAggregatedPerformance", "instancesComparisonData", "instancesComparisonChartRef", "renderInstancesComparisonChart", "loadInstancesComparison", "versionsComparisonData", "versionsComparisonChartRef", "renderVersionsComparisonChart", "loadVersionsComparison", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadMethodsByType", "hasMethodDetailChartData", "methodDetailFilterData", "saveFilter", "loadMethodDetail", "entityType", "lastFilter", "parse", "currentAggregatedFilterStr", "newAggregatedFilterStr", "currentInstancesComparisonFilterStr", "instancesComparisonFilterData", "newInstancesComparisonFilterStr", "currentVersionsComparisonFilterStr", "versionsComparisonFilterData", "newVersionsComparisonFilterStr", "currentMethodsFilterStr", "newMethodsFilterStr", "currentMethodDetailFilterStr", "newMethodDetailFilterStr", "today", "labels", "dailyData", "message", "status", "complete", "onAggregatedFilterChange", "filterData", "currentFilterStr", "newFilterStr", "getInstancesComparison", "onInstancesComparisonFilterChange", "getVersionsComparison", "onVersionsComparisonFilterChange", "loadMostCalledMethods", "mostCalledMethods", "getMostCalledMethods", "applySortToMethods", "slowestMethods", "getSlowestMethods", "loadAllMethods", "getAllMethods", "allMethods", "finalInstanceId", "getMethodDetail", "methodDetailData", "methodDetailChartData", "hasData", "Array", "isArray", "renderMethodDetailChart", "onMethodsFilterChange", "onMethodDetailFilterChange", "showMethodDetail", "method", "saveMetricsVisibility", "chartType", "datasets", "visibility", "dataset", "aggregatedPerformanceChart", "indexOf", "isDatasetVisible", "instancesComparison<PERSON>hart", "versionsComparisonChart", "methodDetailChart", "hidden", "onSort", "column", "newSortingState", "createNewSortingStateWithDefaults", "sortColumn", "sortDirection", "direction", "tableName", "saveSortingState", "sortMethods", "methods", "valueA", "valueB", "instanceName", "toLowerCase", "totalCount", "min", "max", "avg", "median", "percentil95", "defaultSort", "savedSortingState", "loadMetricsVisibility", "visibilityJson", "renderingAggregatedChart", "chartData", "prepareAggregatedPerformanceChartData", "hasAggregatedChartData", "destroy", "document", "body", "contains", "nativeElement", "ctx", "getContext", "labelsLength", "avgDataLength", "avgData", "median<PERSON><PERSON><PERSON><PERSON><PERSON>", "medianData", "p95<PERSON><PERSON><PERSON>ength", "p95Data", "p99<PERSON><PERSON><PERSON>ength", "p99Data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minData", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxData", "totalCountDataLength", "totalCountData", "adjust<PERSON>rray<PERSON><PERSON><PERSON>", "width", "height", "borderColor", "backgroundColor", "borderWidth", "tension", "fill", "yAxisID", "visibleAxes", "getVisibleAxes", "responsive", "maintainAspectRatio", "plugins", "display", "text", "tooltip", "mode", "intersect", "legend", "position", "boxWidth", "onClick", "_e", "legendItem", "datasetIndex", "ci", "hide", "show", "updateAxisVisibility", "scales", "x", "y", "showLeftAxis", "beginAtZero", "y1", "showRightAxis", "grid", "drawOnChartArea", "renderingInstancesComparisonChart", "hasInstancesComparisonChartData", "originalLabels", "truncatedLabelsConfig", "createTruncatedLabelsConfig", "enabled", "max<PERSON><PERSON><PERSON>", "showFullInTooltip", "baseOptions", "update", "finalOptions", "mergeChartConfig", "renderingVersionsComparisonChart", "hasVersionsComparisonChartData", "renderingMethodDetailChart", "methodDetailChartRef", "baseConfig", "truncatedConfig", "Object", "keys", "merged", "ticks", "callbacks", "key", "dateA", "date", "getTime", "dateB", "item", "formatDate", "filter", "percentil99", "helpContent", "bootstrap", "Popover", "existingPopovers", "slice", "call", "querySelectorAll", "el", "popover", "getInstance", "dispose", "popoverTriggerList", "popoverTriggerEl", "helpType", "getAttribute", "content", "container", "trigger", "placement", "html", "transform", "array", "result", "isVisible", "selectors", "viewQuery"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\performance\\performance.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\performance\\performance.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport { PerformanceService } from '../services/performance.service';\nimport { InstanceService } from '../services/instance.service';\nimport { ChartModalService } from '../services/chart-modal.service';\nimport { Chart, registerables } from 'chart.js';\nimport { DatePipe } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { PerformanceFilterData } from './performance-filter/performance-filter.component';\nimport { FilterField, minArrayLength } from '../shared/advanced-filter/advanced-filter.component';\nimport { TabItem } from '../shared/tab-navigation/tab-navigation.component';\nimport { TableSortingService } from '../services/table-sorting.service';\nimport { TextUtilsService } from '../services/text-utils.service';\n\n// Deklarace globálního objektu bootstrap pro TypeScript\ndeclare const bootstrap: any;\n\n// Registrace všech komponent Chart.js\nChart.register(...registerables);\n\n@Component({\n  selector: 'app-performance',\n  templateUrl: './performance.component.html',\n  styleUrls: ['./performance.component.css']\n})\nexport class PerformanceComponent implements OnInit, AfterViewInit, OnDestroy {\n  // Reference na canvas elementy pro grafy\n  @ViewChild('aggregatedPerformanceChart') aggregatedPerformanceChartRef!: ElementRef;\n  @ViewChild('instancesComparisonChart') instancesComparisonChartRef!: ElementRef;\n  @ViewChild('versionsComparisonChart') versionsComparisonChartRef!: ElementRef;\n  @ViewChild('methodDetailChart') methodDetailChartRef!: ElementRef;\n\n  // Grafy\n  aggregatedPerformanceChart: Chart | null = null;\n  instancesComparisonChart: Chart | null = null;\n  versionsComparisonChart: Chart | null = null;\n  methodDetailChart: Chart | null = null;\n\n  // Stav vykreslování grafů\n  renderingAggregatedChart: boolean = false;\n  renderingInstancesComparisonChart: boolean = false;\n  renderingVersionsComparisonChart: boolean = false;\n  renderingMethodDetailChart: boolean = false;\n\n  // Indikátory, zda jsou k dispozici data pro grafy\n  hasAggregatedChartData: boolean = true;\n  hasInstancesComparisonChartData: boolean = true;\n  hasVersionsComparisonChartData: boolean = true;\n  hasMethodDetailChartData: boolean = true;\n\n  // Data\n  slowestMethods: any[] = [];\n  mostCalledMethods: any[] = [];\n  allMethods: any[] = [];\n  selectedMethod: any = null;\n  methodDetailData: any = null;\n  instances: any[] = [];\n  aggregatedPerformanceData: any = null;\n  instancesComparisonData: any = null;\n  versionsComparisonData: any = null;\n  methodDetailChartData: any = null;\n\n  // Typ metod pro zobrazení v záložce \"Metody\"\n  methodsDisplayType: string = 'slowest';\n\n  // Řazení\n  sortColumn: string = 'percentil95';\n  sortDirection: 'asc' | 'desc' = 'desc';\n\n  // Pole pro filtry\n  aggregatedFilterFields: FilterField[] = [];\n  instancesComparisonFilterFields: FilterField[] = [];\n  versionsComparisonFilterFields: FilterField[] = [];\n  methodsFilterFields: FilterField[] = [];\n  methodDetailFilterFields: FilterField[] = [];\n\n  // Stav komponenty\n  loading = true;\n  error: string | null = null;\n  activeTab = 'aggregated';\n  subscriptions: Subscription[] = [];\n\n  // Definice záložek\n  tabs: TabItem[] = [\n    { id: 'aggregated', label: 'Agregovaný výkon' },\n    { id: 'instances-comparison', label: 'Srovnání instancí' },\n    { id: 'versions-comparison', label: 'Srovnání verzí' },\n    { id: 'methods', label: 'Metody' },\n    { id: 'method-detail', label: 'Detail metody' }\n  ];\n\n  // Filtry\n  aggregatedFilterData: PerformanceFilterData = {\n    period: 7\n  };\n\n  instancesComparisonFilterData: PerformanceFilterData = {\n    instanceIds: [],\n    period: 7,\n    className: '',\n    methodName: ''\n  };\n\n  versionsComparisonFilterData: PerformanceFilterData = {\n    period: 90,\n    className: '',\n    methodName: ''\n  };\n\n  methodDetailFilterData: PerformanceFilterData = {\n    period: 30,\n    className: '',\n    methodName: ''\n  };\n\n  constructor(\n    private performanceService: PerformanceService,\n    private instanceService: InstanceService,\n    private datePipe: DatePipe,\n    private chartModalService: ChartModalService,\n    private changeDetectorRef: ChangeDetectorRef,\n    private tableSortingService: TableSortingService,\n    private textUtils: TextUtilsService\n  ) { }\n\n  ngOnInit(): void {\n    console.log('PerformanceComponent - ngOnInit');\n    // Nejprve inicializujeme prázdná pole filtrů, aby se komponenta advanced-filter správně inicializovala\n    this.initEmptyFilterFields();\n\n    // Načtení uloženého stavu třídění\n    this.loadSortingState();\n\n    // Načtení poslední aktivní záložky z localStorage\n    const lastActiveTab = this.loadActiveTab();\n    if (lastActiveTab) {\n      console.log(`ngOnInit - Načtena poslední aktivní záložka: ${lastActiveTab}`);\n      this.activeTab = lastActiveTab;\n    }\n\n    // Inicializace výchozích hodnot filtru\n    this.aggregatedFilterData = {\n      instanceId: undefined,\n      period: '7',\n      fromDate: undefined,\n      toDate: undefined,\n      methodsDisplayType: 'slowest'\n    };\n\n    // Načtení instancí - toto musí proběhnout jako první\n    this.instanceService.getAll().subscribe({\n      next: (data: any) => {\n        console.log('ngOnInit - Instance úspěšně načteny:', data);\n        this.instances = data;\n\n        // Po načtení instancí inicializujeme pole filtrů\n        this.initFilterFields();\n\n        // Načtení posledního filtru pro aktuální záložku\n        this.loadLastFilter('performance_aggregated');\n\n        // Přímé volání načtení dat až po načtení instancí\n        console.log('ngOnInit - Volám API pro načtení dat po načtení instancí');\n\n        // Přímé volání API s použitím uloženého filtru\n        setTimeout(() => {\n          console.log('ngOnInit - Volám API přímo s použitím uloženého filtru:', this.aggregatedFilterData);\n          this.loading = true;\n\n          // Příprava parametrů pro volání API\n          let instanceId = this.aggregatedFilterData.instanceId;\n          if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n            instanceId = undefined;\n          }\n\n          let fromDate: Date;\n          let toDate: Date = new Date();\n\n          // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n          if (!this.aggregatedFilterData.period) {\n            fromDate = new Date();\n            fromDate.setDate(toDate.getDate() - 7); // Výchozí hodnota 7 dní\n          }\n          // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n          else if (this.aggregatedFilterData.period !== 'custom') {\n            fromDate = new Date();\n            fromDate.setDate(toDate.getDate() - Number(this.aggregatedFilterData.period));\n          }\n          // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n          else {\n            // Kontrola, zda fromDate a toDate jsou validní\n            if (this.aggregatedFilterData.fromDate) {\n              fromDate = new Date(this.aggregatedFilterData.fromDate);\n            } else {\n              // Výchozí hodnota, pokud fromDate není zadáno\n              fromDate = new Date();\n              fromDate.setDate(toDate.getDate() - 7);\n            }\n\n            if (this.aggregatedFilterData.toDate) {\n              toDate = new Date(this.aggregatedFilterData.toDate);\n            }\n          }\n\n          console.log('ngOnInit - Parametry pro volání API:', { instanceId, fromDate, toDate });\n\n          const subscription = this.performanceService.getAggregatedPerformance(instanceId, fromDate, toDate).subscribe({\n            next: (data: any) => {\n              console.log('ngOnInit - Data úspěšně načtena přímo:', data);\n\n              // Uložení dat pro pozdější vykreslení\n              this.aggregatedPerformanceData = data;\n\n              // Vykreslení grafu\n              if (this.aggregatedPerformanceChartRef) {\n                this.renderAggregatedPerformanceChart(data);\n              }\n\n              this.loading = false;\n\n              // Načtení dat pro záložku \"Metody\"\n              setTimeout(() => {\n                this.loadSlowestMethods();\n              }, 100);\n            },\n            error: (error: any) => {\n              console.error('ngOnInit - Chyba při přímém načítání dat:', error);\n              this.loading = false;\n            }\n          });\n\n          this.subscriptions.push(subscription);\n        }, 500);\n      },\n      error: (error: any) => {\n        console.error('ngOnInit - Chyba při načítání instancí:', error);\n      }\n    });\n  }\n\n  ngAfterViewInit(): void {\n    console.log('PerformanceComponent - ngAfterViewInit');\n    // Inicializace popoverů pro nápovědu\n    this.initPopovers();\n\n    // Inicializace výchozího nastavení viditelnosti metrik\n    this.initDefaultMetricsVisibility();\n\n    // Načtení dat pro aktivní záložku\n    setTimeout(() => {\n      // Simulujeme změnu záložky, aby se načetla data pro aktivní záložku\n      this.changeTab(this.activeTab);\n    }, 500);\n\n    // Pokud již máme data, zkusíme vykreslit graf\n    if (this.activeTab === 'aggregated' && this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n      console.log('PerformanceComponent - Mám data, zkusím vykreslit graf');\n      setTimeout(() => {\n        this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n      }, 500);\n    } else {\n      console.log('PerformanceComponent - Nemám data nebo canvas element, zkusím později');\n\n      // Přidáme event listener pro změnu záložky, abychom mohli vykreslit graf, až bude canvas element k dispozici\n      setTimeout(() => {\n        if (this.activeTab === 'aggregated' && this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n          console.log('PerformanceComponent - Mám data a canvas element, zkusím vykreslit graf po timeoutu');\n          this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n        }\n      }, 2000);\n    }\n  }\n\n  /**\n   * Inicializace výchozího nastavení viditelnosti metrik\n   */\n  private initDefaultMetricsVisibility(): void {\n    // Výchozí nastavení viditelnosti metrik pro agregovaný výkon\n    if (!localStorage.getItem('metrics_visibility_aggregated')) {\n      const aggregatedVisibility: Record<string, boolean> = {\n        'Průměr (ms)': true,\n        'Medián (ms)': true,\n        '95. percentil (ms)': true,\n        '99. percentil (ms)': false,\n        'Minimum (ms)': false,\n        'Maximum (ms)': false,\n        'Počet volání': false\n      };\n      localStorage.setItem('metrics_visibility_aggregated', JSON.stringify(aggregatedVisibility));\n    }\n\n    // Výchozí nastavení viditelnosti metrik pro detail metody\n    if (!localStorage.getItem('metrics_visibility_method-detail')) {\n      const methodDetailVisibility: Record<string, boolean> = {\n        'Průměr (ms)': true,\n        'Medián (ms)': true,\n        '95. percentil (ms)': true,\n        'Počet volání': false\n      };\n      localStorage.setItem('metrics_visibility_method-detail', JSON.stringify(methodDetailVisibility));\n    }\n\n    // Pro srovnání instancí a verzí nemůžeme nastavit výchozí hodnoty,\n    // protože názvy datasetů závisí na datech z API\n  }\n\n  /**\n   * Inicializace prázdných polí filtrů\n   */\n  initEmptyFilterFields(): void {\n    // Základní pole pro všechny filtry\n    const periodField: FilterField = {\n      name: 'period',\n      label: 'Časové období',\n      type: 'select',\n      options: [\n        { value: 1, label: '1 den' },\n        { value: 7, label: '7 dní' },\n        { value: 30, label: '30 dní' },\n        { value: 90, label: '90 dní' },\n        { value: 'custom', label: 'Vlastní období' }\n      ]\n    };\n\n    // Definice polí pro datum od a do\n    const fromDateField: FilterField = {\n      name: 'fromDate',\n      label: 'Od',\n      type: 'date',\n      visible: (values) => values.period === 'custom', // Zobrazit pouze při vlastním období\n      required: (values) => values.period === 'custom', // Povinné pouze při vlastním období\n      errorMessage: 'Zadejte počáteční datum'\n    };\n\n    const toDateField: FilterField = {\n      name: 'toDate',\n      label: 'Do',\n      type: 'date',\n      visible: (values) => values.period === 'custom', // Zobrazit pouze při vlastním období\n      required: (values) => values.period === 'custom', // Povinné pouze při vlastním období\n      errorMessage: 'Zadejte koncové datum'\n    };\n\n    // Inicializace prázdných polí filtrů\n    this.aggregatedFilterFields = [\n      {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [\n          { value: null, label: 'Všechny instance' }\n        ]\n      },\n      periodField,\n      fromDateField,\n      toDateField\n    ];\n\n    this.instancesComparisonFilterFields = [\n      {\n        name: 'instanceIds',\n        label: 'Instance',\n        type: 'multiselect',\n        options: [],\n        required: true,\n        validators: [minArrayLength(2)],\n        errorMessage: 'Vyberte alespoň dvě instance pro srovnání'\n      },\n      {\n        name: 'className',\n        label: 'Třída',\n        type: 'text'\n      },\n      {\n        name: 'methodName',\n        label: 'Metoda',\n        type: 'text'\n      },\n      periodField,\n      fromDateField,\n      toDateField\n    ];\n\n    this.versionsComparisonFilterFields = [\n      {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [\n          { value: null, label: 'Vyberte instanci' }\n        ],\n        required: true,\n        errorMessage: 'Vyberte instanci pro srovnání verzí'\n      },\n      {\n        name: 'className',\n        label: 'Třída',\n        type: 'text'\n      },\n      {\n        name: 'methodName',\n        label: 'Metoda',\n        type: 'text'\n      },\n      periodField,\n      fromDateField,\n      toDateField\n    ];\n\n    this.methodsFilterFields = [\n      {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [\n          { value: null, label: 'Všechny instance' }\n        ]\n      },\n      {\n        name: 'methodsDisplayType',\n        label: 'Metrika',\n        type: 'select',\n        options: [\n          { value: 'slowest', label: 'Nejpomalejší metody' },\n          { value: 'most-called', label: 'Nejčastěji volané metody' },\n          { value: 'all', label: 'Všechny metody' }\n        ]\n      },\n      periodField,\n      fromDateField,\n      toDateField\n    ];\n\n    this.methodDetailFilterFields = [\n      {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [\n          { value: 0, label: 'Všechny instance' }\n        ],\n        required: false\n      },\n      {\n        name: 'className',\n        label: 'Třída',\n        type: 'text',\n        required: true,\n        errorMessage: 'Zadejte název třídy'\n      },\n      {\n        name: 'methodName',\n        label: 'Metoda',\n        type: 'text',\n        required: true,\n        errorMessage: 'Zadejte název metody'\n      },\n      periodField,\n      fromDateField,\n      toDateField\n    ];\n  }\n\n  /**\n   * Inicializace polí filtrů\n   */\n  initFilterFields(): void {\n    // Základní pole pro všechny filtry\n    const periodField: FilterField = {\n      name: 'period',\n      label: 'Časové období',\n      type: 'select',\n      options: [\n        { value: 1, label: '1 den' },\n        { value: 7, label: '7 dní' },\n        { value: 30, label: '30 dní' },\n        { value: 90, label: '90 dní' },\n        { value: 'custom', label: 'Vlastní období' }\n      ]\n    };\n\n    // Aktualizace pole pro filtr agregovaných metrik\n    const instanceOptions = [\n      { value: null, label: 'Všechny instance' },\n      ...this.instances\n        .sort((a, b) => {\n          // Nejprve seřadíme podle zkratky zákazníka\n          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n          // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n        })\n        .map(instance => ({\n          value: instance.id,\n          label: `${instance.customerAbbreviation} - ${instance.name}`\n        }))\n    ];\n\n    // Aktualizace pole pro filtr agregovaných metrik\n    this.updateFilterField(this.aggregatedFilterFields, 0, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: instanceOptions\n    });\n\n    // Aktualizace pole pro filtr srovnání instancí\n    this.updateFilterField(this.instancesComparisonFilterFields, 0, {\n      name: 'instanceIds',\n      label: 'Instance',\n      type: 'multiselect',\n      options: this.instances\n        .sort((a, b) => {\n          // Nejprve seřadíme podle zkratky zákazníka\n          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n          // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n        })\n        .map(instance => ({\n          value: instance.id,\n          label: `${instance.customerAbbreviation} - ${instance.name}`\n        })),\n      required: true,\n      validators: [minArrayLength(2)],\n      errorMessage: 'Vyberte alespoň dvě instance pro srovnání'\n    });\n\n    // Aktualizace pole pro filtr srovnání verzí\n    this.updateFilterField(this.versionsComparisonFilterFields, 0, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [\n        { value: null, label: 'Vyberte instanci' },\n        ...this.instances\n          .sort((a, b) => {\n            // Nejprve seřadíme podle zkratky zákazníka\n            const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n            // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n            return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n          })\n          .map(instance => ({\n            value: instance.id,\n            label: `${instance.customerAbbreviation} - ${instance.name}`\n          }))\n      ],\n      required: true,\n      errorMessage: 'Vyberte instanci pro srovnání verzí'\n    });\n\n    // Aktualizace pole pro filtr metod\n    this.updateFilterField(this.methodsFilterFields, 0, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: instanceOptions\n    });\n\n    // Aktualizace pole pro filtr detailu metody\n    this.updateFilterField(this.methodDetailFilterFields, 0, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [\n        { value: 0, label: 'Všechny instance' },\n        ...this.instances\n          .sort((a, b) => {\n            // Nejprve seřadíme podle zkratky zákazníka\n            const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n            // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n            return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n          })\n          .map(instance => ({\n            value: instance.id,\n            label: `${instance.customerAbbreviation} - ${instance.name}`\n          }))\n      ]\n    });\n\n    // Aktualizace pole period pro všechny filtry\n    this.updateFilterField(this.aggregatedFilterFields, 1, periodField);\n    this.updateFilterField(this.instancesComparisonFilterFields, 3, periodField);\n    this.updateFilterField(this.versionsComparisonFilterFields, 3, periodField);\n    this.updateFilterField(this.methodsFilterFields, 2, periodField); // Index 2, protože na indexu 1 je methodsDisplayType\n    this.updateFilterField(this.methodDetailFilterFields, 3, periodField);\n  }\n\n  /**\n   * Aktualizace pole filtru\n   */\n  private updateFilterField(filterFields: FilterField[], index: number, newField: FilterField): void {\n    if (filterFields && filterFields.length > index) {\n      // Zachováme validátory a další vlastnosti z původního pole\n      const originalField = filterFields[index];\n      filterFields[index] = {\n        ...originalField,\n        ...newField\n      };\n\n      console.log(`Aktualizace pole ${newField.name}:`, filterFields[index]);\n    }\n  }\n\n  /**\n   * Načtení seznamu instancí\n   */\n  loadInstances(): void {\n    console.log('loadInstances - Načítám seznam instancí');\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadInstances - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    this.loading = true;\n\n    this.subscriptions.push(\n      this.instanceService.getAll().subscribe({\n        next: (data: any) => {\n          console.log('loadInstances - Data úspěšně načtena:', data);\n          this.instances = data;\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n\n          // Načtení posledního filtru pro aktuální záložku\n          this.loadLastFilter('performance_aggregated');\n\n          // Již nebudeme načítat data zde, protože to děláme přímo v ngOnInit\n          console.log('loadInstances - Data instancí načtena, filtry inicializovány');\n\n          this.loading = false;\n        },\n        error: (error: any) => {\n          console.error('Chyba při načítání instancí:', error);\n          this.loading = false;\n        }\n      })\n    );\n  }\n\n  /**\n   * Změna časového období\n   */\n  onPeriodChange(): void {\n    // Tato metoda je volána při změně období v select boxu\n    // Není potřeba implementovat žádnou logiku, protože\n    // zobrazení/skrytí polí od-do je řešeno pomocí *ngIf v šabloně\n  }\n\n\n\n  ngOnDestroy(): void {\n    // Zrušení všech aktivních subscriptions\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Otevře modální okno s grafem v režimu \"full screen\"\n   * @param chart Instance grafu nebo reference na canvas element\n   * @param title Titulek grafu\n   */\n  openFullscreenChart(chart: Chart | HTMLCanvasElement | null, title: string): void {\n    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart\n    if (chart instanceof HTMLCanvasElement) {\n      // Najdeme instanci Chart pro daný canvas element\n      const chartInstance = Chart.getChart(chart);\n      this.chartModalService.openChartModal(chartInstance || null, title);\n    } else {\n      this.chartModalService.openChartModal(chart, title);\n    }\n  }\n\n  /**\n   * Změna aktivní záložky\n   */\n  changeTab(tab: any): void {\n    // Resetujeme chybovou hlášku při změně záložky\n    this.error = '';\n    // Spustíme detekci změn, aby se změna proměnné error projevila okamžitě\n    this.changeDetectorRef.detectChanges();\n\n    // Převod parametru na string, pokud není\n    const tabId = typeof tab === 'string' ? tab : String(tab);\n    this.activeTab = tabId;\n\n    // Uložení aktivní záložky do localStorage\n    this.saveActiveTab(tabId);\n\n    // Pokud je vybrána záložka \"method-detail\", přidáme ji do seznamu záložek\n    if (tabId === 'method-detail' && this.selectedMethod) {\n      // Kontrola, zda záložka již existuje\n      const tabExists = this.tabs.some(t => t.id === 'method-detail');\n      if (!tabExists) {\n        this.tabs.push({ id: 'method-detail', label: 'Detail metody' });\n      }\n    }\n\n    // Načtení dat pro vybranou záložku\n    switch (tabId) {\n      case 'aggregated':\n        // Načtení posledního filtru z localStorage\n        this.loadLastFilter('performance_aggregated');\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku aggregated');\n\n            // Pokud již máme data, zkusíme vykreslit graf\n            if (this.aggregatedPerformanceData && this.aggregatedPerformanceChartRef) {\n              console.log('onTabChange - Mám data, zkusím vykreslit graf');\n              this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData);\n            } else {\n              // Jinak načteme nová data\n              this.loadAggregatedPerformance();\n            }\n          }, 500);\n        });\n        break;\n      case 'instances-comparison':\n        // Načtení posledního filtru z localStorage\n        this.loadLastFilter('performance_instances_comparison');\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku instances-comparison');\n\n            // Pokud již máme data, zkusíme vykreslit graf\n            if (this.instancesComparisonData && this.instancesComparisonChartRef) {\n              console.log('onTabChange - Mám data, zkusím vykreslit graf');\n              this.renderInstancesComparisonChart(this.instancesComparisonData);\n            } else {\n              // Jinak načteme nová data\n              this.loadInstancesComparison();\n            }\n          }, 500);\n        });\n        break;\n      case 'versions-comparison':\n        // Načtení posledního filtru z localStorage\n        this.loadLastFilter('performance_versions_comparison');\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku versions-comparison');\n\n            // Pokud již máme data, zkusíme vykreslit graf\n            if (this.versionsComparisonData && this.versionsComparisonChartRef) {\n              console.log('onTabChange - Mám data, zkusím vykreslit graf');\n              this.renderVersionsComparisonChart(this.versionsComparisonData);\n            } else {\n              // Jinak načteme nová data\n              this.loadVersionsComparison();\n            }\n          }, 500);\n        });\n        break;\n      case 'methods':\n        // Načtení posledního filtru z localStorage\n        const lastFilterKey = `last_filter_performance_methods`;\n        const lastFilterJson = localStorage.getItem(lastFilterKey);\n\n        // Pokud není uložen žádný filtr, resetujeme instanceId na undefined\n        if (!lastFilterJson) {\n          console.log('onTabChange - Není uložen žádný filtr pro záložku methods, resetuji instanceId na undefined');\n          this.aggregatedFilterData.instanceId = undefined;\n        }\n\n        this.loadLastFilter('performance_methods');\n\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku methods, instanceId:', this.aggregatedFilterData.instanceId);\n            this.loadMethodsByType();\n          }, 100);\n        });\n        break;\n      case 'method-detail':\n        // Načtení posledního filtru z localStorage\n        this.loadLastFilter('performance_method_detail');\n        // Resetujeme indikátor, zda jsou k dispozici data pro graf\n        this.hasMethodDetailChartData = true;\n\n        // Pokud máme vybranou metodu, použijeme její hodnoty pro filtr\n        if (this.selectedMethod) {\n          console.log('onTabChange - Použití hodnot z vybrané metody pro filtr:', this.selectedMethod);\n\n          // Zjištění aktuální hodnoty instanceId\n          let instanceId = this.selectedMethod.instanceId;\n\n          // Pokud instanceId je null nebo undefined, použijeme 0 (všechny instance)\n          if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n            instanceId = 0;\n          }\n\n          // Aktualizace hodnot filtru\n          this.methodDetailFilterData = {\n            ...this.methodDetailFilterData,\n            instanceId: instanceId,\n            className: this.selectedMethod.className,\n            methodName: this.selectedMethod.methodName\n          };\n\n          // Uložení filtru do localStorage\n          this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n        }\n\n        // Použijeme requestAnimationFrame, aby se zajistilo, že DOM je plně vykreslen\n        requestAnimationFrame(() => {\n          // Ještě přidáme timeout pro jistotu\n          setTimeout(() => {\n            console.log('onTabChange - Načítám data pro záložku method-detail');\n\n            // Vždy načteme nová data při přepnutí na záložku\n            this.loadMethodDetail();\n          }, 500);\n        });\n        break;\n    }\n\n    // Reinicializace popoverů po změně záložky\n    setTimeout(() => {\n      this.initPopovers();\n    }, 500);\n  }\n\n  /**\n   * Načtení posledního filtru z localStorage\n   */\n  loadLastFilter(entityType: string): void {\n    console.log(`loadLastFilter - Načítám poslední filtr pro ${entityType}`);\n\n    try {\n      const lastFilterKey = `last_filter_${entityType}`;\n      const lastFilterJson = localStorage.getItem(lastFilterKey);\n\n      if (lastFilterJson) {\n        const lastFilter = JSON.parse(lastFilterJson);\n        console.log(`Načten poslední filtr pro ${entityType} z localStorage:`, lastFilter);\n\n        // Aktualizace příslušného filtru podle typu entity\n        switch (entityType) {\n          case 'performance_aggregated':\n            // Kontrola, zda se filtr skutečně změnil\n            const currentAggregatedFilterStr = JSON.stringify(this.aggregatedFilterData);\n            const newAggregatedFilterStr = JSON.stringify({ ...this.aggregatedFilterData, ...lastFilter });\n\n            if (currentAggregatedFilterStr !== newAggregatedFilterStr) {\n              this.aggregatedFilterData = { ...this.aggregatedFilterData, ...lastFilter };\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.aggregatedFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n          case 'performance_instances_comparison':\n            // Kontrola, zda se filtr skutečně změnil\n            const currentInstancesComparisonFilterStr = JSON.stringify(this.instancesComparisonFilterData);\n            const newInstancesComparisonFilterStr = JSON.stringify({ ...this.instancesComparisonFilterData, ...lastFilter });\n\n            if (currentInstancesComparisonFilterStr !== newInstancesComparisonFilterStr) {\n              this.instancesComparisonFilterData = { ...this.instancesComparisonFilterData, ...lastFilter };\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.instancesComparisonFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n          case 'performance_versions_comparison':\n            // Kontrola, zda se filtr skutečně změnil\n            const currentVersionsComparisonFilterStr = JSON.stringify(this.versionsComparisonFilterData);\n            const newVersionsComparisonFilterStr = JSON.stringify({ ...this.versionsComparisonFilterData, ...lastFilter });\n\n            if (currentVersionsComparisonFilterStr !== newVersionsComparisonFilterStr) {\n              this.versionsComparisonFilterData = { ...this.versionsComparisonFilterData, ...lastFilter };\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.versionsComparisonFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n          case 'performance_methods':\n            // Pro metody používáme stejný filtr jako pro agregované metriky\n            // Kontrola, zda se filtr skutečně změnil\n            const currentMethodsFilterStr = JSON.stringify(this.aggregatedFilterData);\n            const newMethodsFilterStr = JSON.stringify({ ...this.aggregatedFilterData, ...lastFilter });\n\n            if (currentMethodsFilterStr !== newMethodsFilterStr) {\n              this.aggregatedFilterData = { ...this.aggregatedFilterData, ...lastFilter };\n\n              // Aktualizace typu zobrazovaných metod\n              if (lastFilter.methodsDisplayType) {\n                this.methodsDisplayType = lastFilter.methodsDisplayType;\n              }\n\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.aggregatedFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n          case 'performance_method_detail':\n            // Kontrola, zda se filtr skutečně změnil\n            const currentMethodDetailFilterStr = JSON.stringify(this.methodDetailFilterData);\n            const newMethodDetailFilterStr = JSON.stringify({ ...this.methodDetailFilterData, ...lastFilter });\n\n            if (currentMethodDetailFilterStr !== newMethodDetailFilterStr) {\n              this.methodDetailFilterData = { ...this.methodDetailFilterData, ...lastFilter };\n              console.log(`Aktualizován filtr pro ${entityType}:`, this.methodDetailFilterData);\n            } else {\n              console.log(`Filtr pro ${entityType} se nezměnil, přeskakuji aktualizaci`);\n            }\n            break;\n        }\n      }\n    } catch (error) {\n      console.error(`Chyba při načítání posledního filtru pro ${entityType} z localStorage`, error);\n    }\n  }\n\n  /**\n   * Načtení agregovaných výkonnostních metrik\n   */\n  loadAggregatedPerformance(): void {\n    console.log('loadAggregatedPerformance - Začínám načítat data');\n\n    // Přidáme kontrolu, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadAggregatedPerformance - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.aggregatedPerformanceChartRef) {\n      console.warn('loadAggregatedPerformance - Reference na canvas element není k dispozici, ale pokračuji v načítání dat');\n    } else {\n      console.log('loadAggregatedPerformance - Reference na canvas element je k dispozici');\n    }\n\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadAggregatedPerformance - Instance nejsou načteny, načítám je');\n\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: (data: any) => {\n          console.log('loadAggregatedPerformance - Instance úspěšně načteny:', data);\n          this.instances = data;\n\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n\n          // Pokračujeme v načítání dat\n          this.loadAggregatedPerformance();\n        },\n        error: (error: any) => {\n          console.error('loadAggregatedPerformance - Chyba při načítání instancí:', error);\n          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n          this.loading = false;\n        }\n      });\n\n      return;\n    }\n\n    // Kontrola, zda máme inicializovaný filtr\n    if (!this.aggregatedFilterData) {\n      console.warn('loadAggregatedPerformance - Filtr není inicializován, inicializuji výchozí hodnoty');\n      this.aggregatedFilterData = {\n        instanceId: undefined,\n        period: '7',\n        fromDate: undefined,\n        toDate: undefined\n      };\n    }\n\n    // Příprava parametrů\n    // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme undefined\n    let instanceId = this.aggregatedFilterData.instanceId;\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n      instanceId = undefined;\n    }\n    console.log('loadAggregatedPerformance - instanceId:', instanceId);\n    let fromDate: Date | undefined;\n    let toDate: Date | undefined;\n\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.aggregatedFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.aggregatedFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.aggregatedFilterData.fromDate) {\n        fromDate = new Date(this.aggregatedFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n\n      if (this.aggregatedFilterData.toDate) {\n        toDate = new Date(this.aggregatedFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n\n    console.log('loadAggregatedPerformance - Volám API pro načtení dat', { instanceId, fromDate, toDate });\n\n    try {\n      const subscription = this.performanceService.getAggregatedPerformance(instanceId, fromDate, toDate).subscribe({\n        next: (data: any) => {\n          console.log('loadAggregatedPerformance - Data úspěšně načtena:', data);\n\n          if (!data) {\n            console.error('loadAggregatedPerformance - API vrátilo prázdná data');\n            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n            this.loading = false;\n            return;\n          }\n\n          // Kontrola, zda data obsahují potřebné údaje\n          if (!data.labels && !data.dailyData) {\n            console.warn('loadAggregatedPerformance - Data neobsahují labels ani dailyData, pokusím se je zpracovat');\n          }\n\n          // Uložení dat pro pozdější vykreslení\n          this.aggregatedPerformanceData = data;\n\n          // Vykreslení grafu\n          this.renderAggregatedPerformanceChart(data);\n          this.loading = false;\n        },\n        error: (error: any) => {\n          console.error('Chyba při načítání agregovaných výkonnostních metrik:', error);\n          this.error = 'Nepodařilo se načíst agregované výkonnostní metriky. ' +\n                      (error.message ? error.message : 'Zkuste to prosím znovu.');\n          this.loading = false;\n\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadAggregatedPerformance - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadAggregatedPerformance - Opakuji volání API');\n              this.loadAggregatedPerformance();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadAggregatedPerformance - Volání API dokončeno');\n        }\n      });\n\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadAggregatedPerformance - Chyba při volání API:', error);\n      this.error = 'Nepodařilo se načíst agregované výkonnostní metriky. Zkuste to prosím znovu.';\n      this.loading = false;\n    }\n\n    // Načtení nejpomalejších metod - pouze pokud je aktivní záložka 'aggregated'\n    if (this.activeTab === 'aggregated') {\n      // Použijeme setTimeout, abychom zajistili, že se volání API nepřekrývají\n      setTimeout(() => {\n        this.loadSlowestMethods();\n      }, 100);\n    }\n  }\n\n  /**\n   * Zpracování změny filtru pro agregované metriky\n   */\n  onAggregatedFilterChange(filterData: PerformanceFilterData): void {\n    console.log('onAggregatedFilterChange - Přijatá data filtru:', filterData);\n\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.aggregatedFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onAggregatedFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    }\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onAggregatedFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n\n    this.aggregatedFilterData = filterData;\n    console.log('onAggregatedFilterChange - Zpracovaná data filtru:', this.aggregatedFilterData);\n\n    // Použijeme setTimeout, abychom zajistili, že se volání API nepřekrývají\n    setTimeout(() => {\n      this.loadAggregatedPerformance();\n    }, 0);\n  }\n\n  /**\n   * Načtení srovnání výkonu metod mezi instancemi\n   */\n  loadInstancesComparison(): void {\n    console.log('loadInstancesComparison - Začínám načítat data');\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadInstancesComparison - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadInstancesComparison - Instance nejsou načteny, načítám je');\n\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: (data: any) => {\n          console.log('loadInstancesComparison - Instance úspěšně načteny:', data);\n          this.instances = data;\n\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n\n          // Pokračujeme v načítání dat\n          this.loadInstancesComparison();\n        },\n        error: (error: any) => {\n          console.error('loadInstancesComparison - Chyba při načítání instancí:', error);\n          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n          this.loading = false;\n        }\n      });\n\n      return;\n    }\n\n    // Kontrola, zda jsou vybrány alespoň dvě instance\n    if (!this.instancesComparisonFilterData.instanceIds || this.instancesComparisonFilterData.instanceIds.length < 2) {\n      // Necháme validaci na formuláři, nezobrazujeme chybový blok\n      this.loading = false;\n      return;\n    }\n\n    // Příprava parametrů\n    const instanceIds = this.instancesComparisonFilterData.instanceIds;\n    const methodName = this.instancesComparisonFilterData.methodName;\n    const className = this.instancesComparisonFilterData.className;\n    let fromDate: Date | undefined;\n    let toDate: Date | undefined;\n\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.instancesComparisonFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.instancesComparisonFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.instancesComparisonFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.instancesComparisonFilterData.fromDate) {\n        fromDate = new Date(this.instancesComparisonFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n\n      if (this.instancesComparisonFilterData.toDate) {\n        toDate = new Date(this.instancesComparisonFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n\n    try {\n      console.log('loadInstancesComparison - Volám API pro načtení dat', { instanceIds, methodName, className, fromDate, toDate });\n\n      const subscription = this.performanceService.getInstancesComparison(instanceIds, methodName, className, fromDate, toDate).subscribe({\n        next: (data: any) => {\n          console.log('loadInstancesComparison - Data úspěšně načtena:', data);\n\n          if (!data) {\n            console.error('loadInstancesComparison - API vrátilo prázdná data');\n            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n            this.loading = false;\n            return;\n          }\n\n          // Uložení dat pro pozdější vykreslení\n          this.instancesComparisonData = data;\n\n          // Vykreslení grafu\n          this.renderInstancesComparisonChart(data);\n          this.loading = false;\n        },\n        error: (error: any) => {\n          console.error('Chyba při načítání srovnání instancí:', error);\n          this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi instancemi. ' +\n                      (error.message ? error.message : 'Zkuste to prosím znovu.');\n          this.loading = false;\n\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadInstancesComparison - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadInstancesComparison - Opakuji volání API');\n              this.loadInstancesComparison();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadInstancesComparison - Volání API dokončeno');\n        }\n      });\n\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadInstancesComparison - Chyba při volání API:', error);\n      this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi instancemi. Zkuste to prosím znovu.';\n      this.loading = false;\n    }\n  }\n\n  /**\n   * Zpracování změny filtru pro srovnání instancí\n   */\n  onInstancesComparisonFilterChange(filterData: PerformanceFilterData): void {\n    console.log('onInstancesComparisonFilterChange - Přijatá data filtru:', filterData);\n\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.instancesComparisonFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onInstancesComparisonFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    }\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onInstancesComparisonFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n\n    this.instancesComparisonFilterData = filterData;\n    console.log('onInstancesComparisonFilterChange - Zpracovaná data filtru:', this.instancesComparisonFilterData);\n    this.loadInstancesComparison();\n  }\n\n  /**\n   * Načtení srovnání výkonu metod mezi verzemi\n   */\n  loadVersionsComparison(): void {\n    console.log('loadVersionsComparison - Začínám načítat data');\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadVersionsComparison - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadVersionsComparison - Instance nejsou načteny, načítám je');\n\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: (data: any) => {\n          console.log('loadVersionsComparison - Instance úspěšně načteny:', data);\n          this.instances = data;\n\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n\n          // Pokračujeme v načítání dat\n          this.loadVersionsComparison();\n        },\n        error: (error: any) => {\n          console.error('loadVersionsComparison - Chyba při načítání instancí:', error);\n          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n          this.loading = false;\n        }\n      });\n\n      return;\n    }\n\n    // Kontrola, zda je vybrána instance\n    if (!this.versionsComparisonFilterData.instanceId ||\n        this.versionsComparisonFilterData.instanceId === null ||\n        String(this.versionsComparisonFilterData.instanceId) === \"null\") {\n      console.warn('loadVersionsComparison - Není vybrána instance, přeskakuji načítání dat');\n      // Necháme validaci na formuláři, nezobrazujeme chybový blok\n      this.loading = false;\n      return;\n    }\n\n    // Příprava parametrů\n    const instanceId = this.versionsComparisonFilterData.instanceId;\n    const methodName = this.versionsComparisonFilterData.methodName;\n    const className = this.versionsComparisonFilterData.className;\n    let fromDate: Date | undefined;\n    let toDate: Date | undefined;\n\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.versionsComparisonFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.versionsComparisonFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.versionsComparisonFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.versionsComparisonFilterData.fromDate) {\n        fromDate = new Date(this.versionsComparisonFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n\n      if (this.versionsComparisonFilterData.toDate) {\n        toDate = new Date(this.versionsComparisonFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n\n    try {\n      console.log('loadVersionsComparison - Volám API pro načtení dat', { instanceId, methodName, className, fromDate, toDate });\n\n      const subscription = this.performanceService.getVersionsComparison(instanceId, methodName, className, fromDate, toDate).subscribe({\n        next: (data: any) => {\n          console.log('loadVersionsComparison - Data úspěšně načtena:', data);\n\n          if (!data) {\n            console.error('loadVersionsComparison - API vrátilo prázdná data');\n            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n            this.loading = false;\n            return;\n          }\n\n          // Uložení dat pro pozdější vykreslení\n          this.versionsComparisonData = data;\n\n          // Vykreslení grafu\n          this.renderVersionsComparisonChart(data);\n          this.loading = false;\n        },\n        error: (error: any) => {\n          console.error('Chyba při načítání srovnání verzí:', error);\n          this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi verzemi. ' +\n                      (error.message ? error.message : 'Zkuste to prosím znovu.');\n          this.loading = false;\n\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadVersionsComparison - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadVersionsComparison - Opakuji volání API');\n              this.loadVersionsComparison();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadVersionsComparison - Volání API dokončeno');\n        }\n      });\n\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadVersionsComparison - Chyba při volání API:', error);\n      this.error = 'Nepodařilo se načíst srovnání výkonu metod mezi verzemi. Zkuste to prosím znovu.';\n      this.loading = false;\n    }\n  }\n\n  /**\n   * Zpracování změny filtru pro srovnání verzí\n   */\n  onVersionsComparisonFilterChange(filterData: PerformanceFilterData): void {\n    console.log('onVersionsComparisonFilterChange - Přijatá data filtru:', filterData);\n\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.versionsComparisonFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onVersionsComparisonFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    }\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onVersionsComparisonFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    // Kontrola, zda je vybrána instance\n    if (filterData.instanceId === null || filterData.instanceId === undefined || String(filterData.instanceId) === \"null\") {\n      console.warn('onVersionsComparisonFilterChange - Není vybrána instance, přeskakuji načítání dat');\n      // Aktualizujeme data filtru, ale nevoláme načítání dat\n      this.versionsComparisonFilterData = filterData;\n      return;\n    }\n\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n\n    this.versionsComparisonFilterData = filterData;\n    console.log('onVersionsComparisonFilterChange - Zpracovaná data filtru:', this.versionsComparisonFilterData);\n    this.loadVersionsComparison();\n  }\n\n  /**\n   * Načtení nejčastěji volaných metod\n   */\n  loadMostCalledMethods(): void {\n    console.log('loadMostCalledMethods - Začínám načítat data');\n\n    // Přidáme kontrolu, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadMostCalledMethods - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    // Nastavíme příznak načítání\n    this.loading = true;\n\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadMostCalledMethods - Instance nejsou načteny, načítám je');\n\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: (data: any) => {\n          console.log('loadMostCalledMethods - Instance úspěšně načteny:', data);\n          this.instances = data;\n\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n\n          // Pokračujeme v načítání dat\n          this.loadMostCalledMethods();\n        },\n        error: (error: any) => {\n          console.error('loadMostCalledMethods - Chyba při načítání instancí:', error);\n          this.mostCalledMethods = [];\n          this.loading = false;\n        }\n      });\n\n      return;\n    }\n\n    // Příprava parametrů\n    // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n    let instanceId = this.aggregatedFilterData.instanceId;\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n      instanceId = undefined;\n      console.log('loadMostCalledMethods - Použití undefined pro instanceId (všechny instance)');\n    }\n    let fromDate: Date | undefined;\n    let toDate: Date | undefined;\n\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.aggregatedFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.aggregatedFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.aggregatedFilterData.fromDate) {\n        fromDate = new Date(this.aggregatedFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n\n      if (this.aggregatedFilterData.toDate) {\n        toDate = new Date(this.aggregatedFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n\n    try {\n      console.log('loadMostCalledMethods - Volám API pro načtení dat', { instanceId, fromDate, toDate });\n\n      const subscription = this.performanceService.getMostCalledMethods(instanceId, fromDate, toDate, 10).subscribe({\n        next: (data: any) => {\n          console.log('loadMostCalledMethods - Data úspěšně načtena:', data);\n\n          if (!data) {\n            console.error('loadMostCalledMethods - API vrátilo prázdná data');\n            this.mostCalledMethods = [];\n            this.loading = false;\n            return;\n          }\n\n          this.mostCalledMethods = data;\n\n          // Aplikujeme řazení na načtená data\n          this.applySortToMethods();\n\n          this.loading = false;\n        },\n        error: (error: any) => {\n          console.error('Chyba při načítání nejčastěji volaných metod:', error);\n          this.mostCalledMethods = [];\n          this.loading = false;\n\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadMostCalledMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadMostCalledMethods - Opakuji volání API');\n              this.loadMostCalledMethods();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadMostCalledMethods - Volání API dokončeno');\n        }\n      });\n\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadMostCalledMethods - Chyba při volání API:', error);\n      this.mostCalledMethods = [];\n      this.loading = false;\n    }\n  }\n\n  /**\n   * Načtení nejpomalejších metod\n   */\n  loadSlowestMethods(): void {\n    console.log('loadSlowestMethods - Začínám načítat data');\n\n    // Přidáme kontrolu, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadSlowestMethods - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    // Nastavíme příznak načítání\n    this.loading = true;\n\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadSlowestMethods - Instance nejsou načteny, načítám je');\n\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: (data: any) => {\n          console.log('loadSlowestMethods - Instance úspěšně načteny:', data);\n          this.instances = data;\n\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n\n          // Pokračujeme v načítání dat\n          this.loadSlowestMethods();\n        },\n        error: (error: any) => {\n          console.error('loadSlowestMethods - Chyba při načítání instancí:', error);\n          this.slowestMethods = [];\n        }\n      });\n\n      return;\n    }\n\n    // Příprava parametrů\n    // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n    let instanceId = this.aggregatedFilterData.instanceId;\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n      instanceId = undefined;\n      console.log('loadSlowestMethods - Použití undefined pro instanceId (všechny instance)');\n    }\n    let fromDate: Date | undefined;\n    let toDate: Date | undefined;\n\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.aggregatedFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.aggregatedFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.aggregatedFilterData.fromDate) {\n        fromDate = new Date(this.aggregatedFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n\n      if (this.aggregatedFilterData.toDate) {\n        toDate = new Date(this.aggregatedFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n\n    try {\n      console.log('loadSlowestMethods - Volám API pro načtení dat', { instanceId, fromDate, toDate });\n\n      const subscription = this.performanceService.getSlowestMethods(instanceId, fromDate, toDate, 10).subscribe({\n        next: (data: any) => {\n          console.log('loadSlowestMethods - Data úspěšně načtena:', data);\n\n          if (!data) {\n            console.error('loadSlowestMethods - API vrátilo prázdná data');\n            this.slowestMethods = [];\n            this.loading = false;\n            return;\n          }\n\n          this.slowestMethods = data;\n\n          // Aplikujeme řazení na načtená data\n          this.applySortToMethods();\n\n          this.loading = false;\n        },\n        error: (error: any) => {\n          console.error('Chyba při načítání nejpomalejších metod:', error);\n          this.slowestMethods = [];\n          this.loading = false;\n\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadSlowestMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadSlowestMethods - Opakuji volání API');\n              this.loadSlowestMethods();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadSlowestMethods - Volání API dokončeno');\n        }\n      });\n\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadSlowestMethods - Chyba při volání API:', error);\n      this.slowestMethods = [];\n      this.loading = false;\n    }\n  }\n\n  /**\n   * Načtení všech metod\n   */\n  loadAllMethods(): void {\n    // Příprava parametrů\n    // Pokud instanceId je řetězec \"null\", hodnota null, undefined nebo 0 (všechny instance), použijeme undefined\n    let instanceId = this.aggregatedFilterData.instanceId;\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\" || instanceId === 0) {\n      instanceId = undefined;\n      console.log('loadAllMethods - Použití undefined pro instanceId (všechny instance)');\n    }\n    let fromDate: Date | undefined;\n    let toDate: Date | undefined;\n\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.aggregatedFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.aggregatedFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.aggregatedFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.aggregatedFilterData.fromDate) {\n        fromDate = new Date(this.aggregatedFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n\n      if (this.aggregatedFilterData.toDate) {\n        toDate = new Date(this.aggregatedFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n\n    try {\n      console.log('loadAllMethods - Volám API pro načtení dat', { instanceId, fromDate, toDate });\n\n      const subscription = this.performanceService.getAllMethods(instanceId, fromDate, toDate).subscribe({\n        next: (data: any) => {\n          console.log('loadAllMethods - Data úspěšně načtena:', data);\n\n          if (!data) {\n            console.error('loadAllMethods - API vrátilo prázdná data');\n            this.allMethods = [];\n            return;\n          }\n\n          this.allMethods = data;\n\n          // Aplikujeme řazení na načtená data\n          this.applySortToMethods();\n        },\n        error: (error: any) => {\n          console.error('Chyba při načítání všech metod:', error);\n          this.allMethods = [];\n\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadAllMethods - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadAllMethods - Opakuji volání API');\n              this.loadAllMethods();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadAllMethods - Volání API dokončeno');\n        }\n      });\n\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadAllMethods - Chyba při volání API:', error);\n      this.allMethods = [];\n    }\n  }\n\n  /**\n   * Načtení detailu metody\n   */\n  loadMethodDetail(): void {\n    console.log('loadMethodDetail - Začínám načítat data');\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('loadMethodDetail - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    // Kontrola, zda máme vybranou metodu nebo zadané povinné parametry\n    if (!this.selectedMethod && (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName)) {\n      console.warn('loadMethodDetail - Není vybrána metoda a nejsou zadány povinné parametry');\n      // Necháme validaci na formuláři, nezobrazujeme chybový blok\n      this.loading = false;\n      return;\n    }\n\n    // Pokud máme vybranou metodu, ale nemáme zadané parametry, použijeme parametry z vybrané metody\n    if (this.selectedMethod && (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName)) {\n      console.log('loadMethodDetail - Použití parametrů z vybrané metody');\n      this.methodDetailFilterData.className = this.selectedMethod.className;\n      this.methodDetailFilterData.methodName = this.selectedMethod.methodName;\n\n      // Zjištění aktuální hodnoty instanceId\n      let instanceId = this.selectedMethod.instanceId;\n\n      // Pokud instanceId je null nebo undefined, použijeme 0 (všechny instance)\n      if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n        instanceId = 0;\n      }\n\n      // Nastavení instanceId\n      this.methodDetailFilterData.instanceId = instanceId;\n\n      // Uložení filtru do localStorage\n      this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n    }\n\n    // Kontrola, zda máme načtené instance\n    if (!this.instances || this.instances.length === 0) {\n      console.warn('loadMethodDetail - Instance nejsou načteny, načítám je');\n\n      // Načtení instancí\n      this.instanceService.getAll().subscribe({\n        next: (data: any) => {\n          console.log('loadMethodDetail - Instance úspěšně načteny:', data);\n          this.instances = data;\n\n          // Po načtení instancí inicializujeme pole filtrů\n          this.initFilterFields();\n\n          // Pokračujeme v načítání dat\n          this.loadMethodDetail();\n        },\n        error: (error: any) => {\n          console.error('loadMethodDetail - Chyba při načítání instancí:', error);\n          this.error = 'Nepodařilo se načíst seznam instancí. Zkuste to prosím znovu.';\n          this.loading = false;\n        }\n      });\n\n      return;\n    }\n\n    // Kontrola, zda jsou vyplněny všechny povinné parametry\n    if (!this.methodDetailFilterData.instanceId || !this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName) {\n      // Necháme validaci na formuláři, nezobrazujeme chybový blok\n      this.loading = false;\n      return;\n    }\n\n    // Příprava parametrů\n    // Pro metodu getMethodDetail potřebujeme instanceId jako number, null nebo 0\n    let instanceId: number | null = this.methodDetailFilterData.instanceId ?? null;\n    // Pokud instanceId je null, undefined nebo řetězec \"null\", použijeme 0 (všechny instance)\n    if (instanceId === null || instanceId === undefined || String(instanceId) === \"null\") {\n      instanceId = 0; // 0 = všechny instance\n      console.log('loadMethodDetail - Použití hodnoty 0 pro instanceId (všechny instance)');\n\n      // Aktualizujeme hodnotu v objektu filtru\n      this.methodDetailFilterData.instanceId = 0;\n    }\n\n    console.log('loadMethodDetail - Finální hodnota instanceId:', instanceId);\n    const className = this.methodDetailFilterData.className;\n    const methodName = this.methodDetailFilterData.methodName;\n    let fromDate: Date | undefined;\n    let toDate: Date | undefined;\n\n    // Pokud period je null nebo undefined, nastavíme výchozí hodnotu na 7 dní\n    if (!this.methodDetailFilterData.period) {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - 7); // Výchozí hodnota 7 dní\n      toDate = today;\n    }\n    // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n    else if (this.methodDetailFilterData.period !== 'custom') {\n      const today = new Date();\n      fromDate = new Date();\n      fromDate.setDate(today.getDate() - Number(this.methodDetailFilterData.period));\n      toDate = today;\n    }\n    // Pokud je vybráno vlastní období, použijeme zadané hodnoty\n    else {\n      // Kontrola, zda fromDate a toDate jsou validní\n      if (this.methodDetailFilterData.fromDate) {\n        fromDate = new Date(this.methodDetailFilterData.fromDate);\n      } else {\n        // Výchozí hodnota, pokud fromDate není zadáno\n        fromDate = new Date();\n        fromDate.setDate(fromDate.getDate() - 7);\n      }\n\n      if (this.methodDetailFilterData.toDate) {\n        toDate = new Date(this.methodDetailFilterData.toDate);\n      } else {\n        // Výchozí hodnota, pokud toDate není zadáno\n        toDate = new Date();\n      }\n    }\n\n    try {\n      console.log('loadMethodDetail - Volám API pro načtení dat', { instanceId, className, methodName, fromDate, toDate });\n\n      // Kontrola, zda jsou všechny povinné parametry nastaveny\n      if (!className || !methodName) {\n        console.error('loadMethodDetail - Nejsou zadány povinné parametry (třída nebo metoda)');\n        this.error = 'Pro zobrazení detailu metody je nutné zadat název třídy a metody.';\n        this.loading = false;\n        return;\n      }\n\n      // Pokud instanceId je 0, použijeme null, aby se parametr instanceId vůbec neodeslal\n      const finalInstanceId = instanceId === 0 ? null : instanceId;\n      const subscription = this.performanceService.getMethodDetail(finalInstanceId, className, methodName, fromDate, toDate).subscribe({\n        next: (data: any) => {\n          console.log('loadMethodDetail - Data úspěšně načtena:', data);\n\n          if (!data) {\n            console.error('loadMethodDetail - API vrátilo prázdná data');\n            this.error = 'API vrátilo prázdná data. Zkuste to prosím znovu.';\n            this.loading = false;\n            return;\n          }\n\n          // Aktualizace hodnot filtru podle načtených dat\n          // Tím zajistíme, že hodnoty ve filtru budou odpovídat načteným datům\n          if (data.className && data.methodName) {\n            console.log('loadMethodDetail - Aktualizuji hodnoty filtru podle načtených dat:', {\n              className: data.className,\n              methodName: data.methodName\n            });\n\n            this.methodDetailFilterData.className = data.className;\n            this.methodDetailFilterData.methodName = data.methodName;\n\n            // Uložení filtru do localStorage\n            this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n          }\n\n          this.methodDetailData = data;\n          this.methodDetailChartData = data;\n\n          // Kontrola, zda jsou data prázdná\n          const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0;\n          this.hasMethodDetailChartData = hasData;\n\n          // Vykreslení grafu\n          this.renderMethodDetailChart(data);\n          this.loading = false;\n        },\n        error: (error: any) => {\n          console.error('Chyba při načítání detailu metody:', error);\n          this.error = 'Nepodařilo se načíst detail metody. ' +\n                      (error.message ? error.message : 'Zkuste to prosím znovu.');\n          this.loading = false;\n\n          // Pokud je chyba způsobena tím, že server neodpovídá, zkusíme to znovu po 5 sekundách\n          if (error.status === 0) {\n            console.log('loadMethodDetail - Server neodpovídá, zkusím to znovu za 5 sekund');\n            setTimeout(() => {\n              console.log('loadMethodDetail - Opakuji volání API');\n              this.loadMethodDetail();\n            }, 5000);\n          }\n        },\n        complete: () => {\n          console.log('loadMethodDetail - Volání API dokončeno');\n        }\n      });\n\n      this.subscriptions.push(subscription);\n    } catch (error) {\n      console.error('loadMethodDetail - Chyba při volání API:', error);\n      this.error = 'Nepodařilo se načíst detail metody. Zkuste to prosím znovu.';\n      this.loading = false;\n    }\n  }\n\n  /**\n   * Načtení dat podle vybraného typu metod\n   */\n  loadMethodsByType(): void {\n    console.log('loadMethodsByType - Načítám data podle typu:', this.methodsDisplayType);\n\n    switch (this.methodsDisplayType) {\n      case 'slowest':\n        this.loadSlowestMethods();\n        break;\n      case 'most-called':\n        this.loadMostCalledMethods();\n        break;\n      case 'all':\n        this.loadAllMethods();\n        break;\n      default:\n        this.loadSlowestMethods();\n        break;\n    }\n  }\n\n\n\n  /**\n   * Zpracování změny filtru pro metody\n   */\n  onMethodsFilterChange(filterData: PerformanceFilterData): void {\n    console.log('onMethodsFilterChange - Přijatá data filtru:', filterData);\n\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.aggregatedFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onMethodsFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    }\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onMethodsFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        filterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        filterData.toDate = new Date(filterData.toDate);\n      }\n    }\n\n    // Aktualizace typu zobrazovaných metod\n    if (filterData.methodsDisplayType) {\n      this.methodsDisplayType = filterData.methodsDisplayType;\n    } else {\n      // Výchozí hodnota, pokud není zadána\n      this.methodsDisplayType = 'slowest';\n      filterData.methodsDisplayType = 'slowest';\n    }\n\n    this.aggregatedFilterData = filterData;\n    console.log('onMethodsFilterChange - Zpracovaná data filtru:', this.aggregatedFilterData);\n    this.loadMethodsByType();\n\n    // Reinicializace popoverů po změně typu zobrazovaných metod\n    setTimeout(() => {\n      this.initPopovers();\n    }, 500);\n  }\n\n  /**\n   * Zpracování změny filtru pro detail metody\n   */\n  onMethodDetailFilterChange(filterData: PerformanceFilterData): void {\n    console.log('onMethodDetailFilterChange - Přijatá data filtru:', filterData);\n\n    // Kontrola, zda již neprobíhá načítání dat\n    if (this.loading) {\n      console.warn('onMethodDetailFilterChange - Načítání dat již probíhá, přeskakuji');\n      return;\n    }\n\n    // Aktualizujeme data filtru\n    this.methodDetailFilterData = { ...filterData };\n\n    // Kontrola, zda je hodnota instanceId nastavena\n    if (filterData.instanceId === null || filterData.instanceId === undefined || String(filterData.instanceId) === \"null\") {\n      console.log('onMethodDetailFilterChange - Hodnota instanceId není nastavena, použijeme 0 (všechny instance)');\n      this.methodDetailFilterData.instanceId = 0;\n    } else {\n      console.log('onMethodDetailFilterChange - Hodnota instanceId je nastavena:', filterData.instanceId);\n    }\n\n    // Pokud se změnil filtr, resetujeme vybranou metodu, aby se použily hodnoty z filtru\n    if (this.selectedMethod) {\n      if (this.selectedMethod.className !== this.methodDetailFilterData.className ||\n          this.selectedMethod.methodName !== this.methodDetailFilterData.methodName ||\n          this.selectedMethod.instanceId !== this.methodDetailFilterData.instanceId) {\n        console.log('onMethodDetailFilterChange - Resetuji vybranou metodu, protože se změnil filtr');\n        this.selectedMethod = null;\n      }\n    }\n\n    // Pokud je vybráno vlastní období, převedeme fromDate a toDate na objekty Date\n    if (filterData.period === 'custom') {\n      if (filterData.fromDate && typeof filterData.fromDate === 'string') {\n        this.methodDetailFilterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (filterData.toDate && typeof filterData.toDate === 'string') {\n        this.methodDetailFilterData.toDate = new Date(filterData.toDate);\n      }\n    }\n    // Pokud není vybráno vlastní období, ale fromDate a toDate jsou nastaveny (z advanced-filter)\n    else if (filterData.fromDate && filterData.toDate) {\n      if (typeof filterData.fromDate === 'string') {\n        this.methodDetailFilterData.fromDate = new Date(filterData.fromDate);\n      }\n      if (typeof filterData.toDate === 'string') {\n        this.methodDetailFilterData.toDate = new Date(filterData.toDate);\n      }\n    }\n\n    console.log('onMethodDetailFilterChange - Zpracovaná data filtru:', this.methodDetailFilterData);\n\n    // Kontrola, zda jsou vyplněny povinné parametry\n    if (!this.methodDetailFilterData.className || !this.methodDetailFilterData.methodName) {\n      console.warn('onMethodDetailFilterChange - Nejsou vyplněny povinné parametry (třída nebo metoda), přeskakuji načítání dat');\n      return;\n    }\n\n    // Kontrola, zda se filtr skutečně změnil\n    const currentFilterStr = JSON.stringify(this.methodDetailFilterData);\n    const newFilterStr = JSON.stringify(filterData);\n\n    // Pokud se filtr nezměnil, pouze logujeme, ale pokračujeme v načítání dat\n    // Tím zajistíme, že tlačítko \"Načíst data\" bude vždy fungovat\n    if (currentFilterStr === newFilterStr) {\n      console.log('onMethodDetailFilterChange - Filtr se nezměnil, ale pokračuji v načítání dat');\n    } else {\n      // Pokud se filtr změnil, uložíme ho do localStorage\n      this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n    }\n\n    // Resetujeme indikátor, zda jsou k dispozici data pro graf\n    this.hasMethodDetailChartData = true;\n\n    // Vždy načteme data a překreslíme graf\n    this.loadMethodDetail();\n  }\n\n  /**\n   * Zobrazení detailu metody\n   */\n  showMethodDetail(method: any): void {\n    console.log('showMethodDetail - Zobrazuji detail metody:', method);\n    this.selectedMethod = method;\n\n    // Zjištění aktuální hodnoty instanceId z aktivního filtru podle záložky\n    let instanceId = method.instanceId;\n\n    // Pro záložky \"aggregated\" a \"methods\" použij hodnotu z this.aggregatedFilterData.instanceId\n    if (this.activeTab === 'aggregated' || this.activeTab === 'methods') {\n      if (this.aggregatedFilterData && this.aggregatedFilterData.instanceId !== undefined) {\n        instanceId = this.aggregatedFilterData.instanceId;\n        // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme 0 (všechny instance)\n        if (instanceId === null || String(instanceId) === \"null\") {\n          instanceId = 0;\n        }\n      }\n    }\n    // Pro záložku \"versions-comparison\" použij hodnotu z this.versionsComparisonFilterData.instanceId\n    else if (this.activeTab === 'versions-comparison') {\n      if (this.versionsComparisonFilterData && this.versionsComparisonFilterData.instanceId !== undefined) {\n        instanceId = this.versionsComparisonFilterData.instanceId;\n        // Pokud instanceId je řetězec \"null\" nebo hodnota null, použijeme undefined\n        if (instanceId === null || String(instanceId) === \"null\") {\n          instanceId = undefined;\n        }\n      }\n    }\n    // Pro záložku \"instances-comparison\" a ostatní záložky použij hodnotu z method.instanceId\n    // Toto je již nastaveno výchozí hodnotou instanceId = method.instanceId\n\n    // Nastavení hodnot filtru\n    // Pro metodu getMethodDetail potřebujeme instanceId jako number nebo null\n    // Pokud instanceId je undefined, použijeme hodnotu z metody\n    if (instanceId === undefined) {\n      instanceId = method.instanceId;\n    }\n\n    // Pokud instanceId je null, nastavíme ho na 0 (všechny instance)\n    if (instanceId === null) {\n      instanceId = 0;\n    }\n\n    console.log('showMethodDetail - Nastavuji hodnoty filtru:', {\n      instanceId: instanceId,\n      className: method.className,\n      methodName: method.methodName\n    });\n\n    // Aktualizace hodnot filtru\n    this.methodDetailFilterData = {\n      ...this.methodDetailFilterData,\n      instanceId: instanceId,\n      className: method.className,\n      methodName: method.methodName\n    };\n\n    // Uložení filtru do localStorage, aby byl dostupný i po obnovení stránky\n    this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n\n    // Resetujeme indikátor, zda jsou k dispozici data pro graf\n    this.hasMethodDetailChartData = true;\n\n    // Přepnutí na záložku s detailem metody\n    this.activeTab = 'method-detail';\n\n    // Uložení aktivní záložky do localStorage\n    this.saveActiveTab('method-detail');\n\n    // Načtení detailu metody\n    this.loadMethodDetail();\n  }\n\n  /**\n   * Uložení nastavení viditelnosti metrik do localStorage\n   */\n  saveMetricsVisibility(chartType: string, datasets: any[]): void {\n    try {\n      const visibility: Record<string, boolean> = {};\n\n      // Procházíme všechny datasety a ukládáme jejich viditelnost\n      datasets.forEach(dataset => {\n        if (dataset.label) {\n          // Použijeme metodu isDatasetVisible z Chart.js, pokud je k dispozici\n          if (this.aggregatedPerformanceChart && chartType === 'aggregated') {\n            const index = datasets.indexOf(dataset);\n            visibility[dataset.label] = this.aggregatedPerformanceChart.isDatasetVisible(index);\n          } else if (this.instancesComparisonChart && chartType === 'instances-comparison') {\n            const index = datasets.indexOf(dataset);\n            visibility[dataset.label] = this.instancesComparisonChart.isDatasetVisible(index);\n          } else if (this.versionsComparisonChart && chartType === 'versions-comparison') {\n            const index = datasets.indexOf(dataset);\n            visibility[dataset.label] = this.versionsComparisonChart.isDatasetVisible(index);\n          } else if (this.methodDetailChart && chartType === 'method-detail') {\n            const index = datasets.indexOf(dataset);\n            visibility[dataset.label] = this.methodDetailChart.isDatasetVisible(index);\n          } else {\n            // Fallback na původní logiku\n            visibility[dataset.label] = !dataset.hidden;\n          }\n        }\n      });\n\n      // Uložení do localStorage\n      localStorage.setItem(`metrics_visibility_${chartType}`, JSON.stringify(visibility));\n      console.log(`Nastavení viditelnosti metrik pro ${chartType} uloženo:`, visibility);\n    } catch (error) {\n      console.error(`Chyba při ukládání nastavení viditelnosti metrik pro ${chartType}:`, error);\n    }\n  }\n\n  /**\n   * Uložení aktivní záložky do localStorage\n   */\n  saveActiveTab(tabId: string): void {\n    try {\n      localStorage.setItem('performance_active_tab', tabId);\n      console.log(`Uložena aktivní záložka: ${tabId}`);\n    } catch (error) {\n      console.error(`Chyba při ukládání aktivní záložky:`, error);\n    }\n  }\n\n  /**\n   * Načtení aktivní záložky z localStorage\n   */\n  loadActiveTab(): string | null {\n    try {\n      const activeTab = localStorage.getItem('performance_active_tab');\n      console.log(`Načtena aktivní záložka z localStorage: ${activeTab}`);\n      return activeTab;\n    } catch (error) {\n      console.error(`Chyba při načítání aktivní záložky:`, error);\n      return null;\n    }\n  }\n\n  /**\n   * Uložení filtru do localStorage\n   * @param entityType Typ entity, pro kterou se ukládá filtr\n   * @param filterData Data filtru\n   */\n  saveFilter(entityType: string, filterData: any): void {\n    try {\n      const lastFilterKey = `last_filter_${entityType}`;\n      localStorage.setItem(lastFilterKey, JSON.stringify(filterData));\n      console.log(`Uložen filtr pro ${entityType} do localStorage:`, filterData);\n    } catch (error) {\n      console.error(`Chyba při ukládání filtru pro ${entityType} do localStorage:`, error);\n    }\n  }\n\n  /**\n   * Zpracování kliknutí na záhlaví sloupce pro řazení\n   * @param column Název sloupce, podle kterého se má řadit\n   */\n  onSort(column: string): void {\n    console.log(`onSort - Řazení podle sloupce: ${column}`);\n\n    const newSortingState = this.tableSortingService.createNewSortingStateWithDefaults(\n      this.sortColumn,\n      this.sortDirection,\n      column,\n      ['totalCount', 'min', 'max', 'avg', 'median', 'percentil95'] // číselné sloupce\n    );\n\n    // Aktualizace lokálního stavu\n    this.sortColumn = newSortingState.column;\n    this.sortDirection = newSortingState.direction;\n\n    // Uložení do localStorage podle aktivní záložky\n    const tableName = this.activeTab === 'methods' ? 'methods' : 'methods';\n    this.tableSortingService.saveSortingState('performance', tableName, newSortingState);\n\n    console.log(`onSort - Nové řazení: ${this.sortColumn} ${this.sortDirection}`);\n\n    // Aplikujeme řazení na data\n    this.applySortToMethods();\n  }\n\n  /**\n   * Aplikace řazení na data metod\n   */\n  applySortToMethods(): void {\n    console.log(`applySortToMethods - Aplikuji řazení: ${this.sortColumn} ${this.sortDirection}`);\n\n    // Řazení nejpomalejších metod\n    if (this.slowestMethods && this.slowestMethods.length > 0) {\n      this.slowestMethods = this.sortMethods([...this.slowestMethods]);\n    }\n\n    // Řazení nejčastěji volaných metod\n    if (this.mostCalledMethods && this.mostCalledMethods.length > 0) {\n      this.mostCalledMethods = this.sortMethods([...this.mostCalledMethods]);\n    }\n\n    // Řazení všech metod\n    if (this.allMethods && this.allMethods.length > 0) {\n      this.allMethods = this.sortMethods([...this.allMethods]);\n    }\n  }\n\n  /**\n   * Řazení pole metod podle aktuálního sloupce a směru\n   * @param methods Pole metod k seřazení\n   * @returns Seřazené pole metod\n   */\n  sortMethods(methods: any[]): any[] {\n    return methods.sort((a, b) => {\n      let valueA: any;\n      let valueB: any;\n\n      // Získání hodnot pro porovnání podle názvu sloupce\n      switch (this.sortColumn) {\n        case 'instanceName':\n          valueA = a.instanceName?.toLowerCase() || '';\n          valueB = b.instanceName?.toLowerCase() || '';\n          break;\n        case 'className':\n          valueA = a.className?.toLowerCase() || '';\n          valueB = b.className?.toLowerCase() || '';\n          break;\n        case 'methodName':\n          valueA = a.methodName?.toLowerCase() || '';\n          valueB = b.methodName?.toLowerCase() || '';\n          break;\n        case 'totalCount':\n          valueA = a.totalCount || 0;\n          valueB = b.totalCount || 0;\n          break;\n        case 'min':\n          valueA = a.min || 0;\n          valueB = b.min || 0;\n          break;\n        case 'max':\n          valueA = a.max || 0;\n          valueB = b.max || 0;\n          break;\n        case 'avg':\n          valueA = a.avg || 0;\n          valueB = b.avg || 0;\n          break;\n        case 'median':\n          valueA = a.median || 0;\n          valueB = b.median || 0;\n          break;\n        case 'percentil95':\n          valueA = a.percentil95 || 0;\n          valueB = b.percentil95 || 0;\n          break;\n        default:\n          valueA = 0;\n          valueB = 0;\n      }\n\n      // Porovnání hodnot podle směru řazení\n      if (this.sortDirection === 'asc') {\n        if (typeof valueA === 'string') {\n          return valueA.localeCompare(valueB);\n        } else {\n          return valueA - valueB;\n        }\n      } else {\n        if (typeof valueA === 'string') {\n          return valueB.localeCompare(valueA);\n        } else {\n          return valueB - valueA;\n        }\n      }\n    });\n  }\n\n  /**\n   * Načtení stavu třídění z localStorage\n   */\n  private loadSortingState(): void {\n    const defaultSort = { column: 'avg', direction: 'desc' as 'asc' | 'desc' };\n    const savedSortingState = this.tableSortingService.loadSortingState('performance', 'methods', defaultSort);\n\n    if (savedSortingState) {\n      this.sortColumn = savedSortingState.column;\n      this.sortDirection = savedSortingState.direction;\n    }\n  }\n\n  /**\n   * Načtení nastavení viditelnosti metrik z localStorage\n   */\n  loadMetricsVisibility(chartType: string): any {\n    try {\n      const visibilityJson = localStorage.getItem(`metrics_visibility_${chartType}`);\n\n      if (visibilityJson) {\n        const visibility = JSON.parse(visibilityJson);\n        console.log(`Načteno nastavení viditelnosti metrik pro ${chartType}:`, visibility);\n        return visibility;\n      }\n    } catch (error) {\n      console.error(`Chyba při načítání nastavení viditelnosti metrik pro ${chartType}:`, error);\n    }\n\n    return null;\n  }\n\n  /**\n   * Vykreslení grafu agregovaných výkonnostních metrik\n   */\n  renderAggregatedPerformanceChart(data: any): void {\n    console.log('renderAggregatedPerformanceChart - data:', data);\n\n    // Nastavíme příznak, že se graf vykresluje\n    this.renderingAggregatedChart = true;\n\n    // Uložíme data pro pozdější vykreslení\n    this.aggregatedPerformanceData = data;\n\n    // Příprava dat pro graf\n    const chartData = this.prepareAggregatedPerformanceChartData(data);\n    console.log('renderAggregatedPerformanceChart - chartData:', chartData);\n\n    // Kontrola, zda jsou data prázdná\n    const hasData = chartData.labels && Array.isArray(chartData.labels) && chartData.labels.length > 0;\n    console.log('renderAggregatedPerformanceChart - hasData:', hasData, 'labels:', chartData.labels);\n\n    if (!hasData) {\n      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n      this.hasAggregatedChartData = false;\n      this.renderingAggregatedChart = false;\n\n      // Zničíme existující graf, pokud existuje\n      if (this.aggregatedPerformanceChart) {\n        this.aggregatedPerformanceChart.destroy();\n        this.aggregatedPerformanceChart = null;\n      }\n\n      return;\n    }\n\n    // Pokud máme data, nastavíme indikátor\n    this.hasAggregatedChartData = true;\n\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.aggregatedPerformanceChartRef) {\n      console.warn('renderAggregatedPerformanceChart - Reference na canvas element není k dispozici, zkusím později');\n\n      // Zkusíme počkat a zkusit to znovu po delším timeoutu\n      setTimeout(() => {\n        console.log('renderAggregatedPerformanceChart - Zkouším znovu po delším timeoutu (2000ms)');\n        if (this.aggregatedPerformanceChartRef) {\n          this.renderAggregatedPerformanceChart(data);\n        } else {\n          console.error('renderAggregatedPerformanceChart - Canvas element stále není k dispozici po 2000ms!');\n          this.renderingAggregatedChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n        }\n      }, 2000);\n\n      return;\n    }\n\n    console.log('renderAggregatedPerformanceChart - Reference na canvas element je k dispozici');\n\n    try {\n      // Kontrola, zda je canvas element v DOM\n      if (!document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n        console.warn('renderAggregatedPerformanceChart - Canvas element není v DOM, zkusím později');\n\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderAggregatedPerformanceChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n          if (this.aggregatedPerformanceChartRef && document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n            this.renderAggregatedPerformanceChart(data);\n          } else {\n            console.error('renderAggregatedPerformanceChart - Canvas element stále není v DOM po 2000ms!');\n          }\n        }, 2000);\n\n        return;\n      }\n\n      // Získání kontextu canvas elementu\n      const ctx = this.aggregatedPerformanceChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        console.error('renderAggregatedPerformanceChart - Nepodařilo se získat kontext canvas elementu!');\n        return;\n      }\n      console.log('renderAggregatedPerformanceChart - Kontext canvas elementu je k dispozici');\n\n    if (this.aggregatedPerformanceChart) {\n      console.log('renderAggregatedPerformanceChart - Ničím existující graf');\n      this.aggregatedPerformanceChart.destroy();\n    }\n\n    // Kontrola, zda jsou všechna pole dat stejné délky\n    const labelsLength = chartData.labels.length;\n    const avgDataLength = chartData.avgData.length;\n    const medianDataLength = chartData.medianData.length;\n    const p95DataLength = chartData.p95Data.length;\n    const p99DataLength = chartData.p99Data.length;\n    const minDataLength = chartData.minData.length;\n    const maxDataLength = chartData.maxData.length;\n    const totalCountDataLength = chartData.totalCountData.length;\n\n    console.log('renderAggregatedPerformanceChart - Délky polí:', {\n      labelsLength,\n      avgDataLength,\n      medianDataLength,\n      p95DataLength,\n      p99DataLength,\n      minDataLength,\n      maxDataLength,\n      totalCountDataLength\n    });\n\n    // Pokud některé pole má jinou délku než labels, upravíme ho\n    if (avgDataLength !== labelsLength) {\n      console.warn('renderAggregatedPerformanceChart - avgData má jinou délku než labels, upravuji');\n      chartData.avgData = this.adjustArrayLength(chartData.avgData, labelsLength);\n    }\n    if (medianDataLength !== labelsLength) {\n      console.warn('renderAggregatedPerformanceChart - medianData má jinou délku než labels, upravuji');\n      chartData.medianData = this.adjustArrayLength(chartData.medianData, labelsLength);\n    }\n    if (p95DataLength !== labelsLength) {\n      console.warn('renderAggregatedPerformanceChart - p95Data má jinou délku než labels, upravuji');\n      chartData.p95Data = this.adjustArrayLength(chartData.p95Data, labelsLength);\n    }\n    if (p99DataLength !== labelsLength) {\n      console.warn('renderAggregatedPerformanceChart - p99Data má jinou délku než labels, upravuji');\n      chartData.p99Data = this.adjustArrayLength(chartData.p99Data, labelsLength);\n    }\n    if (minDataLength !== labelsLength) {\n      console.warn('renderAggregatedPerformanceChart - minData má jinou délku než labels, upravuji');\n      chartData.minData = this.adjustArrayLength(chartData.minData, labelsLength);\n    }\n    if (maxDataLength !== labelsLength) {\n      console.warn('renderAggregatedPerformanceChart - maxData má jinou délku než labels, upravuji');\n      chartData.maxData = this.adjustArrayLength(chartData.maxData, labelsLength);\n    }\n    if (totalCountDataLength !== labelsLength) {\n      console.warn('renderAggregatedPerformanceChart - totalCountData má jinou délku než labels, upravuji');\n      chartData.totalCountData = this.adjustArrayLength(chartData.totalCountData, labelsLength);\n    }\n\n    // Pokud jsou data k dispozici, vykreslíme graf\n    try {\n      console.log('renderAggregatedPerformanceChart - Vytvářím nový graf');\n\n      // Kontrola, zda je canvas element stále platný\n      if (!this.aggregatedPerformanceChartRef || !document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)) {\n        console.error('renderAggregatedPerformanceChart - Canvas element již není platný!');\n        return;\n      }\n\n      // Kontrola, zda má canvas element rozměry\n      const width = this.aggregatedPerformanceChartRef.nativeElement.width;\n      const height = this.aggregatedPerformanceChartRef.nativeElement.height;\n      console.log('renderAggregatedPerformanceChart - Canvas rozměry:', { width, height });\n\n      // Načtení nastavení viditelnosti metrik z localStorage\n      const visibility = this.loadMetricsVisibility('aggregated');\n\n      // Vytvoření datasetů s nastavením viditelnosti\n      const datasets = [\n        {\n          label: 'Průměr (ms)',\n          data: chartData.avgData,\n          borderColor: 'rgba(54, 162, 235, 1)',\n          backgroundColor: 'rgba(54, 162, 235, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Průměr (ms)'] : false\n        },\n        {\n          label: 'Medián (ms)',\n          data: chartData.medianData,\n          borderColor: 'rgba(75, 192, 192, 1)',\n          backgroundColor: 'rgba(75, 192, 192, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Medián (ms)'] : false\n        },\n        {\n          label: '95. percentil (ms)',\n          data: chartData.p95Data,\n          borderColor: 'rgba(255, 99, 132, 1)',\n          backgroundColor: 'rgba(255, 99, 132, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['95. percentil (ms)'] : false\n        },\n        {\n          label: '99. percentil (ms)',\n          data: chartData.p99Data,\n          borderColor: 'rgba(255, 159, 64, 1)',\n          backgroundColor: 'rgba(255, 159, 64, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['99. percentil (ms)'] : true\n        },\n        {\n          label: 'Minimum (ms)',\n          data: chartData.minData,\n          borderColor: 'rgba(153, 102, 255, 1)',\n          backgroundColor: 'rgba(153, 102, 255, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Minimum (ms)'] : true\n        },\n        {\n          label: 'Maximum (ms)',\n          data: chartData.maxData,\n          borderColor: 'rgba(255, 205, 86, 1)',\n          backgroundColor: 'rgba(255, 205, 86, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Maximum (ms)'] : true\n        },\n        {\n          label: 'Počet volání',\n          data: chartData.totalCountData,\n          borderColor: 'rgba(201, 203, 207, 1)',\n          backgroundColor: 'rgba(201, 203, 207, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Počet volání'] : true,\n          yAxisID: 'y1'\n        }\n      ];\n\n      // Určení, které osy Y mají být zobrazené na základě viditelných metrik\n      const visibleAxes = this.getVisibleAxes(datasets);\n\n      // Vytvoření nového grafu\n      this.aggregatedPerformanceChart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: chartData.labels,\n          datasets: datasets\n        },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          title: {\n            display: true,\n            text: chartData.title || 'Výkon v čase'\n          },\n          tooltip: {\n            mode: 'index',\n            intersect: false\n          },\n          legend: {\n            position: 'top',\n            labels: {\n              boxWidth: 12\n            },\n            onClick: (_e, legendItem, legend) => {\n              // Standardní chování - přepnutí viditelnosti datasetu\n              const index = legendItem.datasetIndex;\n              const ci = legend.chart;\n              if (index !== undefined) {\n                if (ci.isDatasetVisible(index)) {\n                  ci.hide(index);\n                  legendItem.hidden = true;\n                } else {\n                  ci.show(index);\n                  legendItem.hidden = false;\n                }\n\n                // Aktualizace viditelnosti os Y na základě nově viditelných metrik\n                this.updateAxisVisibility(ci);\n\n                // Počkáme, až se graf aktualizuje\n                setTimeout(() => {\n                  // Uložení nastavení viditelnosti do localStorage\n                  this.saveMetricsVisibility('aggregated', ci.data.datasets);\n                }, 0);\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Datum'\n            }\n          },\n          y: {\n            display: visibleAxes.showLeftAxis,\n            title: {\n              display: visibleAxes.showLeftAxis,\n              text: 'Doba odezvy (ms)'\n            },\n            beginAtZero: true\n          },\n          y1: {\n            display: visibleAxes.showRightAxis,\n            position: 'right',\n            title: {\n              display: visibleAxes.showRightAxis,\n              text: 'Počet volání'\n            },\n            beginAtZero: true,\n            grid: {\n              drawOnChartArea: !visibleAxes.showLeftAxis // Zobrazit mřížku pouze když levá osa není viditelná\n            }\n          }\n        }\n      }\n    });\n      console.log('renderAggregatedPerformanceChart - Graf úspěšně vytvořen');\n\n      // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n      setTimeout(() => {\n        if (this.aggregatedPerformanceChart) {\n          this.saveMetricsVisibility('aggregated', this.aggregatedPerformanceChart.data.datasets);\n        }\n        // Ukončíme vykreslování\n        this.renderingAggregatedChart = false;\n      }, 100);\n    } catch (error) {\n      console.error('renderAggregatedPerformanceChart - Chyba při vytváření grafu:', error);\n      this.renderingAggregatedChart = false; // Ukončíme vykreslování v případě chyby\n    }\n  } catch (error) {\n    console.error('renderAggregatedPerformanceChart - Chyba při získávání kontextu canvas elementu:', error);\n    this.renderingAggregatedChart = false; // Ukončíme vykreslování v případě chyby\n  }\n  }\n\n  /**\n   * Vykreslení grafu srovnání výkonu metod mezi instancemi\n   */\n  renderInstancesComparisonChart(data: any): void {\n    console.log('renderInstancesComparisonChart - data:', data);\n\n    // Nastavíme příznak, že se graf vykresluje\n    this.renderingInstancesComparisonChart = true;\n\n    // Uložíme data pro pozdější vykreslení\n    this.instancesComparisonData = data;\n\n    // Kontrola, zda jsou data prázdná\n    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0 &&\n                    data.datasets && Array.isArray(data.datasets) && data.datasets.length > 0;\n\n    if (!hasData) {\n      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n      this.hasInstancesComparisonChartData = false;\n      this.renderingInstancesComparisonChart = false;\n\n      // Zničíme existující graf, pokud existuje\n      if (this.instancesComparisonChart) {\n        this.instancesComparisonChart.destroy();\n        this.instancesComparisonChart = null;\n      }\n\n      return;\n    }\n\n    // Pokud máme data, nastavíme indikátor\n    this.hasInstancesComparisonChartData = true;\n\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.instancesComparisonChartRef) {\n      console.warn('renderInstancesComparisonChart - Reference na canvas element není k dispozici, zkusím později');\n\n      // Zkusíme počkat a zkusit to znovu po delším timeoutu\n      setTimeout(() => {\n        console.log('renderInstancesComparisonChart - Zkouším znovu po delším timeoutu (2000ms)');\n        if (this.instancesComparisonChartRef) {\n          this.renderInstancesComparisonChart(data);\n        } else {\n          console.error('renderInstancesComparisonChart - Canvas element stále není k dispozici po 2000ms!');\n          this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n        }\n      }, 2000);\n\n      return;\n    }\n\n    console.log('renderInstancesComparisonChart - Reference na canvas element je k dispozici');\n\n    try {\n      // Kontrola, zda je canvas element v DOM\n      if (!document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n        console.warn('renderInstancesComparisonChart - Canvas element není v DOM, zkusím později');\n\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderInstancesComparisonChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n          if (this.instancesComparisonChartRef && document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n            this.renderInstancesComparisonChart(data);\n          } else {\n            console.error('renderInstancesComparisonChart - Canvas element stále není v DOM po 2000ms!');\n          }\n        }, 2000);\n\n        return;\n      }\n\n      // Získání kontextu canvas elementu\n      const ctx = this.instancesComparisonChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        console.error('renderInstancesComparisonChart - Nepodařilo se získat kontext canvas elementu!');\n        return;\n      }\n      console.log('renderInstancesComparisonChart - Kontext canvas elementu je k dispozici');\n\n      if (this.instancesComparisonChart) {\n        console.log('renderInstancesComparisonChart - Ničím existující graf');\n        this.instancesComparisonChart.destroy();\n      }\n\n    try {\n      console.log('renderInstancesComparisonChart - Vytvářím nový graf');\n\n      // Kontrola, zda je canvas element stále platný\n      if (!this.instancesComparisonChartRef || !document.body.contains(this.instancesComparisonChartRef.nativeElement)) {\n        console.error('renderInstancesComparisonChart - Canvas element již není platný!');\n        return;\n      }\n\n      // Kontrola, zda má canvas element rozměry\n      const width = this.instancesComparisonChartRef.nativeElement.width;\n      const height = this.instancesComparisonChartRef.nativeElement.height;\n      console.log('renderInstancesComparisonChart - Canvas rozměry:', { width, height });\n\n      // Načtení nastavení viditelnosti metrik z localStorage\n      const visibility = this.loadMetricsVisibility('instances-comparison');\n\n      // Aplikace nastavení viditelnosti na datasety\n      if (visibility && data.datasets) {\n        data.datasets.forEach((dataset: any) => {\n          if (dataset.label && visibility[dataset.label] !== undefined) {\n            dataset.hidden = !visibility[dataset.label];\n          }\n        });\n      }\n\n      // Uložení originálních labels pro tooltip\n      const originalLabels = [...data.labels];\n\n      // Konfigurace pro zkrácené popisky na ose X\n      const truncatedLabelsConfig = this.textUtils.createTruncatedLabelsConfig(originalLabels, {\n        enabled: true,        // Zapnout zkrácení popisků\n        maxLength: 30,        // Maximální délka popisku\n        showFullInTooltip: true // Zobrazit plný název v tooltip\n      });\n\n      // Základní konfigurace grafu\n      const baseOptions = {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          title: {\n            display: true,\n            text: data.title || 'Srovnání výkonu metod mezi instancemi'\n          },\n          tooltip: {\n            mode: 'index',\n            intersect: false\n          },\n          legend: {\n            position: 'top',\n            labels: {\n              boxWidth: 12\n            },\n            onClick: (_e: any, legendItem: any, legend: any) => {\n              // Standardní chování - přepnutí viditelnosti datasetu\n              const index = legendItem.datasetIndex;\n              const ci = legend.chart;\n              if (index !== undefined) {\n                if (ci.isDatasetVisible(index)) {\n                  ci.hide(index);\n                  legendItem.hidden = true;\n                } else {\n                  ci.show(index);\n                  legendItem.hidden = false;\n                }\n\n                // Počkáme, až se graf aktualizuje\n                setTimeout(() => {\n                  // Uložení nastavení viditelnosti do localStorage\n                  this.saveMetricsVisibility('instances-comparison', ci.data.datasets);\n                }, 0);\n\n                // Překreslení grafu\n                ci.update();\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Metoda'\n            }\n          },\n          y: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Doba odezvy (ms)'\n            },\n            beginAtZero: true\n          }\n        }\n      };\n\n      // Sloučení s konfigurací pro zkrácené popisky\n      const finalOptions = this.textUtils.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n\n      // Vytvoření nového grafu\n      this.instancesComparisonChart = new Chart(ctx, {\n        type: 'bar',\n        data: {\n          labels: data.labels,\n          datasets: data.datasets\n        },\n        options: finalOptions\n      });\n      console.log('renderInstancesComparisonChart - Graf úspěšně vytvořen');\n\n      // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n      setTimeout(() => {\n        if (this.instancesComparisonChart) {\n          this.saveMetricsVisibility('instances-comparison', this.instancesComparisonChart.data.datasets);\n        }\n        // Ukončíme vykreslování\n        this.renderingInstancesComparisonChart = false;\n      }, 100);\n    } catch (error) {\n      console.error('renderInstancesComparisonChart - Chyba při vytváření grafu:', error);\n      this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování v případě chyby\n    }\n  } catch (error) {\n    console.error('renderInstancesComparisonChart - Chyba při získávání kontextu canvas elementu:', error);\n    this.renderingInstancesComparisonChart = false; // Ukončíme vykreslování v případě chyby\n  }\n  }\n\n  /**\n   * Vykreslení grafu srovnání výkonu metod mezi verzemi\n   */\n  renderVersionsComparisonChart(data: any): void {\n    console.log('renderVersionsComparisonChart - data:', data);\n\n    // Nastavíme příznak, že se graf vykresluje\n    this.renderingVersionsComparisonChart = true;\n\n    // Uložíme data pro pozdější vykreslení\n    this.versionsComparisonData = data;\n\n    // Kontrola, zda jsou data prázdná\n    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0 &&\n                    data.datasets && Array.isArray(data.datasets) && data.datasets.length > 0;\n\n    if (!hasData) {\n      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n      this.hasVersionsComparisonChartData = false;\n      this.renderingVersionsComparisonChart = false;\n\n      // Zničíme existující graf, pokud existuje\n      if (this.versionsComparisonChart) {\n        this.versionsComparisonChart.destroy();\n        this.versionsComparisonChart = null;\n      }\n\n      return;\n    }\n\n    // Pokud máme data, nastavíme indikátor\n    this.hasVersionsComparisonChartData = true;\n\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.versionsComparisonChartRef) {\n      console.warn('renderVersionsComparisonChart - Reference na canvas element není k dispozici, zkusím později');\n\n      // Zkusíme počkat a zkusit to znovu po delším timeoutu\n      setTimeout(() => {\n        console.log('renderVersionsComparisonChart - Zkouším znovu po delším timeoutu (2000ms)');\n        if (this.versionsComparisonChartRef) {\n          this.renderVersionsComparisonChart(data);\n        } else {\n          console.error('renderVersionsComparisonChart - Canvas element stále není k dispozici po 2000ms!');\n          this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n        }\n      }, 2000);\n\n      return;\n    }\n\n    console.log('renderVersionsComparisonChart - Reference na canvas element je k dispozici');\n\n    try {\n      // Kontrola, zda je canvas element v DOM\n      if (!document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n        console.warn('renderVersionsComparisonChart - Canvas element není v DOM, zkusím později');\n\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderVersionsComparisonChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n          if (this.versionsComparisonChartRef && document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n            this.renderVersionsComparisonChart(data);\n          } else {\n            console.error('renderVersionsComparisonChart - Canvas element stále není v DOM po 2000ms!');\n          }\n        }, 2000);\n\n        return;\n      }\n\n      // Získání kontextu canvas elementu\n      const ctx = this.versionsComparisonChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        console.error('renderVersionsComparisonChart - Nepodařilo se získat kontext canvas elementu!');\n        return;\n      }\n      console.log('renderVersionsComparisonChart - Kontext canvas elementu je k dispozici');\n\n      if (this.versionsComparisonChart) {\n        console.log('renderVersionsComparisonChart - Ničím existující graf');\n        this.versionsComparisonChart.destroy();\n      }\n\n    try {\n      console.log('renderVersionsComparisonChart - Vytvářím nový graf');\n\n      // Kontrola, zda je canvas element stále platný\n      if (!this.versionsComparisonChartRef || !document.body.contains(this.versionsComparisonChartRef.nativeElement)) {\n        console.error('renderVersionsComparisonChart - Canvas element již není platný!');\n        return;\n      }\n\n      // Kontrola, zda má canvas element rozměry\n      const width = this.versionsComparisonChartRef.nativeElement.width;\n      const height = this.versionsComparisonChartRef.nativeElement.height;\n      console.log('renderVersionsComparisonChart - Canvas rozměry:', { width, height });\n\n      // Načtení nastavení viditelnosti metrik z localStorage\n      const visibility = this.loadMetricsVisibility('versions-comparison');\n\n      // Aplikace nastavení viditelnosti na datasety\n      if (visibility && data.datasets) {\n        data.datasets.forEach((dataset: any) => {\n          if (dataset.label && visibility[dataset.label] !== undefined) {\n            dataset.hidden = !visibility[dataset.label];\n          }\n        });\n      }\n\n      // Uložení originálních labels pro tooltip\n      const originalLabels = [...data.labels];\n\n      // Konfigurace pro zkrácené popisky na ose X\n      const truncatedLabelsConfig = this.textUtils.createTruncatedLabelsConfig(originalLabels, {\n        enabled: true,        // Zapnout zkrácení popisků\n        maxLength: 30,        // Maximální délka popisku\n        showFullInTooltip: true // Zobrazit plný název v tooltip\n      });\n\n      // Základní konfigurace grafu\n      const baseOptions = {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          title: {\n            display: true,\n            text: data.title || 'Srovnání výkonu metod mezi verzemi'\n          },\n          tooltip: {\n            mode: 'index',\n            intersect: false\n          },\n          legend: {\n            position: 'top',\n            labels: {\n              boxWidth: 12\n            },\n            onClick: (_e: any, legendItem: any, legend: any) => {\n              // Standardní chování - přepnutí viditelnosti datasetu\n              const index = legendItem.datasetIndex;\n              const ci = legend.chart;\n              if (index !== undefined) {\n                if (ci.isDatasetVisible(index)) {\n                  ci.hide(index);\n                  legendItem.hidden = true;\n                } else {\n                  ci.show(index);\n                  legendItem.hidden = false;\n                }\n\n                // Počkáme, až se graf aktualizuje\n                setTimeout(() => {\n                  // Uložení nastavení viditelnosti do localStorage\n                  this.saveMetricsVisibility('versions-comparison', ci.data.datasets);\n                }, 0);\n\n                // Překreslení grafu\n                ci.update();\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Metoda'\n            }\n          },\n          y: {\n            display: true,\n            title: {\n              display: true,\n              text: 'Doba odezvy (ms)'\n            },\n            beginAtZero: true\n          }\n        }\n      };\n\n      // Sloučení s konfigurací pro zkrácené popisky\n      const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n\n      // Vytvoření nového grafu\n      this.versionsComparisonChart = new Chart(ctx, {\n        type: 'bar',\n        data: {\n          labels: data.labels,\n          datasets: data.datasets\n        },\n        options: finalOptions\n      });\n      console.log('renderVersionsComparisonChart - Graf úspěšně vytvořen');\n\n      // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n      setTimeout(() => {\n        if (this.versionsComparisonChart) {\n          this.saveMetricsVisibility('versions-comparison', this.versionsComparisonChart.data.datasets);\n        }\n        // Ukončíme vykreslování\n        this.renderingVersionsComparisonChart = false;\n      }, 100);\n    } catch (error) {\n      console.error('renderVersionsComparisonChart - Chyba při vytváření grafu:', error);\n      this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování v případě chyby\n    }\n  } catch (error) {\n    console.error('renderVersionsComparisonChart - Chyba při získávání kontextu canvas elementu:', error);\n    this.renderingVersionsComparisonChart = false; // Ukončíme vykreslování v případě chyby\n  }\n  }\n\n  /**\n   * Vykreslení grafu detailu metody\n   */\n  renderMethodDetailChart(data: any): void {\n    console.log('renderMethodDetailChart - data:', data);\n\n    // Nastavíme příznak, že se graf vykresluje\n    this.renderingMethodDetailChart = true;\n\n    // Uložíme data pro pozdější vykreslení\n    this.methodDetailChartData = data;\n\n    // Kontrola, zda jsou data prázdná\n    const hasData = data && data.labels && Array.isArray(data.labels) && data.labels.length > 0;\n\n    if (!hasData) {\n      // Pokud nejsou žádná data, nastavíme indikátor a ukončíme vykreslování\n      this.hasMethodDetailChartData = false;\n      this.renderingMethodDetailChart = false;\n\n      // Zničíme existující graf, pokud existuje\n      if (this.methodDetailChart) {\n        this.methodDetailChart.destroy();\n        this.methodDetailChart = null;\n      }\n\n      return;\n    }\n\n    // Aktualizace hodnot filtru podle dat grafu, pokud jsou k dispozici\n    if (data.className && data.methodName) {\n      console.log('renderMethodDetailChart - Aktualizuji hodnoty filtru podle dat grafu:', {\n        className: data.className,\n        methodName: data.methodName\n      });\n\n      // Aktualizace pouze pokud se hodnoty liší\n      if (this.methodDetailFilterData.className !== data.className ||\n          this.methodDetailFilterData.methodName !== data.methodName) {\n\n        this.methodDetailFilterData.className = data.className;\n        this.methodDetailFilterData.methodName = data.methodName;\n\n        // Uložení filtru do localStorage\n        this.saveFilter('performance_method_detail', this.methodDetailFilterData);\n      }\n    }\n\n    // Pokud máme data, nastavíme indikátor\n    this.hasMethodDetailChartData = true;\n\n    // Kontrola, zda je reference na canvas element k dispozici\n    if (!this.methodDetailChartRef) {\n      console.warn('renderMethodDetailChart - Reference na canvas element není k dispozici, zkusím později');\n\n      // Zkusíme počkat a zkusit to znovu po delším timeoutu\n      setTimeout(() => {\n        console.log('renderMethodDetailChart - Zkouším znovu po delším timeoutu (2000ms)');\n        if (this.methodDetailChartRef) {\n          this.renderMethodDetailChart(data);\n        } else {\n          console.error('renderMethodDetailChart - Canvas element stále není k dispozici po 2000ms!');\n          this.renderingMethodDetailChart = false; // Ukončíme vykreslování, pokud se nepodařilo získat referenci\n        }\n      }, 2000);\n\n      return;\n    }\n\n    console.log('renderMethodDetailChart - Reference na canvas element je k dispozici');\n\n    try {\n      // Kontrola, zda je canvas element v DOM\n      if (!document.body.contains(this.methodDetailChartRef.nativeElement)) {\n        console.warn('renderMethodDetailChart - Canvas element není v DOM, zkusím později');\n\n        // Zkusíme počkat a zkusit to znovu po delším timeoutu\n        setTimeout(() => {\n          console.log('renderMethodDetailChart - Zkouším znovu po delším timeoutu (element není v DOM)');\n          if (this.methodDetailChartRef && document.body.contains(this.methodDetailChartRef.nativeElement)) {\n            this.renderMethodDetailChart(data);\n          } else {\n            console.error('renderMethodDetailChart - Canvas element stále není v DOM po 2000ms!');\n          }\n        }, 2000);\n\n        return;\n      }\n\n      // Získání kontextu canvas elementu\n      const ctx = this.methodDetailChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        console.error('renderMethodDetailChart - Nepodařilo se získat kontext canvas elementu!');\n        return;\n      }\n      console.log('renderMethodDetailChart - Kontext canvas elementu je k dispozici');\n\n      if (this.methodDetailChart) {\n        console.log('renderMethodDetailChart - Ničím existující graf');\n        this.methodDetailChart.destroy();\n      }\n\n    try {\n      console.log('renderMethodDetailChart - Vytvářím nový graf');\n\n      // Kontrola, zda je canvas element stále platný\n      if (!this.methodDetailChartRef || !document.body.contains(this.methodDetailChartRef.nativeElement)) {\n        console.error('renderMethodDetailChart - Canvas element již není platný!');\n        return;\n      }\n\n      // Kontrola, zda má canvas element rozměry\n      const width = this.methodDetailChartRef.nativeElement.width;\n      const height = this.methodDetailChartRef.nativeElement.height;\n      console.log('renderMethodDetailChart - Canvas rozměry:', { width, height });\n\n      // Kontrola, zda jsou všechna pole dat stejné délky\n      if (hasData) {\n        const labelsLength = data.labels.length;\n\n        // Kontrola existence a délky polí dat\n        if (!data.avgData || data.avgData.length !== labelsLength) {\n          console.warn('renderMethodDetailChart - avgData má jinou délku než labels, upravuji');\n          data.avgData = this.adjustArrayLength(data.avgData || [], labelsLength);\n        }\n        if (!data.medianData || data.medianData.length !== labelsLength) {\n          console.warn('renderMethodDetailChart - medianData má jinou délku než labels, upravuji');\n          data.medianData = this.adjustArrayLength(data.medianData || [], labelsLength);\n        }\n        if (!data.p95Data || data.p95Data.length !== labelsLength) {\n          console.warn('renderMethodDetailChart - p95Data má jinou délku než labels, upravuji');\n          data.p95Data = this.adjustArrayLength(data.p95Data || [], labelsLength);\n        }\n        if (!data.totalCountData || data.totalCountData.length !== labelsLength) {\n          console.warn('renderMethodDetailChart - totalCountData má jinou délku než labels, upravuji');\n          data.totalCountData = this.adjustArrayLength(data.totalCountData || [], labelsLength);\n        }\n      }\n\n      // Načtení nastavení viditelnosti metrik z localStorage\n      const visibility = this.loadMetricsVisibility('method-detail');\n\n      // Vytvoření datasetů s nastavením viditelnosti\n      const datasets = [\n        {\n          label: 'Průměr (ms)',\n          data: data.avgData,\n          borderColor: 'rgba(54, 162, 235, 1)',\n          backgroundColor: 'rgba(54, 162, 235, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Průměr (ms)'] : false\n        },\n        {\n          label: 'Medián (ms)',\n          data: data.medianData,\n          borderColor: 'rgba(75, 192, 192, 1)',\n          backgroundColor: 'rgba(75, 192, 192, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Medián (ms)'] : false\n        },\n        {\n          label: '95. percentil (ms)',\n          data: data.p95Data,\n          borderColor: 'rgba(255, 99, 132, 1)',\n          backgroundColor: 'rgba(255, 99, 132, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['95. percentil (ms)'] : false\n        },\n        {\n          label: 'Počet volání',\n          data: data.totalCountData,\n          borderColor: 'rgba(201, 203, 207, 1)',\n          backgroundColor: 'rgba(201, 203, 207, 0.2)',\n          borderWidth: 2,\n          tension: 0.3,\n          fill: false,\n          hidden: visibility ? !visibility['Počet volání'] : false,\n          yAxisID: 'y1'\n        }\n      ];\n\n      // Vytvoření nového grafu\n      this.methodDetailChart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: data.labels,\n          datasets: datasets\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          plugins: {\n            title: {\n              display: true,\n              text: data.title || 'Detail metody'\n            },\n            tooltip: {\n              mode: 'index',\n              intersect: false\n            },\n            legend: {\n              position: 'top',\n              labels: {\n                boxWidth: 12\n              },\n              onClick: (_e, legendItem, legend) => {\n                // Standardní chování - přepnutí viditelnosti datasetu\n                const index = legendItem.datasetIndex;\n                const ci = legend.chart;\n                if (index !== undefined) {\n                  if (ci.isDatasetVisible(index)) {\n                    ci.hide(index);\n                    legendItem.hidden = true;\n                  } else {\n                    ci.show(index);\n                    legendItem.hidden = false;\n                  }\n\n                  // Počkáme, až se graf aktualizuje\n                  setTimeout(() => {\n                    // Uložení nastavení viditelnosti do localStorage\n                    this.saveMetricsVisibility('method-detail', ci.data.datasets);\n                  }, 0);\n\n                  // Překreslení grafu\n                  ci.update();\n                }\n              }\n            }\n          },\n          scales: {\n            x: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Datum'\n              }\n            },\n            y: {\n              display: true,\n              title: {\n                display: true,\n                text: 'Doba odezvy (ms)'\n              },\n              beginAtZero: true\n            },\n            y1: {\n              display: true,\n              position: 'right',\n              title: {\n                display: true,\n                text: 'Počet volání'\n              },\n              beginAtZero: true,\n              grid: {\n                drawOnChartArea: false\n              }\n            }\n          }\n        }\n      });\n      console.log('renderMethodDetailChart - Graf úspěšně vytvořen');\n\n      // Uložení aktuálního nastavení viditelnosti metrik do localStorage\n      setTimeout(() => {\n        if (this.methodDetailChart) {\n          this.saveMetricsVisibility('method-detail', this.methodDetailChart.data.datasets);\n        }\n        // Ukončíme vykreslování\n        this.renderingMethodDetailChart = false;\n      }, 100);\n    } catch (error) {\n      console.error('renderMethodDetailChart - Chyba při vytváření grafu:', error);\n      this.renderingMethodDetailChart = false; // Ukončíme vykreslování v případě chyby\n    }\n  } catch (error) {\n    console.error('renderMethodDetailChart - Chyba při získávání kontextu canvas elementu:', error);\n    this.renderingMethodDetailChart = false; // Ukončíme vykreslování v případě chyby\n  }\n  }\n\n\n\n\n\n  /**\n   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu\n   * @param baseConfig Základní konfigurace grafu\n   * @param truncatedConfig Konfigurace pro zkrácené popisky\n   * @returns Sloučená konfigurace\n   */\n  private mergeChartConfig(baseConfig: any, truncatedConfig: any): any {\n    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {\n      return baseConfig;\n    }\n\n    const merged = { ...baseConfig };\n\n    // Sloučení scales\n    if (truncatedConfig.scales) {\n      merged.scales = merged.scales || {};\n      if (truncatedConfig.scales.x) {\n        merged.scales.x = merged.scales.x || {};\n        merged.scales.x.ticks = {\n          ...merged.scales.x.ticks,\n          ...truncatedConfig.scales.x.ticks\n        };\n      }\n    }\n\n    // Sloučení plugins\n    if (truncatedConfig.plugins) {\n      merged.plugins = merged.plugins || {};\n      if (truncatedConfig.plugins.tooltip) {\n        merged.plugins.tooltip = merged.plugins.tooltip || {};\n        merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};\n\n        // Sloučení tooltip callbacks - zachováme existující a přidáme nové\n        Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {\n          merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];\n        });\n      }\n    }\n\n    return merged;\n  }\n\n  // ========================================\n  // PŘÍKLAD POUŽITÍ ZKRÁCENÝCH POPISKŮ:\n  // ========================================\n  //\n  // // 1. Vytvořte konfiguraci pro zkrácené popisky\n  // const truncatedLabelsConfig = this.createTruncatedLabelsConfig(data.labels, {\n  //   enabled: true,           // Zapnout zkrácení\n  //   maxLength: 25,          // Max 25 znaků\n  //   showFullInTooltip: true // Plný název v tooltip\n  // });\n  //\n  // // 2. Vytvořte základní konfiguraci grafu\n  // const baseOptions = {\n  //   responsive: true,\n  //   plugins: { /* vaše plugins */ },\n  //   scales: { /* vaše scales */ }\n  // };\n  //\n  // // 3. Sloučte konfigurace\n  // const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);\n  //\n  // // 4. Vytvořte graf\n  // new Chart(ctx, {\n  //   type: 'bar',\n  //   data: { labels: data.labels, datasets: data.datasets },\n  //   options: finalOptions\n  // });\n  //\n  // ========================================\n\n  /**\n   * Příprava dat pro graf agregovaných výkonnostních metrik\n   */\n  private prepareAggregatedPerformanceChartData(data: any): any {\n    console.log('prepareAggregatedPerformanceChartData - vstupní data:', data);\n\n    // Výchozí prázdná struktura pro data grafu\n    const chartData: {\n      labels: string[];\n      avgData: number[];\n      medianData: number[];\n      p95Data: number[];\n      p99Data: number[];\n      minData: number[];\n      maxData: number[];\n      totalCountData: number[];\n      title: string;\n    } = {\n      labels: [],\n      avgData: [],\n      medianData: [],\n      p95Data: [],\n      p99Data: [],\n      minData: [],\n      maxData: [],\n      totalCountData: [],\n      title: 'Výkon v čase'\n    };\n\n    // Kontrola, zda data obsahují potřebné údaje\n    if (!data) {\n      console.warn('prepareAggregatedPerformanceChartData - Chybí data pro graf');\n      return chartData;\n    }\n\n    try {\n      // Pokud jsou data již připravena v požadovaném formátu z API\n      if (data.labels && Array.isArray(data.labels) && data.labels.length > 0) {\n        console.log('prepareAggregatedPerformanceChartData - Používám předpřipravená data z API');\n        chartData.labels = data.labels;\n        chartData.avgData = data.avgData || [];\n        chartData.medianData = data.medianData || [];\n        chartData.p95Data = data.p95Data || [];\n        chartData.p99Data = data.p99Data || [];\n        chartData.minData = data.minData || [];\n        chartData.maxData = data.maxData || [];\n        chartData.totalCountData = data.totalCountData || [];\n\n        // Nastavení titulku grafu\n        if (data.title) {\n          chartData.title = data.title;\n        }\n      }\n      // Pokud jsou data ve formátu denních dat\n      else if (data.dailyData && Array.isArray(data.dailyData) && data.dailyData.length > 0) {\n        console.log('prepareAggregatedPerformanceChartData - Zpracovávám denní data');\n        const dailyData = data.dailyData;\n\n        // Seřazení dat podle data\n        dailyData.sort((a: any, b: any) => {\n          const dateA = a.date ? new Date(a.date).getTime() : 0;\n          const dateB = b.date ? new Date(b.date).getTime() : 0;\n          return dateA - dateB;\n        });\n\n        // Naplnění polí pro graf\n        chartData.labels = dailyData.map((item: any) => {\n          if (item.date) {\n            const date = new Date(item.date);\n            return this.formatDate(date);\n          }\n          return '';\n        }).filter((label: string) => label !== '');\n\n        // Mapování dat s kontrolou existence vlastností\n        chartData.avgData = dailyData.map((item: any) => item.avg !== undefined ? item.avg : 0);\n        chartData.medianData = dailyData.map((item: any) => item.median !== undefined ? item.median : 0);\n        chartData.p95Data = dailyData.map((item: any) => item.percentil95 !== undefined ? item.percentil95 : 0);\n        chartData.p99Data = dailyData.map((item: any) => item.percentil99 !== undefined ? item.percentil99 : 0);\n        chartData.minData = dailyData.map((item: any) => item.min !== undefined ? item.min : 0);\n        chartData.maxData = dailyData.map((item: any) => item.max !== undefined ? item.max : 0);\n        chartData.totalCountData = dailyData.map((item: any) => item.totalCount !== undefined ? item.totalCount : 0);\n\n        // Nastavení titulku grafu\n        if (data.title) {\n          chartData.title = data.title;\n        }\n      }\n      else {\n        console.warn('prepareAggregatedPerformanceChartData - Neznámý formát dat');\n\n        // Pokus o extrakci dat z neznámého formátu\n        if (typeof data === 'object') {\n          // Pokus o nalezení polí s daty\n          for (const key in data) {\n            if (key === 'labels' && Array.isArray(data[key])) {\n              chartData.labels = data[key].map((item: any) => String(item));\n            } else if (key === 'avgData' && Array.isArray(data[key])) {\n              chartData.avgData = data[key].map((item: any) => Number(item) || 0);\n            } else if (key === 'medianData' && Array.isArray(data[key])) {\n              chartData.medianData = data[key].map((item: any) => Number(item) || 0);\n            } else if (key === 'p95Data' && Array.isArray(data[key])) {\n              chartData.p95Data = data[key].map((item: any) => Number(item) || 0);\n            } else if (key === 'p99Data' && Array.isArray(data[key])) {\n              chartData.p99Data = data[key].map((item: any) => Number(item) || 0);\n            } else if (key === 'minData' && Array.isArray(data[key])) {\n              chartData.minData = data[key].map((item: any) => Number(item) || 0);\n            } else if (key === 'maxData' && Array.isArray(data[key])) {\n              chartData.maxData = data[key].map((item: any) => Number(item) || 0);\n            } else if (key === 'totalCountData' && Array.isArray(data[key])) {\n              chartData.totalCountData = data[key].map((item: any) => Number(item) || 0);\n            } else if (key === 'title' && typeof data[key] === 'string') {\n              chartData.title = data[key];\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.error('prepareAggregatedPerformanceChartData - Chyba při zpracování dat:', error);\n    }\n\n    console.log('prepareAggregatedPerformanceChartData - výstupní data:', chartData);\n    return chartData;\n  }\n\n  /**\n   * Inicializace popoverů pro nápovědu\n   */\n  private initPopovers(): void {\n    console.log('initPopovers - Inicializuji popovery');\n\n    // Definice obsahu nápověd\n    const helpContent: Record<string, string> = {\n      'aggregated-performance': 'Graf zobrazuje agregované výkonnostní metriky DIS metod v čase. ' +\n        'Pro každý den jsou zobrazeny hodnoty jako průměr, medián, 95. percentil, 99. percentil, minimum, maximum a počet volání. ' +\n        'Kliknutím na legendu můžete zobrazit nebo skrýt jednotlivé metriky. ' +\n        'Výchozí zobrazení obsahuje průměr, medián a 95. percentil, ostatní metriky jsou skryté.',\n      'instances-comparison': 'Graf umožňuje srovnání výkonu stejných DIS metod mezi různými instancemi. ' +\n        'Vyberte alespoň dvě instance, které chcete porovnat, a volitelně filtrujte podle konkrétní třídy nebo metody. ' +\n        'Bez filtrace se zobrazují pouze metody s významnou odezvou (alespoň u jedné instance větší než průměrná odezva DIS metod). ' +\n        'Graf zobrazuje průměrnou dobu odezvy metod pro každou vybranou instanci.',\n      'versions-comparison': 'Graf umožňuje srovnání výkonu DIS metod mezi různými verzemi jedné instance. ' +\n        'Vyberte instanci a volitelně filtrujte podle konkrétní třídy nebo metody. ' +\n        'Bez filtrace se zobrazují pouze metody s významnou odezvou (alespoň u jedné verze větší než průměrná odezva DIS metod). ' +\n        'Graf zobrazuje průměrnou dobu odezvy metod pro každou verzi aplikace v rámci vybrané instance.',\n      'slowest-methods': 'Tabulka zobrazuje nejpomalejší DIS metody seřazené podle 95. percentilu doby odezvy. ' +\n        'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' +\n        'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n      'methods': 'Tabulka zobrazuje DIS metody podle zvoleného filtru (nejpomalejší, nejčastěji volané nebo všechny). ' +\n        'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' +\n        'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n      'most-called-methods': 'Tabulka zobrazuje nejčastěji volané DIS metody seřazené podle počtu volání. ' +\n        'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' +\n        'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n      'all-methods': 'Tabulka zobrazuje všechny DIS metody. ' +\n        'Pro každou metodu jsou zobrazeny statistické údaje jako počet volání, minimální, maximální a průměrná doba odezvy, ' +\n        'medián a 95. percentil. Kliknutím na tlačítko s ikonou informace zobrazíte detail konkrétní metody.',\n      'method-detail': 'Graf zobrazuje výkon konkrétní DIS metody v čase. ' +\n        'Pro každý den jsou zobrazeny hodnoty jako průměr, medián, 95. percentil, 99. percentil, minimum, maximum a počet volání. ' +\n        'Kliknutím na legendu můžete zobrazit nebo skrýt jednotlivé metriky. ' +\n        'Nad grafem jsou zobrazeny souhrnné statistiky metody za celé zvolené období.',\n      'avg-response-time': 'Průměrná doba odezvy metody v milisekundách za celé zvolené období. ' +\n        'Průměr je vypočítán jako aritmetický průměr všech naměřených hodnot. ' +\n        'Může být ovlivněn extrémními hodnotami, proto je vhodné sledovat také medián.',\n      'median-response-time': 'Medián doby odezvy metody v milisekundách za celé zvolené období. ' +\n        'Medián představuje prostřední hodnotu ze všech naměřených časů (50. percentil). ' +\n        'Polovina volání byla rychlejší a polovina pomalejší než tato hodnota. ' +\n        'Medián není ovlivněn extrémními hodnotami, proto lépe reprezentuje typickou dobu odezvy než průměr.',\n      'p95-response-time': '95. percentil doby odezvy metody v milisekundách za celé zvolené období. ' +\n        'Tato hodnota znamená, že 95% všech volání metody bylo rychlejších než tato hodnota. ' +\n        'Zbývajících 5% volání bylo pomalejších. ' +\n        'Tento ukazatel je důležitý pro identifikaci problémů s výkonem, které ovlivňují menší část uživatelů.',\n      'total-calls': 'Celkový počet volání metody za zvolené časové období. ' +\n        'Tento údaj ukazuje, jak často je metoda používána. ' +\n        'Vysoký počet volání v kombinaci s delší dobou odezvy může indikovat potenciální problém s výkonem aplikace.'\n    };\n\n    try {\n      // Inicializace popoverů pomocí Bootstrap API\n      setTimeout(() => {\n        try {\n          // Kontrola, zda je bootstrap k dispozici\n          if (typeof bootstrap === 'undefined') {\n            console.error('initPopovers - Bootstrap není k dispozici!');\n            return;\n          }\n\n          // Kontrola, zda je Popover k dispozici\n          if (typeof bootstrap.Popover === 'undefined') {\n            console.error('initPopovers - Bootstrap.Popover není k dispozici!');\n            return;\n          }\n\n          // Nejprve zrušíme všechny existující popovery\n          const existingPopovers = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\n          existingPopovers.forEach((el: any) => {\n            try {\n              // @ts-ignore\n              const popover = bootstrap.Popover.getInstance(el);\n              if (popover) {\n                popover.dispose();\n              }\n            } catch (error) {\n              console.warn('initPopovers - Chyba při rušení existujícího popoveru:', error);\n            }\n          });\n\n          // Nyní inicializujeme nové popovery\n          const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\n          console.log('initPopovers - Nalezeno', popoverTriggerList.length, 'elementů s popovery');\n\n          popoverTriggerList.forEach((popoverTriggerEl: any) => {\n            try {\n              const helpType = popoverTriggerEl.getAttribute('data-help-type');\n              const content = helpContent[helpType] || 'Nápověda není k dispozici.';\n\n              // Přidáme titulek popoveru\n              let title = '';\n              switch (helpType) {\n                case 'aggregated-performance':\n                  title = 'Agregovaný výkon v čase';\n                  break;\n                case 'instances-comparison':\n                  title = 'Srovnání výkonu metod mezi instancemi';\n                  break;\n                case 'versions-comparison':\n                  title = 'Srovnání výkonu metod mezi verzemi';\n                  break;\n                case 'slowest-methods':\n                  title = 'Nejpomalejší metody';\n                  break;\n                case 'most-called-methods':\n                  title = 'Nejčastěji volané metody';\n                  break;\n                case 'all-methods':\n                  title = 'Všechny metody';\n                  break;\n                case 'method-detail':\n                  title = 'Detail metody';\n                  break;\n                case 'avg-response-time':\n                  title = 'Průměrná doba odezvy';\n                  break;\n                case 'median-response-time':\n                  title = 'Medián doby odezvy';\n                  break;\n                case 'p95-response-time':\n                  title = '95. percentil';\n                  break;\n                case 'total-calls':\n                  title = 'Celkový počet volání';\n                  break;\n                default:\n                  title = 'Nápověda';\n              }\n\n              // @ts-ignore\n              new bootstrap.Popover(popoverTriggerEl, {\n                container: 'body',\n                trigger: 'hover',\n                placement: 'top',\n                title: title,\n                content: content,\n                html: true\n              });\n            } catch (error) {\n              console.error('initPopovers - Chyba při inicializaci popoveru:', error);\n            }\n          });\n\n          console.log('initPopovers - Popovery úspěšně inicializovány');\n        } catch (error) {\n          console.error('initPopovers - Chyba při inicializaci popoverů:', error);\n        }\n      }, 500);\n    } catch (error) {\n      console.error('initPopovers - Chyba při inicializaci popoverů:', error);\n    }\n  }\n\n\n\n  /**\n   * Formátování data\n   */\n  formatDate(date: Date): string {\n    return this.datePipe.transform(date, 'dd.MM.yyyy') || '';\n  }\n\n  /**\n   * Úprava délky pole na požadovanou délku\n   * @param array Pole, které chceme upravit\n   * @param length Požadovaná délka pole\n   * @returns Upravené pole s požadovanou délkou\n   */\n  private adjustArrayLength(array: any[], length: number): any[] {\n    if (!array) {\n      return new Array(length).fill(0);\n    }\n\n    if (array.length === length) {\n      return array;\n    }\n\n    if (array.length > length) {\n      return array.slice(0, length);\n    }\n\n    // Pokud je pole kratší než požadovaná délka, doplníme ho nulami\n    const result = [...array];\n    while (result.length < length) {\n      result.push(0);\n    }\n\n    return result;\n  }\n\n  /**\n   * Určí, které osy Y mají být zobrazené na základě viditelných metrik\n   */\n  private getVisibleAxes(datasets: any[], chart?: any): { showLeftAxis: boolean, showRightAxis: boolean } {\n    let showLeftAxis = false;\n    let showRightAxis = false;\n\n    datasets.forEach((dataset, index) => {\n      // Pokud máme referenci na graf, použijeme Chart.js API pro zjištění viditelnosti\n      const isVisible = chart ? chart.isDatasetVisible(index) : !dataset.hidden;\n\n      if (isVisible) {\n        if (dataset.yAxisID === 'y1') {\n          // Metrika \"Počet volání\" používá pravou osu\n          showRightAxis = true;\n        } else {\n          // Všechny ostatní metriky (doba odezvy) používají levou osu\n          showLeftAxis = true;\n        }\n      }\n    });\n\n    return { showLeftAxis, showRightAxis };\n  }\n\n  /**\n   * Aktualizuje viditelnost os Y na základě aktuálně viditelných metrik\n   */\n  private updateAxisVisibility(chart: any): void {\n    if (!chart || !chart.data || !chart.data.datasets) {\n      return;\n    }\n\n    const visibleAxes = this.getVisibleAxes(chart.data.datasets, chart);\n\n    // Aktualizace konfigurace os\n    if (chart.options && chart.options.scales) {\n      if (chart.options.scales.y) {\n        chart.options.scales.y.display = visibleAxes.showLeftAxis;\n        chart.options.scales.y.title.display = visibleAxes.showLeftAxis;\n      }\n\n      if (chart.options.scales.y1) {\n        chart.options.scales.y1.display = visibleAxes.showRightAxis;\n        chart.options.scales.y1.title.display = visibleAxes.showRightAxis;\n        // Zobrazit mřížku pro pravou osu pouze když levá osa není viditelná\n        chart.options.scales.y1.grid.drawOnChartArea = !visibleAxes.showLeftAxis;\n      }\n    }\n\n    // Překreslení grafu s novými nastaveními\n    chart.update();\n  }\n}\n", "<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2>Výkon DIS</h2>\n  </div>\n\n  <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> -->\n  <app-tab-navigation\n    [tabs]=\"tabs\"\n    [activeTabId]=\"activeTab\"\n    (tabChange)=\"changeTab($event)\">\n  </app-tab-navigation>\n\n  <!-- Chybov<PERSON> hl<PERSON>ška -->\n  <div *ngIf=\"error\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n    {{ error }}\n    <button type=\"button\" class=\"btn-close\" (click)=\"error = null\"></button>\n  </div>\n\n  <!-- Agregovaný výkon -->\n  <div *ngIf=\"activeTab === 'aggregated'\">\n    <!-- Filtr -->\n    <app-advanced-filter\n      [entityType]=\"'performance_aggregated'\"\n      [fields]=\"aggregatedFilterFields\"\n      (filterChange)=\"onAggregatedFilterChange($event)\">\n    </app-advanced-filter>\n\n    <div *ngIf=\"loading\" class=\"text-center my-5\">\n      <div class=\"spinner-border text-primary\" role=\"status\">\n        <span class=\"visually-hidden\">Načítání...</span>\n      </div>\n      <p class=\"mt-2\">Načítání dat...</p>\n    </div>\n\n    <div *ngIf=\"!loading\">\n      <!-- Graf agregovaného výkonu -->\n      <div class=\"card mb-4\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"aggregated-performance\" style=\"cursor: help;\">Agregovaný výkon v čase</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(aggregatedPerformanceChart, 'Agregovaný výkon v čase')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 400px; width: 100%;\">\n            <div *ngIf=\"renderingAggregatedChart\" class=\"chart-loading-overlay\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n              <p class=\"mt-2\">Zpracování dat grafu...</p>\n            </div>\n            <div *ngIf=\"!hasAggregatedChartData\" class=\"no-data-message\">\n              <i class=\"bi bi-bar-chart-line\"></i>\n              <p>Nejsou k dispozici žádná data pro zobrazení grafu</p>\n            </div>\n            <canvas *ngIf=\"hasAggregatedChartData\" #aggregatedPerformanceChart width=\"800\" height=\"400\" style=\"display: block;\"></canvas>\n          </div>\n        </div>\n      </div>\n\n      <!-- Tabulka nejpomalejších metod -->\n      <div class=\"card mb-4\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"slowest-methods\" style=\"cursor: help;\">Nejpomalejší metody</h5>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"table-responsive\">\n            <table class=\"table table-striped table-hover\">\n              <thead class=\"dark-header table-header-override\">\n                <tr class=\"dark-header-row\">\n                  <th (click)=\"onSort('instanceName')\" class=\"sortable-header\">\n                    Instance\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'instanceName' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'instanceName' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'instanceName'\"></i>\n                  </th>\n                  <th (click)=\"onSort('className')\" class=\"sortable-header\">\n                    Třída\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'className' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'className' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'className'\"></i>\n                  </th>\n                  <th (click)=\"onSort('methodName')\" class=\"sortable-header\">\n                    Metoda\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'methodName' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'methodName' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'methodName'\"></i>\n                  </th>\n                  <th (click)=\"onSort('totalCount')\" class=\"sortable-header\">\n                    Počet volání\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'totalCount' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'totalCount' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'totalCount'\"></i>\n                  </th>\n                  <th (click)=\"onSort('min')\" class=\"sortable-header\">\n                    Min (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'min' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'min' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'min'\"></i>\n                  </th>\n                  <th (click)=\"onSort('max')\" class=\"sortable-header\">\n                    Max (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'max' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'max' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'max'\"></i>\n                  </th>\n                  <th (click)=\"onSort('avg')\" class=\"sortable-header\">\n                    Průměr (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'avg' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'avg' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'avg'\"></i>\n                  </th>\n                  <th (click)=\"onSort('median')\" class=\"sortable-header\">\n                    Medián (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'median' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'median' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'median'\"></i>\n                  </th>\n                  <th (click)=\"onSort('percentil95')\" class=\"sortable-header\">\n                    95. percentil (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'percentil95' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'percentil95' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'percentil95'\"></i>\n                  </th>\n                  <th>Akce</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let method of slowestMethods\" style=\"cursor: pointer;\">\n                  <td>{{ method.instanceName }}</td>\n                  <td>{{ method.className }}</td>\n                  <td>{{ method.methodName }}</td>\n                  <td>{{ method.totalCount }}</td>\n                  <td>{{ method.min | number:'1.0-2' }}</td>\n                  <td>{{ method.max | number:'1.0-2' }}</td>\n                  <td>{{ method.avg | number:'1.0-2' }}</td>\n                  <td>{{ method.median | number:'1.0-2' }}</td>\n                  <td>{{ method.percentil95 | number:'1.0-2' }}</td>\n                  <td>\n                    <button class=\"btn btn-sm btn-outline-info\" (click)=\"showMethodDetail(method)\" title=\"Zobrazit detail\">\n                      <i class=\"bi bi-info-circle\"></i>\n                    </button>\n                  </td>\n                </tr>\n                <tr *ngIf=\"!slowestMethods || slowestMethods.length === 0\">\n                  <td colspan=\"10\" class=\"text-center\">Žádné metody k zobrazení</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Srovnání instancí -->\n  <div *ngIf=\"activeTab === 'instances-comparison'\">\n    <!-- Filtr -->\n    <app-advanced-filter\n      [entityType]=\"'performance_instances_comparison'\"\n      [fields]=\"instancesComparisonFilterFields\"\n      (filterChange)=\"onInstancesComparisonFilterChange($event)\">\n    </app-advanced-filter>\n\n    <div *ngIf=\"loading\" class=\"text-center my-5\">\n      <div class=\"spinner-border text-primary\" role=\"status\">\n        <span class=\"visually-hidden\">Načítání...</span>\n      </div>\n      <p class=\"mt-2\">Načítání dat...</p>\n    </div>\n\n    <div *ngIf=\"!loading\">\n      <!-- Graf srovnání instancí -->\n      <div class=\"card mb-4\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"instances-comparison\" style=\"cursor: help;\">Srovnání výkonu metod mezi instancemi</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(instancesComparisonChart, 'Srovnání výkonu metod mezi instancemi')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 400px; width: 100%;\">\n            <div *ngIf=\"renderingInstancesComparisonChart\" class=\"chart-loading-overlay\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n              <p class=\"mt-2\">Zpracování dat grafu...</p>\n            </div>\n            <div *ngIf=\"!hasInstancesComparisonChartData\" class=\"no-data-message\">\n              <i class=\"bi bi-bar-chart-line\"></i>\n              <p>Nejsou k dispozici žádná data pro zobrazení grafu</p>\n            </div>\n            <canvas *ngIf=\"hasInstancesComparisonChartData\" #instancesComparisonChart width=\"800\" height=\"400\" style=\"display: block;\"></canvas>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Srovnání verzí -->\n  <div *ngIf=\"activeTab === 'versions-comparison'\">\n    <!-- Filtr -->\n    <app-advanced-filter\n      [entityType]=\"'performance_versions_comparison'\"\n      [fields]=\"versionsComparisonFilterFields\"\n      (filterChange)=\"onVersionsComparisonFilterChange($event)\">\n    </app-advanced-filter>\n\n    <div *ngIf=\"loading\" class=\"text-center my-5\">\n      <div class=\"spinner-border text-primary\" role=\"status\">\n        <span class=\"visually-hidden\">Načítání...</span>\n      </div>\n      <p class=\"mt-2\">Načítání dat...</p>\n    </div>\n\n    <div *ngIf=\"!loading\">\n      <!-- Graf srovnání verzí -->\n      <div class=\"card mb-4\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"versions-comparison\" style=\"cursor: help;\">Srovnání výkonu metod mezi verzemi</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(versionsComparisonChart, 'Srovnání výkonu metod mezi verzemi')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 400px; width: 100%;\">\n            <div *ngIf=\"renderingVersionsComparisonChart\" class=\"chart-loading-overlay\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n              <p class=\"mt-2\">Zpracování dat grafu...</p>\n            </div>\n            <div *ngIf=\"!hasVersionsComparisonChartData\" class=\"no-data-message\">\n              <i class=\"bi bi-bar-chart-line\"></i>\n              <p>Nejsou k dispozici žádná data pro zobrazení grafu</p>\n            </div>\n            <canvas *ngIf=\"hasVersionsComparisonChartData\" #versionsComparisonChart width=\"800\" height=\"400\" style=\"display: block;\"></canvas>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Metody -->\n  <div *ngIf=\"activeTab === 'methods'\">\n    <!-- Filtr -->\n    <app-advanced-filter\n      [entityType]=\"'performance_methods'\"\n      [fields]=\"methodsFilterFields\"\n      (filterChange)=\"onMethodsFilterChange($event)\">\n    </app-advanced-filter>\n\n    <div *ngIf=\"loading\" class=\"text-center my-5\">\n      <div class=\"spinner-border text-primary\" role=\"status\">\n        <span class=\"visually-hidden\">Načítání...</span>\n      </div>\n      <p class=\"mt-2\">Načítání dat...</p>\n    </div>\n\n    <div *ngIf=\"!loading\">\n      <!-- Tabulka metod -->\n      <div class=\"card mb-4\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" style=\"cursor: help;\">\n            <span *ngIf=\"methodsDisplayType === 'slowest'\" data-bs-toggle=\"popover\" data-help-type=\"slowest-methods\">Nejpomalejší metody</span>\n            <span *ngIf=\"methodsDisplayType === 'most-called'\" data-bs-toggle=\"popover\" data-help-type=\"most-called-methods\">Nejčastěji volané metody</span>\n            <span *ngIf=\"methodsDisplayType === 'all'\" data-bs-toggle=\"popover\" data-help-type=\"all-methods\">Všechny metody</span>\n          </h5>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"table-responsive\">\n            <table class=\"table table-striped table-hover\">\n              <thead class=\"dark-header table-header-override\">\n                <tr class=\"dark-header-row\">\n                  <th (click)=\"onSort('instanceName')\" class=\"sortable-header\">\n                    Instance\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'instanceName' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'instanceName' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'instanceName'\"></i>\n                  </th>\n                  <th (click)=\"onSort('className')\" class=\"sortable-header\">\n                    Třída\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'className' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'className' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'className'\"></i>\n                  </th>\n                  <th (click)=\"onSort('methodName')\" class=\"sortable-header\">\n                    Metoda\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'methodName' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'methodName' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'methodName'\"></i>\n                  </th>\n                  <th (click)=\"onSort('totalCount')\" class=\"sortable-header\">\n                    Počet volání\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'totalCount' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'totalCount' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'totalCount'\"></i>\n                  </th>\n                  <th (click)=\"onSort('min')\" class=\"sortable-header\">\n                    Min (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'min' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'min' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'min'\"></i>\n                  </th>\n                  <th (click)=\"onSort('max')\" class=\"sortable-header\">\n                    Max (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'max' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'max' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'max'\"></i>\n                  </th>\n                  <th (click)=\"onSort('avg')\" class=\"sortable-header\">\n                    Průměr (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'avg' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'avg' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'avg'\"></i>\n                  </th>\n                  <th (click)=\"onSort('median')\" class=\"sortable-header\">\n                    Medián (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'median' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'median' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'median'\"></i>\n                  </th>\n                  <th (click)=\"onSort('percentil95')\" class=\"sortable-header\">\n                    95. percentil (ms)\n                    <i class=\"bi\"\n                      [class.bi-sort-up]=\"sortColumn === 'percentil95' && sortDirection === 'asc'\"\n                      [class.bi-sort-down]=\"sortColumn === 'percentil95' && sortDirection === 'desc'\"\n                      [class.bi-sort]=\"sortColumn !== 'percentil95'\"></i>\n                  </th>\n                  <th>Akce</th>\n                </tr>\n              </thead>\n              <tbody>\n                <ng-container *ngIf=\"methodsDisplayType === 'slowest'\">\n                  <tr *ngFor=\"let method of slowestMethods\" style=\"cursor: pointer;\">\n                    <td>{{ method.instanceName }}</td>\n                    <td>{{ method.className }}</td>\n                    <td>{{ method.methodName }}</td>\n                    <td>{{ method.totalCount }}</td>\n                    <td>{{ method.min | number:'1.0-2' }}</td>\n                    <td>{{ method.max | number:'1.0-2' }}</td>\n                    <td>{{ method.avg | number:'1.0-2' }}</td>\n                    <td>{{ method.median | number:'1.0-2' }}</td>\n                    <td>{{ method.percentil95 | number:'1.0-2' }}</td>\n                    <td>\n                      <button class=\"btn btn-sm btn-outline-info\" (click)=\"showMethodDetail(method)\" title=\"Zobrazit detail\">\n                        <i class=\"bi bi-info-circle\"></i>\n                      </button>\n                    </td>\n                  </tr>\n                  <tr *ngIf=\"!slowestMethods || slowestMethods.length === 0\">\n                    <td colspan=\"10\" class=\"text-center\">Žádné metody k zobrazení</td>\n                  </tr>\n                </ng-container>\n\n                <ng-container *ngIf=\"methodsDisplayType === 'most-called'\">\n                  <tr *ngFor=\"let method of mostCalledMethods\" style=\"cursor: pointer;\">\n                    <td>{{ method.instanceName }}</td>\n                    <td>{{ method.className }}</td>\n                    <td>{{ method.methodName }}</td>\n                    <td>{{ method.totalCount }}</td>\n                    <td>{{ method.min | number:'1.0-2' }}</td>\n                    <td>{{ method.max | number:'1.0-2' }}</td>\n                    <td>{{ method.avg | number:'1.0-2' }}</td>\n                    <td>{{ method.median | number:'1.0-2' }}</td>\n                    <td>{{ method.percentil95 | number:'1.0-2' }}</td>\n                    <td>\n                      <button class=\"btn btn-sm btn-outline-info\" (click)=\"showMethodDetail(method)\" title=\"Zobrazit detail\">\n                        <i class=\"bi bi-info-circle\"></i>\n                      </button>\n                    </td>\n                  </tr>\n                  <tr *ngIf=\"!mostCalledMethods || mostCalledMethods.length === 0\">\n                    <td colspan=\"10\" class=\"text-center\">Žádné metody k zobrazení</td>\n                  </tr>\n                </ng-container>\n\n                <ng-container *ngIf=\"methodsDisplayType === 'all'\">\n                  <tr *ngFor=\"let method of allMethods\" style=\"cursor: pointer;\">\n                    <td>{{ method.instanceName }}</td>\n                    <td>{{ method.className }}</td>\n                    <td>{{ method.methodName }}</td>\n                    <td>{{ method.totalCount }}</td>\n                    <td>{{ method.min | number:'1.0-2' }}</td>\n                    <td>{{ method.max | number:'1.0-2' }}</td>\n                    <td>{{ method.avg | number:'1.0-2' }}</td>\n                    <td>{{ method.median | number:'1.0-2' }}</td>\n                    <td>{{ method.percentil95 | number:'1.0-2' }}</td>\n                    <td>\n                      <button class=\"btn btn-sm btn-outline-info\" (click)=\"showMethodDetail(method)\" title=\"Zobrazit detail\">\n                        <i class=\"bi bi-info-circle\"></i>\n                      </button>\n                    </td>\n                  </tr>\n                  <tr *ngIf=\"!allMethods || allMethods.length === 0\">\n                    <td colspan=\"10\" class=\"text-center\">Žádné metody k zobrazení</td>\n                  </tr>\n                </ng-container>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Detail metody -->\n  <div *ngIf=\"activeTab === 'method-detail'\">\n    <!-- Filtr -->\n    <app-advanced-filter\n      [entityType]=\"'performance_method_detail'\"\n      [fields]=\"methodDetailFilterFields\"\n      (filterChange)=\"onMethodDetailFilterChange($event)\">\n    </app-advanced-filter>\n\n    <div *ngIf=\"loading\" class=\"text-center my-5\">\n      <div class=\"spinner-border text-primary\" role=\"status\">\n        <span class=\"visually-hidden\">Načítání...</span>\n      </div>\n      <p class=\"mt-2\">Načítání dat...</p>\n    </div>\n\n    <div *ngIf=\"!loading && methodDetailData\">\n      <!-- Statistiky metody -->\n      <div class=\"row mb-4\">\n        <div class=\"col-md-3 mb-3\">\n          <div class=\"card h-100\">\n            <div class=\"card-body text-center\">\n              <h5 class=\"card-title\" data-bs-toggle=\"popover\" data-help-type=\"avg-response-time\" style=\"cursor: help;\">Průměrná doba odezvy</h5>\n              <p class=\"display-4 mb-0\">{{ methodDetailData.overallStats?.avg | number:'1.0-2' }}</p>\n              <p class=\"text-muted\">ms</p>\n            </div>\n          </div>\n        </div>\n        <div class=\"col-md-3 mb-3\">\n          <div class=\"card h-100\">\n            <div class=\"card-body text-center\">\n              <h5 class=\"card-title\" data-bs-toggle=\"popover\" data-help-type=\"median-response-time\" style=\"cursor: help;\">Medián doby odezvy</h5>\n              <p class=\"display-4 mb-0\">{{ methodDetailData.overallStats?.median | number:'1.0-2' }}</p>\n              <p class=\"text-muted\">ms</p>\n            </div>\n          </div>\n        </div>\n        <div class=\"col-md-3 mb-3\">\n          <div class=\"card h-100\">\n            <div class=\"card-body text-center\">\n              <h5 class=\"card-title\" data-bs-toggle=\"popover\" data-help-type=\"p95-response-time\" style=\"cursor: help;\">95. percentil</h5>\n              <p class=\"display-4 mb-0\">{{ methodDetailData.overallStats?.percentil95 | number:'1.0-2' }}</p>\n              <p class=\"text-muted\">ms</p>\n            </div>\n          </div>\n        </div>\n        <div class=\"col-md-3 mb-3\">\n          <div class=\"card h-100\">\n            <div class=\"card-body text-center\">\n              <h5 class=\"card-title\" data-bs-toggle=\"popover\" data-help-type=\"total-calls\" style=\"cursor: help;\">Celkový počet volání</h5>\n              <p class=\"display-4 mb-0\">{{ methodDetailData.overallStats?.totalCount }}</p>\n              <p class=\"text-muted\">za zvolené období</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Graf detailu metody -->\n      <div class=\"card mb-4\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"method-detail\" style=\"cursor: help;\">Výkon metody v čase</h5>\n          <button class=\"btn btn-sm btn-outline-info\" (click)=\"openFullscreenChart(methodDetailChart, 'Výkon metody v čase')\" title=\"Zobrazit graf na celou obrazovku\">\n            <i class=\"bi bi-arrows-fullscreen\"></i>\n          </button>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 400px; width: 100%;\">\n            <div *ngIf=\"renderingMethodDetailChart\" class=\"chart-loading-overlay\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n              <p class=\"mt-2\">Zpracování dat grafu...</p>\n            </div>\n            <div *ngIf=\"!hasMethodDetailChartData\" class=\"no-data-message\">\n              <i class=\"bi bi-bar-chart-line\"></i>\n              <p>Nejsou k dispozici žádná data pro zobrazení grafu</p>\n            </div>\n            <canvas *ngIf=\"hasMethodDetailChartData\" #methodDetailChart width=\"800\" height=\"400\" style=\"display: block;\"></canvas>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}