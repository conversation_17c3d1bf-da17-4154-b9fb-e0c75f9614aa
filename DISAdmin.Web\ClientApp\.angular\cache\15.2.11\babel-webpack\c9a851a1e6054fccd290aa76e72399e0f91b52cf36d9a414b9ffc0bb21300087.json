{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\n/**\r\n * Služba pro zobrazení grafů v modálním okně\r\n */\nexport let ChartModalService = /*#__PURE__*/(() => {\n  class ChartModalService {\n    constructor() {\n      // ID modálního okna\n      this.modalId = 'chartFullscreenModal';\n      // Instance modálního okna\n      this.modalInstance = null;\n      // Subjekt pro předání dat grafu\n      this.chartDataSubject = new Subject();\n      // Observable pro předání dat grafu\n      this.chartData$ = this.chartDataSubject.asObservable();\n    }\n    /**\r\n     * Inteligentní zkrácení názvu metody pro zobrazení v grafu\r\n     * @param text Text k zkrácení\r\n     * @param maxLength Maximální délka včetně \"...\" (výchozí 30 znaků)\r\n     * @returns Zkrácený text s \"...\" pokud je potřeba, cel<PERSON><PERSON> délka nepřekro<PERSON> maxLength\r\n     */\n    truncateMethodName(text, maxLength = 30) {\n      if (!text || text.length <= maxLength) {\n        return text;\n      }\n      // Zkrátíme na maxLength - 3 znaků, aby se vešly \"...\"\n      const targetLength = maxLength - 3;\n      if (targetLength <= 0) {\n        return '...';\n      }\n      // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n      const truncated = text.substring(0, targetLength);\n      const lastDotIndex = truncated.lastIndexOf('.');\n      // Pokud najdeme tečku a není příliš na začátku (alespoň 40% z cílové délky), zkrátíme na ní\n      if (lastDotIndex > 0 && lastDotIndex >= targetLength * 0.4) {\n        return text.substring(0, lastDotIndex) + '...';\n      }\n      // Jinak zkrátíme na pevné délce\n      return truncated + '...';\n    }\n    /**\r\n     * Detekuje, zda graf používá zkrácené popisky na ose X\r\n     * @param chartInstance Instance grafu\r\n     * @returns True pokud graf používá zkrácené popisky\r\n     */\n    hasXAxisTruncation(chartInstance) {\n      const xAxis = chartInstance.options?.scales?.x;\n      return !!xAxis?.ticks?.callback;\n    }\n    /**\r\n     * Získá originální labels z dat grafu pro tooltip\r\n     * @param chartInstance Instance grafu\r\n     * @returns Originální labels nebo null\r\n     */\n    getOriginalLabels(chartInstance) {\n      // Zkusíme najít originální labels v data objektu\n      const data = chartInstance.data;\n      if (data.originalLabels && Array.isArray(data.originalLabels)) {\n        return data.originalLabels;\n      }\n      // Fallback na běžné labels\n      if (data.labels && Array.isArray(data.labels)) {\n        return data.labels;\n      }\n      return null;\n    }\n    /**\r\n     * Otevře modální okno s grafem\r\n     * @param chartInstance Instance grafu, který chceme zobrazit\r\n     * @param title Titulek grafu\r\n     */\n    openChartModal(chartInstance, title) {\n      if (!chartInstance) {\n        console.error('Nelze zobrazit graf v modálním okně - instance grafu není k dispozici');\n        return;\n      }\n      try {\n        // Získáme typ grafu\n        let chartType = 'line'; // Výchozí typ grafu\n        // Pokusíme se získat typ grafu z instance\n        if (chartInstance.config && typeof chartInstance.config === 'object') {\n          // Pokud je config objekt, zkusíme získat typ\n          if ('type' in chartInstance.config) {\n            chartType = chartInstance.config.type;\n          }\n        }\n        // Získáme konfiguraci grafu\n        const config = {\n          type: chartType,\n          data: JSON.parse(JSON.stringify(chartInstance.data)),\n          options: JSON.parse(JSON.stringify(chartInstance.options))\n        };\n        // Detekujeme, zda původní graf používá zkrácené popisky na ose X\n        const hasXTruncation = this.hasXAxisTruncation(chartInstance);\n        const originalLabels = this.getOriginalLabels(chartInstance);\n        // Upravíme konfiguraci pro modální okno\n        if (config.options) {\n          // Zajistíme responzivní zobrazení\n          config.options.responsive = true;\n          config.options.maintainAspectRatio = false;\n          // Zvětšíme fonty pro lepší čitelnost\n          if (config.options.plugins && config.options.plugins.title) {\n            config.options.plugins.title.font = {\n              ...config.options.plugins.title.font,\n              size: 18\n            };\n          }\n          if (config.options.scales) {\n            // Zvětšíme fonty pro osy\n            Object.keys(config.options.scales).forEach(axisKey => {\n              const axis = config.options.scales[axisKey];\n              if (axis && axis.ticks) {\n                axis.ticks.font = {\n                  ...axis.ticks.font,\n                  size: 14\n                };\n              }\n            });\n            // Pokud původní graf používal zkrácené popisky na ose X, aplikujeme je i zde\n            if (hasXTruncation && originalLabels && config.options.scales.x) {\n              config.options.scales.x.ticks = config.options.scales.x.ticks || {};\n              config.options.scales.x.ticks.callback = (_value, index) => {\n                const label = originalLabels[index];\n                return this.truncateMethodName(label);\n              };\n            }\n          }\n          // Pokud původní graf používal zkrácené popisky, přidáme tooltip s plnými názvy\n          if (hasXTruncation && originalLabels) {\n            config.options.plugins = config.options.plugins || {};\n            config.options.plugins.tooltip = config.options.plugins.tooltip || {};\n            config.options.plugins.tooltip.callbacks = config.options.plugins.tooltip.callbacks || {};\n            config.options.plugins.tooltip.callbacks.title = context => {\n              if (context && context.length > 0) {\n                const dataIndex = context[0].dataIndex;\n                const fullLabel = originalLabels[dataIndex];\n                return fullLabel || context[0].label;\n              }\n              return context[0]?.label || '';\n            };\n          }\n        }\n        // Předáme data grafu do subjektu\n        this.chartDataSubject.next({\n          chartInstance,\n          title,\n          config\n        });\n        // Otevřeme modální okno\n        this.openModal();\n      } catch (error) {\n        console.error('Chyba při otevírání grafu v modálním okně:', error);\n      }\n    }\n    /**\r\n     * Otevře modální okno\r\n     */\n    openModal() {\n      const modalElement = document.getElementById(this.modalId);\n      if (!modalElement) {\n        console.error(`Modal element with ID ${this.modalId} not found`);\n        return;\n      }\n      // Zkontrolujeme, zda již existuje instance modálního okna\n      let existingModal = window.bootstrap.Modal.getInstance(modalElement);\n      if (existingModal) {\n        existingModal.dispose();\n      }\n      // Vytvoříme novou instanci modálního okna\n      const modalOptions = {\n        backdrop: true,\n        keyboard: true\n      };\n      this.modalInstance = new window.bootstrap.Modal(modalElement, modalOptions);\n      this.modalInstance.show();\n    }\n    /**\r\n     * Zavře modální okno\r\n     */\n    closeModal() {\n      if (this.modalInstance) {\n        this.modalInstance.hide();\n        this.modalInstance = null;\n      }\n    }\n    static {\n      this.ɵfac = function ChartModalService_Factory(t) {\n        return new (t || ChartModalService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ChartModalService,\n        factory: ChartModalService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ChartModalService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}