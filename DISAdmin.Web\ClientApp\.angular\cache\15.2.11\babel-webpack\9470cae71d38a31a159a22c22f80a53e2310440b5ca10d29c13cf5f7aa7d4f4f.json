{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TextUtilsService {\n  /**\r\n   * Inteligentní zkrácení názvu metody pro zobrazení v grafu\r\n   * @param text Text k zkrácení\r\n   * @param maxLength Maximální délka včetně \"...\" (výchozí 30 znaků)\r\n   * @returns Zkrácený text s \"...\" pokud je potřeba, celková délka nepřekročí maxLength\r\n   */\n  truncateMethodName(text, maxLength = 30) {\n    if (!text || text.length <= maxLength) {\n      return text;\n    }\n    // Zkrátíme na maxLength - 3 znaků, aby se vešly \"...\"\n    const targetLength = maxLength - 3;\n    if (targetLength <= 0) {\n      return '...';\n    }\n    // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n    const truncated = text.substring(0, targetLength);\n    const lastDotIndex = truncated.lastIndexOf('.');\n    // Pokud najdeme tečku a není příliš na začátku (alespoň 40% z cílové délky), zkrátíme na ní\n    if (lastDotIndex > 0 && lastDotIndex >= targetLength * 0.4) {\n      return text.substring(0, lastDotIndex) + '...';\n    }\n    // Jinak zkrátíme na pevné délce\n    return truncated + '...';\n  }\n  /**\r\n   * Vytvoří konfiguraci pro zkrácení popisků na ose X pro Chart.js\r\n   * @param originalLabels Originální popisky\r\n   * @param options Možnosti konfigurace\r\n   * @returns Objekt s konfigurací pro Chart.js\r\n   */\n  createTruncatedLabelsConfig(originalLabels, options = {}) {\n    const config = {\n      enabled: options.enabled ?? true,\n      maxLength: options.maxLength ?? 30,\n      showFullInTooltip: options.showFullInTooltip ?? true\n    };\n    if (!config.enabled) {\n      return {};\n    }\n    const result = {\n      scales: {\n        x: {\n          ticks: {\n            callback: (_value, index) => {\n              const label = originalLabels[index];\n              return this.truncateMethodName(label, config.maxLength);\n            },\n            // Zakázat automatické přeskakování a zkracování Chart.js\n            autoSkip: false,\n            maxRotation: 45,\n            minRotation: 45,\n            autoSkipPadding: 0 // Žádné automatické odsazení\n          }\n        }\n      }\n    };\n\n    if (config.showFullInTooltip) {\n      result.plugins = {\n        tooltip: {\n          callbacks: {\n            title: context => {\n              if (context && context.length > 0) {\n                const dataIndex = context[0].dataIndex;\n                const fullLabel = originalLabels[dataIndex];\n                return fullLabel || context[0].label;\n              }\n              return context[0]?.label || '';\n            }\n          }\n        }\n      };\n    }\n    return result;\n  }\n  /**\r\n   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu\r\n   * @param baseConfig Základní konfigurace grafu\r\n   * @param truncatedConfig Konfigurace pro zkrácené popisky\r\n   * @returns Sloučená konfigurace\r\n   */\n  mergeChartConfig(baseConfig, truncatedConfig) {\n    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {\n      return baseConfig;\n    }\n    const merged = {\n      ...baseConfig\n    };\n    // Sloučení scales\n    if (truncatedConfig.scales) {\n      merged.scales = merged.scales || {};\n      if (truncatedConfig.scales.x) {\n        merged.scales.x = merged.scales.x || {};\n        merged.scales.x.ticks = {\n          ...merged.scales.x.ticks,\n          ...truncatedConfig.scales.x.ticks\n        };\n      }\n    }\n    // Sloučení plugins\n    if (truncatedConfig.plugins) {\n      merged.plugins = merged.plugins || {};\n      if (truncatedConfig.plugins.tooltip) {\n        merged.plugins.tooltip = merged.plugins.tooltip || {};\n        merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};\n        // Sloučení tooltip callbacks - zachováme existující a přidáme nové\n        Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {\n          merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];\n        });\n      }\n    }\n    return merged;\n  }\n  static {\n    this.ɵfac = function TextUtilsService_Factory(t) {\n      return new (t || TextUtilsService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TextUtilsService,\n      factory: TextUtilsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAKA,OAAM,MAAOA,gBAAgB;EAE3B;;;;;;EAMAC,kBAAkB,CAACC,IAAY,EAAEC,YAAoB,EAAE;IACrD,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE;MACrC,OAAOD,IAAI;;IAGb;IACA,MAAMG,YAAY,GAAGF,SAAS,GAAG,CAAC;IAClC,IAAIE,YAAY,IAAI,CAAC,EAAE;MACrB,OAAO,KAAK;;IAGd;IACA,MAAMC,SAAS,GAAGJ,IAAI,CAACK,SAAS,CAAC,CAAC,EAAEF,YAAY,CAAC;IACjD,MAAMG,YAAY,GAAGF,SAAS,CAACG,WAAW,CAAC,GAAG,CAAC;IAE/C;IACA,IAAID,YAAY,GAAG,CAAC,IAAIA,YAAY,IAAIH,YAAY,GAAG,GAAG,EAAE;MAC1D,OAAOH,IAAI,CAACK,SAAS,CAAC,CAAC,EAAEC,YAAY,CAAC,GAAG,KAAK;;IAGhD;IACA,OAAOF,SAAS,GAAG,KAAK;EAC1B;EAEA;;;;;;EAMAI,2BAA2B,CAACC,cAAwB,EAAEC,UAIlD,EAAE;IACJ,MAAMC,MAAM,GAAG;MACbC,OAAO,EAAEF,OAAO,CAACE,OAAO,IAAI,IAAI;MAChCX,SAAS,EAAES,OAAO,CAACT,SAAS,IAAI,EAAE;MAClCY,iBAAiB,EAAEH,OAAO,CAACG,iBAAiB,IAAI;KACjD;IAED,IAAI,CAACF,MAAM,CAACC,OAAO,EAAE;MACnB,OAAO,EAAE;;IAGX,MAAME,MAAM,GAAQ;MAClBC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDC,KAAK,EAAE;YACLC,QAAQ,EAAE,CAACC,MAAW,EAAEC,KAAa,KAAI;cACvC,MAAMC,KAAK,GAAGZ,cAAc,CAACW,KAAK,CAAC;cACnC,OAAO,IAAI,CAACrB,kBAAkB,CAACsB,KAAK,EAAEV,MAAM,CAACV,SAAS,CAAC;YACzD,CAAC;YACD;YACAqB,QAAQ,EAAE,KAAK;YACfC,WAAW,EAAE,EAAE;YACfC,WAAW,EAAE,EAAE;YACfC,eAAe,EAAE,CAAC,CAAM;;;;KAI/B;;IAED,IAAId,MAAM,CAACE,iBAAiB,EAAE;MAC5BC,MAAM,CAACY,OAAO,GAAG;QACfC,OAAO,EAAE;UACPC,SAAS,EAAE;YACTC,KAAK,EAAGC,OAAY,IAAI;cACtB,IAAIA,OAAO,IAAIA,OAAO,CAAC5B,MAAM,GAAG,CAAC,EAAE;gBACjC,MAAM6B,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS;gBACtC,MAAMC,SAAS,GAAGvB,cAAc,CAACsB,SAAS,CAAC;gBAC3C,OAAOC,SAAS,IAAIF,OAAO,CAAC,CAAC,CAAC,CAACT,KAAK;;cAEtC,OAAOS,OAAO,CAAC,CAAC,CAAC,EAAET,KAAK,IAAI,EAAE;YAChC;;;OAGL;;IAGH,OAAOP,MAAM;EACf;EAEA;;;;;;EAMAmB,gBAAgB,CAACC,UAAe,EAAEC,eAAoB;IACpD,IAAI,CAACA,eAAe,IAAIC,MAAM,CAACC,IAAI,CAACF,eAAe,CAAC,CAACjC,MAAM,KAAK,CAAC,EAAE;MACjE,OAAOgC,UAAU;;IAGnB,MAAMI,MAAM,GAAG;MAAE,GAAGJ;IAAU,CAAE;IAEhC;IACA,IAAIC,eAAe,CAACpB,MAAM,EAAE;MAC1BuB,MAAM,CAACvB,MAAM,GAAGuB,MAAM,CAACvB,MAAM,IAAI,EAAE;MACnC,IAAIoB,eAAe,CAACpB,MAAM,CAACC,CAAC,EAAE;QAC5BsB,MAAM,CAACvB,MAAM,CAACC,CAAC,GAAGsB,MAAM,CAACvB,MAAM,CAACC,CAAC,IAAI,EAAE;QACvCsB,MAAM,CAACvB,MAAM,CAACC,CAAC,CAACC,KAAK,GAAG;UACtB,GAAGqB,MAAM,CAACvB,MAAM,CAACC,CAAC,CAACC,KAAK;UACxB,GAAGkB,eAAe,CAACpB,MAAM,CAACC,CAAC,CAACC;SAC7B;;;IAIL;IACA,IAAIkB,eAAe,CAACT,OAAO,EAAE;MAC3BY,MAAM,CAACZ,OAAO,GAAGY,MAAM,CAACZ,OAAO,IAAI,EAAE;MACrC,IAAIS,eAAe,CAACT,OAAO,CAACC,OAAO,EAAE;QACnCW,MAAM,CAACZ,OAAO,CAACC,OAAO,GAAGW,MAAM,CAACZ,OAAO,CAACC,OAAO,IAAI,EAAE;QACrDW,MAAM,CAACZ,OAAO,CAACC,OAAO,CAACC,SAAS,GAAGU,MAAM,CAACZ,OAAO,CAACC,OAAO,CAACC,SAAS,IAAI,EAAE;QAEzE;QACAQ,MAAM,CAACC,IAAI,CAACF,eAAe,CAACT,OAAO,CAACC,OAAO,CAACC,SAAS,CAAC,CAACW,OAAO,CAACC,GAAG,IAAG;UACnEF,MAAM,CAACZ,OAAO,CAACC,OAAO,CAACC,SAAS,CAACY,GAAG,CAAC,GAAGL,eAAe,CAACT,OAAO,CAACC,OAAO,CAACC,SAAS,CAACY,GAAG,CAAC;QACxF,CAAC,CAAC;;;IAIN,OAAOF,MAAM;EACf;;;uBAnIWxC,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAA2C,SAAhB3C,gBAAgB;MAAA4C,YAFf;IAAM;EAAA", "names": ["TextUtilsService", "truncateMethodName", "text", "max<PERSON><PERSON><PERSON>", "length", "targetLength", "truncated", "substring", "lastDotIndex", "lastIndexOf", "createTruncatedLabelsConfig", "originalLabels", "options", "config", "enabled", "showFullInTooltip", "result", "scales", "x", "ticks", "callback", "_value", "index", "label", "autoSkip", "maxRotation", "minRotation", "autoSkipPadding", "plugins", "tooltip", "callbacks", "title", "context", "dataIndex", "fullLabel", "mergeChartConfig", "baseConfig", "truncatedConfig", "Object", "keys", "merged", "for<PERSON>ach", "key", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\services\\text-utils.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TextUtilsService {\n\n  /**\n   * Inteligentní zkrácení názvu metody pro zobrazení v grafu\n   * @param text Text k zkrácení\n   * @param maxLength Maximální délka včetně \"...\" (výchozí 30 znaků)\n   * @returns Zkrácený text s \"...\" pokud je potřeba, celkov<PERSON> délka nepřekročí maxLength\n   */\n  truncateMethodName(text: string, maxLength: number = 30): string {\n    if (!text || text.length <= maxLength) {\n      return text;\n    }\n    \n    // Zkrátíme na maxLength - 3 znaků, aby se vešly \"...\"\n    const targetLength = maxLength - 3;\n    if (targetLength <= 0) {\n      return '...';\n    }\n    \n    // Pokusíme se zkrátit na posledním \".\" před limitem pro zachování čitelnosti\n    const truncated = text.substring(0, targetLength);\n    const lastDotIndex = truncated.lastIndexOf('.');\n    \n    // Pokud najdeme tečku a není příliš na začátku (alespoň 40% z cílové délky), zkrátíme na ní\n    if (lastDotIndex > 0 && lastDotIndex >= targetLength * 0.4) {\n      return text.substring(0, lastDotIndex) + '...';\n    }\n    \n    // Jinak zkrátíme na pevné délce\n    return truncated + '...';\n  }\n\n  /**\n   * Vytvoří konfiguraci pro zkrácení popisků na ose X pro Chart.js\n   * @param originalLabels Originální popisky\n   * @param options Možnosti konfigurace\n   * @returns Objekt s konfigurací pro Chart.js\n   */\n  createTruncatedLabelsConfig(originalLabels: string[], options: {\n    enabled?: boolean;\n    maxLength?: number;\n    showFullInTooltip?: boolean;\n  } = {}) {\n    const config = {\n      enabled: options.enabled ?? true,\n      maxLength: options.maxLength ?? 30,\n      showFullInTooltip: options.showFullInTooltip ?? true\n    };\n\n    if (!config.enabled) {\n      return {};\n    }\n\n    const result: any = {\n      scales: {\n        x: {\n          ticks: {\n            callback: (_value: any, index: number) => {\n              const label = originalLabels[index];\n              return this.truncateMethodName(label, config.maxLength);\n            },\n            // Zakázat automatické přeskakování a zkracování Chart.js\n            autoSkip: false,        // Nezobrazovat automaticky jen některé popisky\n            maxRotation: 45,        // Fixní rotace 45°\n            minRotation: 45,        // Fixní rotace 45°\n            autoSkipPadding: 0      // Žádné automatické odsazení\n          }\n        }\n      }\n    };\n\n    if (config.showFullInTooltip) {\n      result.plugins = {\n        tooltip: {\n          callbacks: {\n            title: (context: any) => {\n              if (context && context.length > 0) {\n                const dataIndex = context[0].dataIndex;\n                const fullLabel = originalLabels[dataIndex];\n                return fullLabel || context[0].label;\n              }\n              return context[0]?.label || '';\n            }\n          }\n        }\n      };\n    }\n\n    return result;\n  }\n\n  /**\n   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu\n   * @param baseConfig Základní konfigurace grafu\n   * @param truncatedConfig Konfigurace pro zkrácené popisky\n   * @returns Sloučená konfigurace\n   */\n  mergeChartConfig(baseConfig: any, truncatedConfig: any): any {\n    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {\n      return baseConfig;\n    }\n\n    const merged = { ...baseConfig };\n\n    // Sloučení scales\n    if (truncatedConfig.scales) {\n      merged.scales = merged.scales || {};\n      if (truncatedConfig.scales.x) {\n        merged.scales.x = merged.scales.x || {};\n        merged.scales.x.ticks = {\n          ...merged.scales.x.ticks,\n          ...truncatedConfig.scales.x.ticks\n        };\n      }\n    }\n\n    // Sloučení plugins\n    if (truncatedConfig.plugins) {\n      merged.plugins = merged.plugins || {};\n      if (truncatedConfig.plugins.tooltip) {\n        merged.plugins.tooltip = merged.plugins.tooltip || {};\n        merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};\n        \n        // Sloučení tooltip callbacks - zachováme existující a přidáme nové\n        Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {\n          merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];\n        });\n      }\n    }\n\n    return merged;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}