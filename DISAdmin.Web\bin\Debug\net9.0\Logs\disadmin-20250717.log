2025-07-17 10:51:45.832 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-17 10:51:46.097 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-07-17 10:52:16.433 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (JVD) - 28618.28100695 minut
2025-07-17 10:52:16.443 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (MILPE) - 232.27045059 minut
2025-07-17 10:52:16.450 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (ZP) - 28626.062711255 minut
2025-07-17 10:53:51.264 +02:00 [ERR] DISAdmin.Api.Auth.JwtUtils: Chyba při validaci tokenu
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '30. 6. 2025 16:15:01', Current time (UTC): '17. 7. 2025 8:53:51'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateTokenPayload(JwtSecurityToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateJWS(String token, TokenValidationParameters validationParameters, BaseConfiguration currentConfiguration, SecurityToken& signatureValidatedToken, ExceptionDispatchInfo& exceptionThrown)
--- End of stack trace from previous location ---
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateToken(String token, JwtSecurityToken outerToken, TokenValidationParameters validationParameters, SecurityToken& signatureValidatedToken)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateToken(String token, TokenValidationParameters validationParameters, SecurityToken& validatedToken)
   at DISAdmin.Api.Auth.JwtUtils.ValidateToken(String token) in C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Api\Auth\JwtUtils.cs:line 53
2025-07-17 10:53:51.264 +02:00 [ERR] DISAdmin.Api.Auth.JwtUtils: Chyba při validaci tokenu
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '30. 6. 2025 16:15:01', Current time (UTC): '17. 7. 2025 8:53:51'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateTokenPayload(JwtSecurityToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateJWS(String token, TokenValidationParameters validationParameters, BaseConfiguration currentConfiguration, SecurityToken& signatureValidatedToken, ExceptionDispatchInfo& exceptionThrown)
--- End of stack trace from previous location ---
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateToken(String token, JwtSecurityToken outerToken, TokenValidationParameters validationParameters, SecurityToken& signatureValidatedToken)
   at System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.ValidateToken(String token, TokenValidationParameters validationParameters, SecurityToken& validatedToken)
   at DISAdmin.Api.Auth.JwtUtils.ValidateToken(String token) in C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Api\Auth\JwtUtils.cs:line 53
2025-07-17 10:53:56.466 +02:00 [WRN] DISAdmin.Core.Services.UserService: Neúspěšný pokus o přihlášení - nesprávné heslo pro uživatele admin. IP: ::1
2025-07-17 10:54:05.722 +02:00 [WRN] DISAdmin.Core.Services.UserService: Neúspěšný pokus o přihlášení - nesprávné heslo pro uživatele admin. IP: ::1
2025-07-17 10:55:48.104 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-17 10:55:48.350 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-07-17 10:56:34.004 +02:00 [WRN] DISAdmin.Core.Services.UserService: Neúspěšný pokus o přihlášení - nesprávné heslo pro uživatele admin. IP: ::1
2025-07-17 10:56:41.951 +02:00 [WRN] DISAdmin.Core.Services.UserService: Neúspěšný pokus o přihlášení - nesprávné heslo pro uživatele admin. IP: ::1
2025-07-17 11:00:53.305 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-17 11:00:55.582 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-17 11:11:41.742 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-17 11:11:41.922 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-07-17 11:32:12.276 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (MLP) - 35.18877912333333 minut
2025-07-17 11:45:49.628 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-17 11:45:54.014 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-17 12:32:12.444 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (ZP) - 95.74512185833333 minut
2025-07-17 12:32:12.450 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (MLP) - 95.19445155333334 minut
2025-07-17 12:42:12.482 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (JVD) - 98.17191144333333 minut
2025-07-17 12:42:12.489 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (MILPE) - 99.19560962 minut
2025-07-17 15:03:24.754 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-17 15:03:24.927 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-07-17 15:03:55.239 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (MILPE) - 240.90409216666666 minut
2025-07-17 15:03:55.251 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (MLP) - 246.90467042666666 minut
2025-07-17 15:51:43.506 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-17 15:51:43.758 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-07-17 15:53:49.672 +02:00 [ERR] Microsoft.AspNetCore.SpaProxy.SpaProxyLaunchManager: Unable to connect to the SPA development server at 'http://localhost:4200'.
2025-07-17 16:12:13.982 +02:00 [WRN] DISAdmin.Core.BackgroundServices.CommunicationOutageBackgroundService: Vytvořen alert pro výpadek komunikace: Instance Produkce (MLP) - 315.2192244733333 minut
2025-07-17 16:17:14.815 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-17 16:24:26.824 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-17 16:24:27.081 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-07-17 16:36:41.029 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-17 16:36:41.265 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-07-17 16:44:35.563 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-17 16:44:35.790 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
