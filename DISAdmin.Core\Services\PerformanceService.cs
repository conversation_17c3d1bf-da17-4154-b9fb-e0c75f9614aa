using DISAdmin.Core.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DISAdmin.Core.Services;

/// <summary>
/// Služba pro práci s výkonnostními metrikami DIS metod
/// </summary>
public class PerformanceService
{
    private readonly DISAdminDbContext _context;
    private readonly ILogger<PerformanceService> _logger;
    private readonly MonitoringService _monitoringService;

    public PerformanceService(DISAdminDbContext context, ILogger<PerformanceService> logger, MonitoringService monitoringService)
    {
        _context = context;
        _logger = logger;
        _monitoringService = monitoringService;
    }

    /// <summary>
    /// Získá srovnání výkonu metod mezi instancemi
    /// </summary>
    /// <param name="instanceIds">ID instancí pro srovnání</param>
    /// <param name="methodName">Název metody (volitelné)</param>
    /// <param name="className">N<PERSON><PERSON>v třídy (volitelné)</param>
    /// <param name="fromDate">Počáteč<PERSON><PERSON> datum</param>
    /// <param name="toDate">Koncové datum</param>
    /// <returns>Data pro graf srovnání výkonu mezi instancemi</returns>
    public async Task<Dictionary<string, object>> GetInstancesComparisonAsync(
        List<int> instanceIds,
        string? methodName = null,
        string? className = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var result = new Dictionary<string, object>();

        if (instanceIds == null || instanceIds.Count == 0)
        {
            _logger.LogWarning("Nebyly předány instance pro porovnání");
            return result;
        }

        // Výchozí časové období - posledních 7 dní
        var startDate = fromDate ?? DateTime.UtcNow.AddDays(-7);
        var endDate = toDate ?? DateTime.UtcNow;

        // Získání dat z databáze
        var query = _context.PerformanceMetrics
            .Include(m => m.Instance)
            .ThenInclude(i => i.Customer)
            .Where(m => instanceIds.Contains(m.InstanceId) &&
                       m.Timestamp >= startDate &&
                       m.Timestamp <= endDate);

        // Filtrování podle metody a třídy
        if (!string.IsNullOrEmpty(methodName))
        {
            query = query.Where(m => m.MethodName == methodName);
        }

        if (!string.IsNullOrEmpty(className))
        {
            query = query.Where(m => m.ClassName == className);
        }

        // Seskupení podle instance a metody
        var performanceData = await query
            .GroupBy(m => new { m.InstanceId, m.Instance.Name, m.Instance.Customer.Abbreviation, m.ClassName, m.MethodName })
            .Select(g => new
            {
                InstanceId = g.Key.InstanceId,
                InstanceName = g.Key.Name,
                CustomerAbbreviation = g.Key.Abbreviation,
                InstanceDisplayName = $"{g.Key.Abbreviation} - {g.Key.Name}",
                ClassName = g.Key.ClassName,
                MethodName = g.Key.MethodName,
                FullMethodName = $"{g.Key.ClassName}.{g.Key.MethodName}",
                Min = g.Average(m => m.Min),
                Max = g.Average(m => m.Max),
                Avg = g.Average(m => m.Avg),
                Median = g.Average(m => m.Median),
                Percentil95 = g.Average(m => m.Percentil95),
                Percentil99 = g.Average(m => m.Percentil99),
                StdDev = g.Average(m => m.StdDev),
                TotalCount = g.Sum(m => m.TotalCount),
                NonZeroCount = g.Sum(m => m.NonZeroCount)
            })
            .OrderByDescending(g => g.Avg)
            .ToListAsync();

        // Pokud není zadán filtr na třídu nebo metodu, filtrujeme podle průměrné odezvy DIS metod
        if (string.IsNullOrEmpty(methodName) && string.IsNullOrEmpty(className))
        {
            try
            {
                // Získáme průměrnou odezvu DIS metod ze systémových statistik
                var systemStats = await _monitoringService.GetSystemStatisticsAsync();
                if (systemStats.TryGetValue("AvgApiResponseTime", out var avgResponseTimeObj) &&
                    avgResponseTimeObj is double avgResponseTime && avgResponseTime > 0)
                {
                    // Filtrujeme metody - zobrazíme pouze ty, které mají alespoň u jedné instance hodnotu větší než průměr
                    var methodsToShow = performanceData
                        .GroupBy(d => d.FullMethodName)
                        .Where(g => g.Any(method => method.Avg > avgResponseTime))
                        .Select(g => g.Key)
                        .ToHashSet();

                    performanceData = performanceData
                        .Where(d => methodsToShow.Contains(d.FullMethodName))
                        .ToList();

                    _logger.LogInformation($"Filtrování metod podle průměrné odezvy {avgResponseTime}ms - zobrazeno {methodsToShow.Count} metod z původních {performanceData.Count}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Chyba při získávání průměrné odezvy pro filtrování metod - zobrazí se všechny metody");
            }
        }

        // Příprava dat pro graf
        var instances = performanceData
            .Select(d => new { d.InstanceId, d.InstanceName, d.InstanceDisplayName })
            .Distinct()
            .ToList();

        var methods = performanceData
            .Select(d => d.FullMethodName)
            .Distinct()
            .ToList();

        // Vytvoření datových sad pro každou instanci
        var datasets = new List<object>();
        var colors = new[]
        {
            "rgba(54, 162, 235, 0.5)", "rgba(255, 99, 132, 0.5)",
            "rgba(75, 192, 192, 0.5)", "rgba(255, 159, 64, 0.5)",
            "rgba(153, 102, 255, 0.5)", "rgba(255, 205, 86, 0.5)"
        };

        var borderColors = new[]
        {
            "rgba(54, 162, 235, 1)", "rgba(255, 99, 132, 1)",
            "rgba(75, 192, 192, 1)", "rgba(255, 159, 64, 1)",
            "rgba(153, 102, 255, 1)", "rgba(255, 205, 86, 1)"
        };

        for (int i = 0; i < instances.Count; i++)
        {
            var instance = instances[i];
            var instanceData = performanceData.Where(d => d.InstanceId == instance.InstanceId).ToList();

            var avgData = new List<double>();
            foreach (var method in methods)
            {
                var methodData = instanceData.FirstOrDefault(d => d.FullMethodName == method);
                avgData.Add(methodData?.Avg ?? 0);
            }

            datasets.Add(new
            {
                label = instance.InstanceDisplayName,
                data = avgData,
                backgroundColor = colors[i % colors.Length],
                borderColor = borderColors[i % borderColors.Length],
                borderWidth = 1
            });
        }

        result["labels"] = methods;
        result["datasets"] = datasets;
        result["title"] = "Srovnání výkonu metod mezi instancemi";

        return result;
    }

    /// <summary>
    /// Získá srovnání výkonu metod mezi verzemi pro jednu instanci
    /// </summary>
    /// <param name="instanceId">ID instance</param>
    /// <param name="methodName">Název metody (volitelné)</param>
    /// <param name="className">Název třídy (volitelné)</param>
    /// <param name="fromDate">Počáteční datum</param>
    /// <param name="toDate">Koncové datum</param>
    /// <returns>Data pro graf srovnání výkonu mezi verzemi</returns>
    public async Task<Dictionary<string, object>> GetVersionsComparisonAsync(
        int instanceId,
        string? methodName = null,
        string? className = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var result = new Dictionary<string, object>();

        // Výchozí časové období - posledních 90 dní
        var startDate = fromDate ?? DateTime.UtcNow.AddDays(-90);
        var endDate = toDate ?? DateTime.UtcNow;

        // Získání dat z databáze
        var query = _context.PerformanceMetrics
            .Where(m => m.InstanceId == instanceId &&
                       m.Timestamp >= startDate &&
                       m.Timestamp <= endDate &&
                       m.VersionNumber != null);

        // Filtrování podle metody a třídy
        if (!string.IsNullOrEmpty(methodName))
        {
            query = query.Where(m => m.MethodName == methodName);
        }

        if (!string.IsNullOrEmpty(className))
        {
            query = query.Where(m => m.ClassName == className);
        }

        // Seskupení podle verze a metody
        var performanceData = await query
            .GroupBy(m => new { m.VersionNumber, m.ClassName, m.MethodName })
            .Select(g => new
            {
                Version = g.Key.VersionNumber,
                ClassName = g.Key.ClassName,
                MethodName = g.Key.MethodName,
                FullMethodName = $"{g.Key.ClassName}.{g.Key.MethodName}",
                Min = g.Average(m => m.Min),
                Max = g.Average(m => m.Max),
                Avg = g.Average(m => m.Avg),
                Median = g.Average(m => m.Median),
                Percentil95 = g.Average(m => m.Percentil95),
                Percentil99 = g.Average(m => m.Percentil99),
                StdDev = g.Average(m => m.StdDev),
                TotalCount = g.Sum(m => m.TotalCount),
                NonZeroCount = g.Sum(m => m.NonZeroCount)
            })
            .OrderByDescending(g => g.Avg)
            .ToListAsync();

        // Příprava dat pro graf
        var versions = performanceData
            .Select(d => d.Version)
            .Distinct()
            .ToList();

        var methods = performanceData
            .Select(d => d.FullMethodName)
            .Distinct()
            .ToList();

        // Vytvoření datových sad pro každou verzi
        var datasets = new List<object>();
        var colors = new[]
        {
            "rgba(54, 162, 235, 0.5)", "rgba(255, 99, 132, 0.5)",
            "rgba(75, 192, 192, 0.5)", "rgba(255, 159, 64, 0.5)",
            "rgba(153, 102, 255, 0.5)", "rgba(255, 205, 86, 0.5)"
        };

        var borderColors = new[]
        {
            "rgba(54, 162, 235, 1)", "rgba(255, 99, 132, 1)",
            "rgba(75, 192, 192, 1)", "rgba(255, 159, 64, 1)",
            "rgba(153, 102, 255, 1)", "rgba(255, 205, 86, 1)"
        };

        for (int i = 0; i < versions.Count; i++)
        {
            var version = versions[i];
            var versionData = performanceData.Where(d => d.Version == version).ToList();

            var avgData = new List<double>();
            foreach (var method in methods)
            {
                var methodData = versionData.FirstOrDefault(d => d.FullMethodName == method);
                avgData.Add(methodData?.Avg ?? 0);
            }

            datasets.Add(new
            {
                label = $"Verze {version}",
                data = avgData,
                backgroundColor = colors[i % colors.Length],
                borderColor = borderColors[i % borderColors.Length],
                borderWidth = 1
            });
        }

        result["labels"] = methods;
        result["datasets"] = datasets;
        result["title"] = "Srovnání výkonu metod mezi verzemi";

        return result;
    }

    /// <summary>
    /// Získá agregované výkonnostní metriky pro jednu nebo všechny instance
    /// </summary>
    /// <param name="instanceId">ID instance (volitelné)</param>
    /// <param name="fromDate">Počáteční datum</param>
    /// <param name="toDate">Koncové datum</param>
    /// <returns>Agregované výkonnostní metriky</returns>
    public async Task<Dictionary<string, object>> GetAggregatedPerformanceAsync(
        int? instanceId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var result = new Dictionary<string, object>();

        // Výchozí časové období - posledních 7 dní
        var startDate = fromDate ?? DateTime.UtcNow.AddDays(-7);
        var endDate = toDate ?? DateTime.UtcNow;

        // Získání dat z databáze
        var query = _context.PerformanceMetrics
            .Where(m => m.Timestamp >= startDate && m.Timestamp <= endDate);

        if (instanceId.HasValue)
        {
            query = query.Where(m => m.InstanceId == instanceId.Value);
        }

        // Seskupení podle dne a výpočet agregovaných hodnot
        var dailyData = await query
            .GroupBy(m => m.Timestamp.Date)
            .Select(g => new
            {
                Date = g.Key,
                Min = g.Average(m => m.Min),
                Max = g.Average(m => m.Max),
                Avg = g.Average(m => m.Avg),
                Median = g.Average(m => m.Median),
                Percentil95 = g.Average(m => m.Percentil95),
                Percentil99 = g.Average(m => m.Percentil99),
                StdDev = g.Average(m => m.StdDev),
                TotalCount = g.Sum(m => m.TotalCount),
                NonZeroCount = g.Sum(m => m.NonZeroCount)
            })
            .OrderBy(g => g.Date)
            .ToListAsync();

        // Příprava dat pro graf
        var labels = dailyData.Select(d => d.Date.ToString("dd.MM.yyyy")).ToList();
        var minData = dailyData.Select(d => d.Min).ToList();
        var maxData = dailyData.Select(d => d.Max).ToList();
        var avgData = dailyData.Select(d => d.Avg).ToList();
        var medianData = dailyData.Select(d => d.Median).ToList();
        var p95Data = dailyData.Select(d => d.Percentil95).ToList();
        var p99Data = dailyData.Select(d => d.Percentil99).ToList();
        var totalCountData = dailyData.Select(d => d.TotalCount).ToList();

        result["labels"] = labels;
        result["minData"] = minData;
        result["maxData"] = maxData;
        result["avgData"] = avgData;
        result["medianData"] = medianData;
        result["p95Data"] = p95Data;
        result["p99Data"] = p99Data;
        result["totalCountData"] = totalCountData;
        result["title"] = instanceId.HasValue ? "Výkon instance v čase" : "Výkon všech instancí v čase";

        // Přidání denních dat pro případné další zpracování na klientovi
        result["dailyData"] = dailyData;

        return result;
    }

    /// <summary>
    /// Získá nejpomalejší metody pro jednu nebo všechny instance
    /// </summary>
    /// <param name="instanceId">ID instance (volitelné)</param>
    /// <param name="fromDate">Počáteční datum</param>
    /// <param name="toDate">Koncové datum</param>
    /// <param name="limit">Počet metod k vrácení</param>
    /// <returns>Nejpomalejší metody</returns>
    public async Task<List<object>> GetSlowestMethodsAsync(
        int? instanceId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int limit = 10)
    {
        // Výchozí časové období - posledních 7 dní
        var startDate = fromDate ?? DateTime.UtcNow.AddDays(-7);
        var endDate = toDate ?? DateTime.UtcNow;

        // Získání dat z databáze
        var query = _context.PerformanceMetrics
            .Include(m => m.Instance)
            .ThenInclude(i => i.Customer)
            .Where(m => m.Timestamp >= startDate && m.Timestamp <= endDate);

        if (instanceId.HasValue)
        {
            query = query.Where(m => m.InstanceId == instanceId.Value);
        }

        // Seskupení podle metody a výpočet agregovaných hodnot
        var methodsData = await query
            .GroupBy(m => new { m.InstanceId, m.Instance.Name, m.Instance.Customer.Abbreviation, m.ClassName, m.MethodName })
            .Select(g => new
            {
                InstanceId = g.Key.InstanceId,
                InstanceName = $"{g.Key.Abbreviation} - {g.Key.Name}",
                ClassName = g.Key.ClassName,
                MethodName = g.Key.MethodName,
                FullMethodName = $"{g.Key.ClassName}.{g.Key.MethodName}",
                Min = g.Average(m => m.Min),
                Max = g.Average(m => m.Max),
                Avg = g.Average(m => m.Avg),
                Median = g.Average(m => m.Median),
                Percentil95 = g.Average(m => m.Percentil95),
                Percentil99 = g.Average(m => m.Percentil99),
                StdDev = g.Average(m => m.StdDev),
                TotalCount = g.Sum(m => m.TotalCount),
                NonZeroCount = g.Sum(m => m.NonZeroCount)
            })
            .OrderByDescending(g => g.Percentil95) // Řazení podle 95. percentilu
            .Take(limit)
            .ToListAsync();

        return methodsData.Cast<object>().ToList();
    }

    /// <summary>
    /// Získá detailní statistiky pro konkrétní metodu
    /// </summary>
    /// <param name="instanceId">ID instance (volitelné)</param>
    /// <param name="className">Název třídy</param>
    /// <param name="methodName">Název metody</param>
    /// <param name="fromDate">Počáteční datum</param>
    /// <param name="toDate">Koncové datum</param>
    /// <returns>Detailní statistiky metody</returns>
    public async Task<Dictionary<string, object>> GetMethodDetailAsync(
        int? instanceId,
        string className,
        string methodName,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var result = new Dictionary<string, object>();

        // Výchozí časové období - posledních 30 dní
        var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
        var endDate = toDate ?? DateTime.UtcNow;

        // Získání dat z databáze
        var query = _context.PerformanceMetrics
            .Where(m => m.ClassName == className &&
                       m.MethodName == methodName &&
                       m.Timestamp >= startDate &&
                       m.Timestamp <= endDate);

        // Filtrování podle instance, pokud je zadána
        if (instanceId.HasValue)
        {
            query = query.Where(m => m.InstanceId == instanceId.Value);
        }

        // Seskupení podle dne
        var dailyData = await query
            .GroupBy(m => m.Timestamp.Date)
            .Select(g => new
            {
                Date = g.Key,
                Min = g.Average(m => m.Min),
                Max = g.Average(m => m.Max),
                Avg = g.Average(m => m.Avg),
                Median = g.Average(m => m.Median),
                Percentil95 = g.Average(m => m.Percentil95),
                Percentil99 = g.Average(m => m.Percentil99),
                StdDev = g.Average(m => m.StdDev),
                TotalCount = g.Sum(m => m.TotalCount),
                NonZeroCount = g.Sum(m => m.NonZeroCount)
            })
            .OrderBy(g => g.Date)
            .ToListAsync();

        // Příprava dat pro graf
        var labels = dailyData.Select(d => d.Date.ToString("dd.MM.yyyy")).ToList();
        var minData = dailyData.Select(d => d.Min).ToList();
        var maxData = dailyData.Select(d => d.Max).ToList();
        var avgData = dailyData.Select(d => d.Avg).ToList();
        var medianData = dailyData.Select(d => d.Median).ToList();
        var p95Data = dailyData.Select(d => d.Percentil95).ToList();
        var p99Data = dailyData.Select(d => d.Percentil99).ToList();
        var totalCountData = dailyData.Select(d => d.TotalCount).ToList();

        result["labels"] = labels;
        result["minData"] = minData;
        result["maxData"] = maxData;
        result["avgData"] = avgData;
        result["medianData"] = medianData;
        result["p95Data"] = p95Data;
        result["p99Data"] = p99Data;
        result["totalCountData"] = totalCountData;
        result["title"] = $"Výkon metody {className}.{methodName} v čase";

        // Získání celkových statistik
        var overallStats = await query
            .GroupBy(m => new { m.ClassName, m.MethodName })
            .Select(g => new
            {
                Min = g.Min(m => m.Min),
                Max = g.Max(m => m.Max),
                Avg = g.Average(m => m.Avg),
                Median = g.Average(m => m.Median),
                Percentil95 = g.Average(m => m.Percentil95),
                Percentil99 = g.Average(m => m.Percentil99),
                StdDev = g.Average(m => m.StdDev),
                TotalCount = g.Sum(m => m.TotalCount),
                NonZeroCount = g.Sum(m => m.NonZeroCount)
            })
            .FirstOrDefaultAsync();

        if (overallStats != null)
        {
            result["overallStats"] = overallStats;
        }

        return result;
    }

    /// <summary>
    /// Získá nejčastěji volané metody pro jednu nebo všechny instance
    /// </summary>
    /// <param name="instanceId">ID instance (volitelné)</param>
    /// <param name="fromDate">Počáteční datum</param>
    /// <param name="toDate">Koncové datum</param>
    /// <param name="limit">Počet metod k vrácení</param>
    /// <returns>Nejčastěji volané metody</returns>
    public async Task<List<object>> GetMostCalledMethodsAsync(
        int? instanceId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int limit = 10)
    {
        // Výchozí časové období - posledních 7 dní
        var startDate = fromDate ?? DateTime.UtcNow.AddDays(-7);
        var endDate = toDate ?? DateTime.UtcNow;

        // Získání dat z databáze
        var query = _context.PerformanceMetrics
            .Include(m => m.Instance)
            .ThenInclude(i => i.Customer)
            .Where(m => m.Timestamp >= startDate && m.Timestamp <= endDate);

        if (instanceId.HasValue)
        {
            query = query.Where(m => m.InstanceId == instanceId.Value);
        }

        // Seskupení podle metody a výpočet agregovaných hodnot
        var methodsData = await query
            .GroupBy(m => new { m.InstanceId, m.Instance.Name, m.Instance.Customer.Abbreviation, m.ClassName, m.MethodName })
            .Select(g => new
            {
                InstanceId = g.Key.InstanceId,
                InstanceName = $"{g.Key.Abbreviation} - {g.Key.Name}",
                ClassName = g.Key.ClassName,
                MethodName = g.Key.MethodName,
                FullMethodName = $"{g.Key.ClassName}.{g.Key.MethodName}",
                Min = g.Average(m => m.Min),
                Max = g.Average(m => m.Max),
                Avg = g.Average(m => m.Avg),
                Median = g.Average(m => m.Median),
                Percentil95 = g.Average(m => m.Percentil95),
                Percentil99 = g.Average(m => m.Percentil99),
                StdDev = g.Average(m => m.StdDev),
                TotalCount = g.Sum(m => m.TotalCount),
                NonZeroCount = g.Sum(m => m.NonZeroCount)
            })
            .OrderByDescending(g => g.TotalCount) // Řazení podle počtu volání
            .Take(limit)
            .ToListAsync();

        return methodsData.Cast<object>().ToList();
    }

    /// <summary>
    /// Získá seznam všech metod pro jednu nebo všechny instance
    /// </summary>
    /// <param name="instanceId">ID instance (volitelné)</param>
    /// <param name="fromDate">Počáteční datum</param>
    /// <param name="toDate">Koncové datum</param>
    /// <returns>Seznam metod</returns>
    public async Task<List<object>> GetAllMethodsAsync(
        int? instanceId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        // Výchozí časové období - posledních 7 dní
        var startDate = fromDate ?? DateTime.UtcNow.AddDays(-7);
        var endDate = toDate ?? DateTime.UtcNow;

        // Získání dat z databáze s explicitním joinem
        var query = from pm in _context.PerformanceMetrics
                    join instance in _context.DISInstances on pm.InstanceId equals instance.Id
                    join customer in _context.Customers on instance.CustomerId equals customer.Id
                    where pm.Timestamp >= startDate && pm.Timestamp <= endDate
                    select new
                    {
                        pm.InstanceId,
                        InstanceName = instance.Name,
                        CustomerAbbreviation = customer.Abbreviation,
                        pm.ClassName,
                        pm.MethodName,
                        pm.Min,
                        pm.Max,
                        pm.Avg,
                        pm.Median,
                        pm.Percentil95,
                        pm.Percentil99,
                        pm.StdDev,
                        pm.TotalCount,
                        pm.NonZeroCount
                    };

        if (instanceId.HasValue)
        {
            query = query.Where(m => m.InstanceId == instanceId.Value);
        }

        // Seskupení podle metody - nejprve bez string formátování
        var groupedData = await query
            .GroupBy(m => new { m.InstanceId, m.InstanceName, m.CustomerAbbreviation, m.ClassName, m.MethodName })
            .Select(g => new
            {
                InstanceId = g.Key.InstanceId,
                InstanceName = g.Key.InstanceName,
                CustomerAbbreviation = g.Key.CustomerAbbreviation,
                ClassName = g.Key.ClassName,
                MethodName = g.Key.MethodName,
                Min = g.Average(m => m.Min),
                Max = g.Average(m => m.Max),
                Avg = g.Average(m => m.Avg),
                Median = g.Average(m => m.Median),
                Percentil95 = g.Average(m => m.Percentil95),
                Percentil99 = g.Average(m => m.Percentil99),
                StdDev = g.Average(m => m.StdDev),
                TotalCount = g.Sum(m => m.TotalCount),
                NonZeroCount = g.Sum(m => m.NonZeroCount)
            })
            .ToListAsync();

        // Formátování stringů na klientské straně
        var methodsData = groupedData
            .Select(g => new
            {
                InstanceId = g.InstanceId,
                InstanceName = $"{g.CustomerAbbreviation} - {g.InstanceName}",
                ClassName = g.ClassName,
                MethodName = g.MethodName,
                FullMethodName = $"{g.ClassName}.{g.MethodName}",
                Min = g.Min,
                Max = g.Max,
                Avg = g.Avg,
                Median = g.Median,
                Percentil95 = g.Percentil95,
                Percentil99 = g.Percentil99,
                StdDev = g.StdDev,
                TotalCount = g.TotalCount,
                NonZeroCount = g.NonZeroCount
            })
            .OrderBy(g => g.InstanceName)
            .ThenBy(g => g.ClassName)
            .ThenBy(g => g.MethodName)
            .ToList();

        return methodsData.Cast<object>().ToList();
    }
}
