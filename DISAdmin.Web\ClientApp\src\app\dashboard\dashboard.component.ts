import { Component, OnInit, OnDestroy, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { ChartService } from '../services/chart.service';
import { AuthService } from '../services/auth.service';
import { AlertService } from '../services/alert.service';
import { DashboardConfigService } from '../services/dashboard-config.service';
import { ChartModalService } from '../services/chart-modal.service';
import { MonitoringService } from '../services/monitoring.service';
import { Chart, registerables } from 'chart.js';
import { Subscription, interval } from 'rxjs';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { DashboardConfig, DashboardWidget } from '../models/dashboard-config.model';
import { Router } from '@angular/router';

// Registrace všech komponent Chart.js
Chart.register(...registerables);

// Import Bootstrap pro popover
declare var bootstrap: any;

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('apiCallsChart') apiCallsChartRef!: ElementRef;
  @ViewChild('apiPerformanceChart') apiPerformanceChartRef!: ElementRef;
  @ViewChild('instancesUsageChart') instancesUsageChartRef!: ElementRef;
  @ViewChild('productionPerformanceChart') productionPerformanceChartRef!: ElementRef;
  @ViewChild('securityEventsChart') securityEventsChartRef!: ElementRef;

  loading = true;
  error: string | null = null;
  currentUser: any;
  isAdmin = false;
  alerts: any[] = [];
  systemStatistics: any = {};
  dashboardSummary: any = {};

  // Grafy
  apiCallsChart: Chart | null = null;
  apiPerformanceChart: Chart | null = null;
  instancesUsageChart: Chart | null = null;
  productionPerformanceChart: Chart | null = null;
  securityEventsChart: Chart | null = null;

  // Filtry
  selectedDays = 30;
  selectedInstanceId: number | null = null;

  // Dashboard konfigurace
  dashboardConfig: DashboardConfig | null = null;
  widgets: DashboardWidget[] = [];
  editMode = false;

  // Aktualizace dat
  private updateSubscription: Subscription | null = null;

  // Event listener pro zavírání popoverů
  private documentClickListener: ((event: Event) => void) | null = null;

  constructor(
    private chartService: ChartService,
    private authService: AuthService,
    private alertService: AlertService,
    private dashboardConfigService: DashboardConfigService,
    private chartModalService: ChartModalService,
    private monitoringService: MonitoringService,
    private router: Router
  ) {
    this.authService.currentUser.subscribe(user => {
      this.currentUser = user;
      this.isAdmin = user?.isAdmin || false;
    });

    // Načtení uložené hodnoty počtu dní z local storage
    const savedDateRange = localStorage.getItem('monitoring_dateRange');
    if (savedDateRange) {
      this.selectedDays = parseInt(savedDateRange, 10);
    }
  }

  ngOnInit(): void {
    // Načtení konfigurace dashboardu
    this.loadDashboardConfig();

    this.loadAlerts();
    this.loadSystemStatistics();
    this.loadDashboardSummary();
  }

  /**
   * Načte konfiguraci dashboardu pro aktuálního uživatele
   */
  loadDashboardConfig(): void {
    this.dashboardConfigService.getUserDashboardConfig().subscribe({
      next: (config) => {
        this.dashboardConfig = config;
        this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);
      },
      error: (error) => {
        console.error('Chyba při načítání konfigurace dashboardu', error);
        // Použijeme výchozí konfiguraci
        this.dashboardConfigService.resetDashboardConfig().subscribe(config => {
          this.dashboardConfig = config;
          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);
        });
      }
    });
  }

  ngAfterViewInit(): void {
    // Inicializace grafů po načtení view
    setTimeout(() => {
      this.initCharts();
      // Inicializace popoverů s delším zpožděním, aby se zajistilo, že DOM je plně načten
      setTimeout(() => {
        this.initPopovers();
      }, 300);
    }, 500);
  }

  ngOnDestroy(): void {
    // Zrušení subscription při zničení komponenty
    if (this.updateSubscription) {
      this.updateSubscription.unsubscribe();
    }

    // Odstranění globálního event listeneru
    if (this.documentClickListener) {
      document.removeEventListener('click', this.documentClickListener);
      this.documentClickListener = null;
    }

    // Zničení všech popoverů
    this.destroyPopovers();

    // Zničení grafů
    this.destroyCharts();
  }

  /**
   * Načtení upozornění
   */
  loadAlerts(): void {
    this.alertService.getDashboardAlerts().subscribe({
      next: (data) => {
        this.alerts = data.filter((alert: any) => !alert.isResolved);
      },
      error: (err) => {
        console.error('Chyba při načítání upozornění', err);
        this.error = 'Nepodařilo se načíst upozornění';
      }
    });
  }

  /**
   * Načtení systémových statistik
   */
  loadSystemStatistics(): void {
    this.monitoringService.getSystemStatistics().subscribe({
      next: (data) => {
        this.systemStatistics = data;
      },
      error: (err) => {
        console.error('Chyba při načítání systémových statistik', err);
        this.error = 'Nepodařilo se načíst systémové statistiky';
      }
    });
  }

  /**
   * Načtení dashboard summary dat
   */
  loadDashboardSummary(): void {
    this.monitoringService.getDashboardSummary().subscribe({
      next: (data) => {
        this.dashboardSummary = data;
      },
      error: (err) => {
        console.error('Chyba při načítání dashboard summary', err);
        this.error = 'Nepodařilo se načíst dashboard summary';
      }
    });
  }

  /**
   * Inicializace všech grafů
   */
  initCharts(): void {
    this.loading = true;

    // Načtení dat pro grafy
    this.loadApiCallsChart();
    this.loadApiPerformanceChart();
    this.loadInstancesUsageChart();
    this.loadProductionPerformanceChart();

    if (this.isAdmin) {
      this.loadSecurityEventsChart();
    }

    this.loading = false;
  }

  /**
   * Aktualizace všech grafů
   */
  refreshCharts(): void {
    this.loadApiCallsChart();
    this.loadApiPerformanceChart();
    this.loadInstancesUsageChart();
    this.loadProductionPerformanceChart();

    if (this.isAdmin) {
      this.loadSecurityEventsChart();
    }

    this.loadAlerts();
    this.loadSystemStatistics();
    this.loadDashboardSummary();

    // Reinicializace popoverů po aktualizaci dat
    setTimeout(() => {
      this.initPopovers();
    }, 100);
  }

  /**
   * Zničení všech grafů
   */
  destroyCharts(): void {
    if (this.apiCallsChart) {
      this.apiCallsChart.destroy();
      this.apiCallsChart = null;
    }

    if (this.apiPerformanceChart) {
      this.apiPerformanceChart.destroy();
      this.apiPerformanceChart = null;
    }

    if (this.instancesUsageChart) {
      this.instancesUsageChart.destroy();
      this.instancesUsageChart = null;
    }

    if (this.securityEventsChart) {
      this.securityEventsChart.destroy();
      this.securityEventsChart = null;
    }
  }

  /**
   * Načtení grafu API volání
   */
  loadApiCallsChart(): void {
    this.chartService.getApiCallsChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({
      next: (data) => {
        if (this.apiCallsChart) {
          this.apiCallsChart.data.labels = data.labels;
          this.apiCallsChart.data.datasets[0].data = data.data;
          this.apiCallsChart.update();
        } else if (this.apiCallsChartRef) {
          this.apiCallsChart = new Chart(this.apiCallsChartRef.nativeElement, {
            type: 'line',
            data: {
              labels: data.labels,
              datasets: [{
                label: 'Počet DISApi volání',
                data: data.data,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: data.title,
                  font: {
                    size: 16
                  }
                },
                legend: {
                  display: true,
                  position: 'top'
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Počet volání'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Datum'
                  }
                }
              }
            }
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf API volání', err);
        this.error = 'Nepodařilo se načíst data pro graf API volání';
      }
    });
  }

  /**
   * Načtení grafu výkonu API
   */
  loadApiPerformanceChart(): void {
    this.chartService.getApiPerformanceChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({
      next: (data) => {
        if (this.apiPerformanceChart) {
          this.apiPerformanceChart.data.labels = data.labels;
          this.apiPerformanceChart.data.datasets[0].data = data.avgData;
          this.apiPerformanceChart.data.datasets[1].data = data.maxData;
          this.apiPerformanceChart.update();
        } else if (this.apiPerformanceChartRef) {
          // Uložení originálních labels pro tooltip
          const originalLabels = [...data.labels];

          // Konfigurace pro zkrácené popisky na ose X
          const truncatedLabelsConfig = this.createTruncatedLabelsConfig(originalLabels, {
            enabled: true,        // Zapnout zkrácení popisků
            maxLength: 30,        // Maximální délka popisku
            showFullInTooltip: true // Zobrazit plný název v tooltip
          });

          // Základní konfigurace grafu
          const baseOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              title: {
                display: true,
                text: data.title,
                font: {
                  size: 16
                }
              },
              legend: {
                display: true,
                position: 'top'
              },
              tooltip: {
                mode: 'index',
                intersect: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'Doba odezvy (ms)'
                }
              },
              x: {
                title: {
                  display: true,
                  text: 'Endpoint'
                }
              }
            }
          };

          // Sloučení s konfigurací pro zkrácené popisky
          const finalOptions = this.mergeChartConfig(baseOptions, truncatedLabelsConfig);

          this.apiPerformanceChart = new Chart(this.apiPerformanceChartRef.nativeElement, {
            type: 'bar',
            data: {
              labels: data.labels,
              datasets: [
                {
                  label: 'Průměrná doba odezvy (ms)',
                  data: data.avgData,
                  backgroundColor: 'rgba(54, 162, 235, 0.5)',
                  borderColor: 'rgba(54, 162, 235, 1)',
                  borderWidth: 1
                },
                {
                  label: 'Maximální doba odezvy (ms)',
                  data: data.maxData,
                  backgroundColor: 'rgba(255, 99, 132, 0.5)',
                  borderColor: 'rgba(255, 99, 132, 1)',
                  borderWidth: 1
                }
              ]
            },
            options: finalOptions
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf výkonu API', err);
        this.error = 'Nepodařilo se načíst data pro graf výkonu API';
      }
    });
  }

  /**
   * Načtení grafu využití instancí
   */
  loadInstancesUsageChart(): void {
    this.chartService.getInstancesUsageChartData(this.selectedDays).subscribe({
      next: (data) => {
        if (this.instancesUsageChart) {
          this.instancesUsageChart.data.labels = data.labels;
          this.instancesUsageChart.data.datasets[0].data = data.data;
          this.instancesUsageChart.update();
        } else if (this.instancesUsageChartRef) {
          this.instancesUsageChart = new Chart(this.instancesUsageChartRef.nativeElement, {
            type: 'bar',
            data: {
              labels: data.labels,
              datasets: [{
                label: 'Počet volání DIS metod',
                data: data.data,
                backgroundColor: [
                  'rgba(255, 99, 132, 0.5)',
                  'rgba(54, 162, 235, 0.5)',
                  'rgba(255, 206, 86, 0.5)',
                  'rgba(75, 192, 192, 0.5)',
                  'rgba(153, 102, 255, 0.5)',
                  'rgba(255, 159, 64, 0.5)',
                  'rgba(199, 199, 199, 0.5)',
                  'rgba(83, 102, 255, 0.5)',
                  'rgba(255, 99, 255, 0.5)',
                  'rgba(99, 255, 132, 0.5)'
                ],
                borderColor: [
                  'rgba(255, 99, 132, 1)',
                  'rgba(54, 162, 235, 1)',
                  'rgba(255, 206, 86, 1)',
                  'rgba(75, 192, 192, 1)',
                  'rgba(153, 102, 255, 1)',
                  'rgba(255, 159, 64, 1)',
                  'rgba(199, 199, 199, 1)',
                  'rgba(83, 102, 255, 1)',
                  'rgba(255, 99, 255, 1)',
                  'rgba(99, 255, 132, 1)'
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: data.title,
                  font: {
                    size: 16
                  }
                },
                legend: {
                  display: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Počet volání'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Instance'
                  }
                }
              }
            }
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf využití instancí', err);
        this.error = 'Nepodařilo se načíst data pro graf využití instancí';
      }
    });
  }

  /**
   * Načtení grafu výkonu produkčních instancí
   */
  loadProductionPerformanceChart(): void {
    this.chartService.getProductionPerformanceChartData(this.selectedDays).subscribe({
      next: (data) => {
        if (this.productionPerformanceChart) {
          this.productionPerformanceChart.data.labels = data.labels;
          this.productionPerformanceChart.data.datasets = data.datasets;
          this.productionPerformanceChart.update();
        } else if (this.productionPerformanceChartRef) {
          this.productionPerformanceChart = new Chart(this.productionPerformanceChartRef.nativeElement, {
            type: 'line',
            data: {
              labels: data.labels,
              datasets: data.datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: data.title,
                  font: {
                    size: 16
                  }
                },
                legend: {
                  display: true,
                  position: 'top'
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Doba odezvy (ms)'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Datum'
                  }
                }
              }
            }
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf výkonu produkčních instancí', err);
        this.error = 'Nepodařilo se načíst data pro graf výkonu produkčních instancí';
      }
    });
  }

  /**
   * Načtení grafu bezpečnostních událostí
   */
  loadSecurityEventsChart(): void {
    this.chartService.getSecurityEventsChartData(this.selectedDays).subscribe({
      next: (data) => {
        if (this.securityEventsChart) {
          this.securityEventsChart.data.labels = data.labels;
          this.securityEventsChart.data.datasets = data.datasets;
          this.securityEventsChart.update();
        } else if (this.securityEventsChartRef) {
          this.securityEventsChart = new Chart(this.securityEventsChartRef.nativeElement, {
            type: 'line',
            data: {
              labels: data.labels,
              datasets: data.datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: data.title,
                  font: {
                    size: 16
                  }
                },
                legend: {
                  display: true,
                  position: 'top'
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Počet událostí'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Datum'
                  }
                }
              }
            }
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf bezpečnostních událostí', err);
        this.error = 'Nepodařilo se načíst data pro graf bezpečnostních událostí';
      }
    });
  }

  /**
   * Změna filtru dnů
   */
  onDaysChange(days: number): void {
    this.selectedDays = days;

    // Uložení vybraného počtu dní do local storage
    localStorage.setItem('monitoring_dateRange', days.toString());

    this.refreshCharts();
  }

  /**
   * Změna filtru instance
   */
  onInstanceChange(instanceId: number | null): void {
    this.selectedInstanceId = instanceId;
    this.refreshCharts();
  }



  /**
   * Získání textu pro typ alertu
   */
  getAlertTypeText(alertType: string): string {
    switch (alertType) {
      case 'SecurityBreach':
        return 'Bezpečnostní incident';
      case 'CertificateExpiration':
        return 'Expirace certifikátu';
      case 'FailedConnectionAttempts':
        return 'Selhané připojení';
      case 'SuspiciousActivity':
        return 'Podezřelá aktivita';
      default:
        return alertType;
    }
  }

  /**
   * Přepnutí režimu úprav dashboardu
   */
  toggleEditMode(): void {
    this.editMode = !this.editMode;
  }

  /**
   * Zpracování přetažení widgetu
   */
  onDrop(event: CdkDragDrop<DashboardWidget[]>): void {
    if (event.previousIndex === event.currentIndex) {
      return;
    }

    // Získáme všechny viditelné widgety v pořadí, jak jsou zobrazeny v DOM
    const visibleWidgets = Array.from(document.querySelectorAll('.col-lg-6[cdkDrag]'))
      .map(element => {
        // Získáme ID widgetu z atributu *ngIf
        const widget = this.widgets.find(w => {
          const widgetId = w.id;
          return element.innerHTML.includes(`isWidgetVisible('${widgetId}')`);
        });
        return widget;
      })
      .filter(widget => widget !== undefined) as DashboardWidget[];

    // Provedeme přesun v poli viditelných widgetů
    moveItemInArray(visibleWidgets, event.previousIndex, event.currentIndex);

    // Aktualizujeme pozice všech widgetů
    let position = 0;

    // Nejprve aktualizujeme pozice viditelných widgetů
    visibleWidgets.forEach(visibleWidget => {
      const widget = this.widgets.find(w => w.id === visibleWidget.id);
      if (widget) {
        widget.position = position++;
      }
    });

    // Poté aktualizujeme pozice skrytých widgetů
    this.widgets
      .filter(widget => !visibleWidgets.some(vw => vw.id === widget.id))
      .forEach(widget => {
        widget.position = position++;
      });

    // Seřadíme widgety podle pozice
    this.widgets.sort((a, b) => a.position - b.position);

    // Uložení konfigurace
    this.saveDashboardConfig();
  }

  /**
   * Změna viditelnosti widgetu
   */
  toggleWidgetVisibility(widgetId: string): void {
    const widget = this.widgets.find(w => w.id === widgetId);
    if (widget) {
      widget.visible = !widget.visible;
      this.saveDashboardConfig();
    }
  }

  /**
   * Kontrola viditelnosti widgetu
   */
  isWidgetVisible(widgetId: string): boolean {
    const widget = this.widgets.find(w => w.id === widgetId);
    return widget ? widget.visible : true;
  }

  /**
   * Uložení konfigurace dashboardu
   */
  saveDashboardConfig(): void {
    if (!this.dashboardConfig) {
      return;
    }

    this.dashboardConfig.widgets = [...this.widgets];
    this.dashboardConfig.lastModified = new Date();

    this.dashboardConfigService.saveDashboardConfig(this.dashboardConfig).subscribe({
      next: (config) => {
        console.log('Konfigurace dashboardu byla úspěšně uložena');
      },
      error: (error) => {
        console.error('Chyba při ukládání konfigurace dashboardu', error);
      }
    });
  }

  /**
   * Reset konfigurace dashboardu na výchozí hodnoty
   */
  resetDashboardConfig(): void {
    if (confirm('Opravdu chcete resetovat dashboard na výchozí nastavení?')) {
      this.dashboardConfigService.resetDashboardConfig().subscribe({
        next: (config) => {
          this.dashboardConfig = config;
          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);
          this.editMode = false;
          this.refreshCharts();
        },
        error: (error) => {
          console.error('Chyba při resetování konfigurace dashboardu', error);
        }
      });
    }
  }

  /**
   * Otevře modální okno s grafem v režimu "full screen"
   * @param chart Instance grafu nebo reference na canvas element
   * @param title Titulek grafu
   */
  openFullscreenChart(chart: Chart | HTMLCanvasElement | null, title: string): void {
    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart
    if (chart instanceof HTMLCanvasElement) {
      // Najdeme instanci Chart pro daný canvas element
      const chartInstance = Chart.getChart(chart);
      this.chartModalService.openChartModal(chartInstance || null, title);
    } else {
      this.chartModalService.openChartModal(chart, title);
    }
  }

  /**
   * Inicializace popoverů pro nápovědu
   */
  private initPopovers(): void {
    // Nejprve zničíme existující popovers
    this.destroyPopovers();

    // Definice obsahu nápověd
    const helpContent: Record<string, string> = {
      'api-performance': 'Graf zobrazuje top 10 nejpomalejších metod seřazených podle 95. percentilu doby odezvy. ' +
        'Pro každou metodu jsou zobrazeny dva sloupce: průměrná doba odezvy (modrý sloupec) a maximální doba odezvy (červený sloupec). ' +
        'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',
      'api-calls': 'Graf zobrazuje počet DISApi volání v čase pro vybrané období. ' +
        'Zobrazuje volání z DIS aplikací na DISAdmin.DISApi službu. ' +
        'Data jsou získávána z tabulky ActivityLogs za zvolené období.',
      'instances-usage': 'Graf zobrazuje celkový počet volání DIS metod podle instancí. ' +
        'Ukazuje, které instance jsou nejvíce zatížené podle skutečného počtu volání metod. ' +
        'Data jsou získávána z tabulky PerformanceMetrics jako suma TotalCount za zvolené období.',
      'production-performance': 'Graf zobrazuje průměrnou dobu odezvy a 95. percentil pouze pro produkční instance. ' +
        'Produkční instance jsou kritické pro běžný provoz a jejich výkon je klíčový pro uživatelskou spokojenost. ' +
        '95. percentil ukazuje, že 95% všech volání bylo rychlejších než zobrazená hodnota, což je lepší indikátor než prostý průměr. ' +
        'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',
      'security-events': 'Graf zobrazuje počet bezpečnostních událostí v čase. ' +
        'Zahrnuje události jako neúspěšné pokusy o přihlášení, podezřelé aktivity a porušení bezpečnosti. ' +
        'Data jsou získávána z tabulky SecurityLogs za zvolené období.',
      'system-info': 'Tento widget zobrazuje souhrnné systémové informace včetně počtu aktivních upozornění, ' +
        'dostupnosti API, průměrné odezvy DIS metod a počtu expirujících certifikátů. ' +
        'Poskytuje rychlý přehled o celkovém stavu systému.',
      // Nápovědy pro karty systémových informací
      'alerts-count': 'Počet aktivních upozornění v systému. Upozornění jsou automaticky generována při detekci problémů s výkonem, ' +
        'bezpečností nebo dostupností služeb. Kliknutím přejdete na modul Alerty a upozornění.',
      'security-events-count': 'Počet nevyřešených bezpečnostních událostí včetně neúspěšných pokusů o přihlášení, ' +
        'podezřelých aktivit a narušení bezpečnosti. Kliknutím přejdete na modul Bezpečnostní události.',
      'api-availability': 'Procentuální dostupnost DIS API za posledních 24 hodin. Počítá se jako poměr úspěšných volání ' +
        'k celkovému počtu volání API. Data jsou získávána z tabulky ActivityLogs. Kliknutím přejdete na modul Monitoring.',
      'api-response-time': 'Průměrná doba odezvy DIS API metod v milisekundách za posledních 24 hodin. ' +
        'Nižší hodnota znamená lepší výkon systému. Data jsou získávána z tabulky PerformanceMetrics. ' +
        'Kliknutím přejdete na modul Výkon DIS.',
      'expiring-certificates': 'Počet certifikátů, které vyprší během následujících 30 dnů. ' +
        'Včasná obnova certifikátů je kritická pro bezpečnost a nepřerušený provoz systému. ' +
        'Kliknutím přejdete na modul Správa certifikátů.'
    };

    // Nejprve zrušíme všechny existující popovery
    document.querySelectorAll('[data-bs-toggle="popover"]').forEach(el => {
      const popover = bootstrap.Popover.getInstance(el);
      if (popover) {
        popover.dispose();
      }
    });

    // Inicializace popoverů pomocí Bootstrap API
    document.querySelectorAll('[data-bs-toggle="popover"]').forEach(el => {
      const helpType = el.getAttribute('data-help-type');
      //console.log('Initializing popover for element with help-type:', helpType);

      if (helpType && helpType in helpContent) {
        try {
          const popover = new bootstrap.Popover(el, {
            content: helpContent[helpType as keyof typeof helpContent],
            html: true,
            trigger: 'hover focus',
            placement: 'top',
            container: 'body',
            sanitize: false
          });

          // Přidáme event listener pro kliknutí na klikatelné karty
          if (el.classList.contains('clickable-card') || el.closest('.clickable-card')) {
            el.addEventListener('click', () => {
              popover.hide();
            });
          }
        } catch (error) {
          console.error('Error initializing popover:', error);
        }
      } else if (helpType) {
        console.warn('Help content not found for type:', helpType);
      }
    });

    // Přidáme globální event listener pro zavření popoverů při kliknutí kamkoliv
    this.documentClickListener = (event) => {
      const target = event.target as HTMLElement;
      // Pokud se nekliklo na element s popoverem nebo na samotný popover, zavřeme všechny popovers
      if (!target.closest('[data-bs-toggle="popover"]') && !target.closest('.popover')) {
        this.hideAllPopovers();
      }
    };
    document.addEventListener('click', this.documentClickListener);
  }

  /**
   * Zavře všechny aktivní popovers
   */
  private hideAllPopovers(): void {
    document.querySelectorAll('[data-bs-toggle="popover"]').forEach(el => {
      const popoverInstance = bootstrap.Popover.getInstance(el);
      if (popoverInstance) {
        popoverInstance.hide();
      }
    });
  }

  /**
   * Zničí všechny popovers
   */
  private destroyPopovers(): void {
    document.querySelectorAll('[data-bs-toggle="popover"]').forEach(el => {
      const popoverInstance = bootstrap.Popover.getInstance(el);
      if (popoverInstance) {
        popoverInstance.dispose();
      }
    });
  }

  /**
   * Navigace na modul Alerty a upozornění
   */
  navigateToAlerts(): void {
    this.hideAllPopovers();
    this.router.navigate(['/alerts']);
  }

  /**
   * Navigace na modul Bezpečnostní události
   */
  navigateToSecurityEvents(): void {
    this.hideAllPopovers();
    this.router.navigate(['/security']);
  }

  /**
   * Navigace na modul Certifikáty
   */
  navigateToCertificates(): void {
    this.hideAllPopovers();
    this.router.navigate(['/certificates']);
  }

  /**
   * Navigace na modul Monitoring
   */
  navigateToMonitoring(): void {
    this.hideAllPopovers();
    this.router.navigate(['/monitoring']);
  }

  /**
   * Navigace na modul Výkon DIS
   */
  navigateToPerformance(): void {
    this.hideAllPopovers();
    this.router.navigate(['/performance']);
  }

  /**
   * Inteligentní zkrácení názvu metody pro zobrazení v grafu
   * @param text Text k zkrácení
   * @param maxLength Maximální délka včetně "..." (výchozí 30 znaků)
   * @returns Zkrácený text s "..." pokud je potřeba, celková délka nepřekročí maxLength
   */
  private truncateMethodName(text: string, maxLength: number = 30): string {
    if (!text || text.length <= maxLength) {
      return text;
    }

    // Zkrátíme na maxLength - 3 znaků, aby se vešly "..."
    const targetLength = maxLength - 3;
    if (targetLength <= 0) {
      return '...';
    }

    // Pokusíme se zkrátit na posledním "." před limitem pro zachování čitelnosti
    const truncated = text.substring(0, targetLength);
    const lastDotIndex = truncated.lastIndexOf('.');

    // Pokud najdeme tečku a není příliš na začátku (alespoň 40% z cílové délky), zkrátíme na ní
    if (lastDotIndex > 0 && lastDotIndex >= targetLength * 0.4) {
      return text.substring(0, lastDotIndex) + '...';
    }

    // Jinak zkrátíme na pevné délce
    return truncated + '...';
  }

  /**
   * Vytvoří konfiguraci pro zkrácení popisků na ose X
   * @param originalLabels Originální popisky
   * @param options Možnosti konfigurace
   * @returns Objekt s konfigurací pro Chart.js
   */
  private createTruncatedLabelsConfig(originalLabels: string[], options: {
    enabled?: boolean;
    maxLength?: number;
    showFullInTooltip?: boolean;
  } = {}) {
    const config = {
      enabled: options.enabled ?? true,
      maxLength: options.maxLength ?? 30,
      showFullInTooltip: options.showFullInTooltip ?? true
    };

    if (!config.enabled) {
      return {};
    }

    const result: any = {
      scales: {
        x: {
          ticks: {
            callback: (value: any, index: number) => {
              const label = originalLabels[index];
              return this.truncateMethodName(label, config.maxLength);
            }
          }
        }
      }
    };

    if (config.showFullInTooltip) {
      result.plugins = {
        tooltip: {
          callbacks: {
            title: (context: any) => {
              if (context && context.length > 0) {
                const dataIndex = context[0].dataIndex;
                const fullLabel = originalLabels[dataIndex];
                return fullLabel || context[0].label;
              }
              return context[0]?.label || '';
            }
          }
        }
      };
    }

    return result;
  }

  /**
   * Sloučí konfiguraci pro zkrácené popisky s existující konfigurací grafu
   * @param baseConfig Základní konfigurace grafu
   * @param truncatedConfig Konfigurace pro zkrácené popisky
   * @returns Sloučená konfigurace
   */
  private mergeChartConfig(baseConfig: any, truncatedConfig: any): any {
    if (!truncatedConfig || Object.keys(truncatedConfig).length === 0) {
      return baseConfig;
    }

    const merged = { ...baseConfig };

    // Sloučení scales
    if (truncatedConfig.scales) {
      merged.scales = merged.scales || {};
      if (truncatedConfig.scales.x) {
        merged.scales.x = merged.scales.x || {};
        merged.scales.x.ticks = {
          ...merged.scales.x.ticks,
          ...truncatedConfig.scales.x.ticks
        };
      }
    }

    // Sloučení plugins
    if (truncatedConfig.plugins) {
      merged.plugins = merged.plugins || {};
      if (truncatedConfig.plugins.tooltip) {
        merged.plugins.tooltip = merged.plugins.tooltip || {};
        merged.plugins.tooltip.callbacks = merged.plugins.tooltip.callbacks || {};

        // Sloučení tooltip callbacks - zachováme existující a přidáme nové
        Object.keys(truncatedConfig.plugins.tooltip.callbacks).forEach(key => {
          merged.plugins.tooltip.callbacks[key] = truncatedConfig.plugins.tooltip.callbacks[key];
        });
      }
    }

    return merged;
  }
}
